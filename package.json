{"name": "innit", "version": "0.1.0", "private": true, "scripts": {"dev": "nuxt dev --port 9008", "build": "nuxt build", "serve": "nuxt build && PORT=8080 node --require=elastic-apm-node/start.js --experimental-loader=elastic-apm-node/loader.mjs .output/server/index.mjs", "start": "node --require=elastic-apm-node/start.js --experimental-loader=elastic-apm-node/loader.mjs .output/server/index.mjs", "icons": "vsvg -s src/assets/svg -t src/assets/svg/sprite-svg", "doc": "jsdoc -c jsDoc.json"}, "type": "module", "dependencies": {"@auth0/auth0-vue": "^2.3.3", "@braintree/sanitize-url": "^7.0.4", "@elastic/apm-rum": "^5.16.3", "@elastic/apm-rum-vue": "^2.1.9", "@microsoft/fetch-event-source": "^2.0.1", "@nuxtjs/dotenv": "^1.4.2", "@pinia/nuxt": "^0.9.0", "@vue/runtime-core": "^3.5.13", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/components": "^13.1.0", "@vueuse/core": "^12.0.0", "@vueuse/nuxt": "^12.0.0", "axios": "^1.8.2", "config": "^3.3.12", "elastic-apm-node": "^4.11.0", "express": "^4.21.2", "file-saver": "^2.0.5", "h3": "^1.13.0", "install": "^0.13.0", "json-bigint": "^1.0.0", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "logrocket": "^9.0.0", "mitt": "^3.0.1", "mixpanel-browser": "^2.56.0", "nuxt": "^3.17.3", "nuxt-svgo": "^4.2.1", "papaparse": "^5.4.1", "query-string": "^9.1.1", "rollup": "^4.32.1", "v-lazy-image": "^2.1.1", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-i18n": "^10.0.6", "vue-router": "^4.5.0", "vuejs-paginate-next": "^1.0.2", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"nuxi": "^3.16.0", "sass": "^1.83.0", "video.js": "^8.21.0"}}