import { ref, computed } from 'vue';
import { PROJECT_PERMISSIONS } from '@/сonstants/project-permissions';
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { useInnitAuth } from "./useInnitAuth.js";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useConfig } from "./useConfig.js";

export const useProjectLang = () => {
  const isAdminValue = ref(false);
  const app = useNuxtApp();
  const store = useStore();
  const router = useRouter();
  const auth = useInnitAuth();
  const { isInnitAdmin } = useInnitAuthStore();
  const { config } = useConfig();

  /**
   * @deprecated Use authorizationToken from useInnitAuthStore or getAuthorizationTokenAsync from useAuth
   * @type {ComputedRef<any>}
   */
  const getToken = computed(async () => {
    const token = await auth.getAuthorizationTokenAsync();
    return token;
  });

  /**
   * @deprecated Use isInnitAdmin from useInnitAuthStore
   * @type {ComputedRef<Promise<boolean|*>>}
   */
  const isAdmin = computed(() => {
    return isInnitAdmin.value;
  });

  const projectPermissions = computed(() => {
    const permissions = store.getters['userData/getProjectPermissions'];
    return permissions ? [...permissions] : null;
  });

  const hasContentPermission = computed(async () => {
    return isInnitAdmin.value || hasPermission(PROJECT_PERMISSIONS.MANAGE_CONTENT);
  });

  const isProjectReady = computed(() => {
    return !!store.getters['userData/getProject']?.id;
  });

  const readyProject = (callback) => {
    if (config.value.IS_SERVER) {
      return;
    }

    if (typeof callback !== 'function') {
      throw new Error('Only accepts a function');
    }

    const project = store.getters['userData/getProject'];
    if (project) {
      callback({
        id: project?.id,
        displayName: project?.displayName,
        isProjectReady: !!project?.id,
      });
      return;
    }

    const unwatch = store.watch(
      (state, getters) => getters['userData/getProject'],
      (newProjectData) => {
        if (!newProjectData) return;

        callback({
          id: newProjectData.id,
          displayName: newProjectData.displayName,
          isProjectReady: !!newProjectData.id,
        });
        unwatch();
      }
    );
  };

  const checkPermissions = async () => {
    isAdminValue.value = await isAdmin.value;
  };
  const getProject = async () => {
    if (!store.getters['userData/getProject']) {
      await store.dispatch('userData/fetchProjectsAsync', { isAdmin: isAdmin, isHotRefresh: true });
    }
    return store.getters['userData/getProject'];
  };

  const getProjectList = async () => {
    if (!store.getters['userData/getProjectList']) {
      await store.dispatch('userData/fetchProjectsAsync', { isAdmin: isAdmin, isHotRefresh: true });
    }
    return store.getters['userData/getProjectList'];
  };

  const switchProject = async (project) => {
    await store.dispatch('userData/setProject', project);
    await Promise.all([
      store.dispatch('userData/fetchLangsAsync', { isHotRefresh: true }),
      store.dispatch('userData/fetchUserPermissionsAsync', { isHotRefresh: true }),
      store.dispatch('config/fetchFeaturesAsync', { isHotRefresh: true }),
    ]).catch(async (error) => {
      await findProjectWithPermissionsAsync(error);
    });
  };

  const findProjectWithPermissionsAsync = async (error) => {
    if (error.response?.status === 403 && error.response?.config?.url.includes('projects/permissions')) {
      const project = store.getters['userData/getProject'];
      const projectList = store.getters['userData/getProjectList'];
      const filteredProjects = projectList.filter(item => item?.id !== project?.id);
      const nextProject = filteredProjects[0];

      if (!nextProject?.id) {
        await store.dispatch('userData/setProject', { id: undefined, displayName: undefined });
        await store.dispatch('userData/setProjectList', filteredProjects);
        await router.push('/create-project');
        return;
      }

      await switchProject({ id: nextProject.id, displayName: nextProject.displayName });
      await store.dispatch('userData/setProjectList', filteredProjects);
    }
  };

  const getDefaultLang = async () => {
    if (!store.getters['userData/getDefaultLang']) {
      await _setLangs();
    }
    return store.getters['userData/getDefaultLang'];
  };

  const getAvailableLangs = async () => {
    if (!store.getters['userData/getAvailableLangs']) {
      await _setLangs();
    }
    return store.getters['userData/getAvailableLangs'];
  };

  const _setLangs = async () => {
    const project = store.getters['userData/getProject'];
    try {
      const response = await WebBackendService.getLangConfig(project, store, app?.$auth);
      store.dispatch('userData/setLangs', response);
    } catch {
      store.dispatch('userData/setLangs', {});
    }
  };

  const hasPermission = (permission) => {
    return projectPermissions.value === null ? false : projectPermissions.value.includes(permission);
  };

  return {
    getToken,
    isAdmin,
    projectPermissions,
    hasContentPermission,
    isProjectReady,
    readyProject,
    getProject,
    getProjectList,
    switchProject,
    findProjectWithPermissionsAsync,
    getDefaultLang,
    getAvailableLangs,
    hasPermission,
    isAdminValue,
    checkPermissions,
  };
};
