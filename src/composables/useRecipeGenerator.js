import { computed, watch } from "vue";
import { useStore } from "vuex";
import {
  CONTENT_GENERATION_TYPE,
  getStreamMessageForAdvanceOutput,
  RECIPE_GENERATION_FLAG,
} from "@/models/recipe-generator.model";
import { STATUS_CODE_MESSAGES } from "@/сonstants/status-code-messages";
import { KEYS } from "@/сonstants/keys";

export function useRecipeGenerator() {
  const { triggerLoading } = useCommonUtils();
  const store = useStore();

  const isLoading = computed(() => {
    const isGenerating = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING);
    const isImageRefreshing = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING);
    const isRecipeModifying = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING);
    const isSaving = store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_SAVING);
    return isGenerating || isImageRefreshing || isRecipeModifying || isSaving;
  });

  const isGenerating = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATING));
  const isImageRefreshing = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_IMAGE_REFRESHING));
  const isRecipeModifying = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_RECIPE_MODIFYING));
  const isSaving = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_SAVING));
  const isGenerationComplete = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATION_COMPLETE));
  const isGeneratedCorrectly = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.IS_GENERATED_CORRECTLY));
  const flow = computed(() => store.getters["recipeGeneration/getFlag"](RECIPE_GENERATION_FLAG.FLOW));
  const resetAt = computed(() => store.getters["recipeGeneration/getResetAt"]);

  watch(isLoading, handleCampaignModified);

  function setStreamText(text) {
    store.dispatch('recipeGeneration/setStreamText', { value: text }).catch();
  }

  function handleCampaignModified(newValue, oldValue) {
    if (newValue !== oldValue) {
      // Assuming `triggerLoading` is a utility method from the commonUtils mixin
      triggerLoading(KEYS.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
    }
  }

  function setOnMessageProgressSteps(type, step, result) {
    store.dispatch('recipeGeneration/setGenerationProgressSteps', {
      type: type,
      step: step,
      result: type === CONTENT_GENERATION_TYPE.PROGRESS ? result?.progress : "",
      isStreamOutput: true,
      isHidden: false,
      reason: type === CONTENT_GENERATION_TYPE.FAILURE ? result?.reason : undefined,
    }).catch();
  }

  function setInnerProgressSteps(type, step) {
    store.dispatch('recipeGeneration/setGenerationProgressSteps', {
      type: type,
      step: step,
      isStreamOutput: false,
      isDone: true,
      isHidden: false,
    }).catch();
  }

  function clearTimer(timer) {
    if (timer) {
      clearTimeout(timer);
    }
  }

  function abortConnection(ctrl) {
    if (ctrl) {
      ctrl.abort();
    }
  }

  function setTraceId(response) {
    const id = response?.headers?.get("X-Forensic-Id") || response?.headers?.get("x-forensic-id");
    if (!id) {
      return;
    }

    store.dispatch("recipeGeneration/setTraceId", { value: id }).catch();
    const pageContainerEl = document?.querySelector(".iq-r-g");
    pageContainerEl?.setAttribute('data-trace-id', id);
  }

  function handleStreamError(response, { isStreamOnOpen, isEventFatalError, isStreamOnError, progressMessage }) {
    if (isStreamOnOpen) {
      let progress;
      if (response.status >= 400 && response.status < 500 && response.status !== 429) {
        progress = "# Client-side errors";
      } else if (response.status >= 500) {
        progress = "# Server-side errors";
      }

      if (progress) {
        const id = response?.headers?.get("X-Forensic-Id") || response?.headers?.get("x-forensic-id");
        setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, { progress }));
        setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
          status: response.status,
          "Trace ID": id,
          detail: STATUS_CODE_MESSAGES[response.status],
        }));
      }
      return;
    }

    if (isEventFatalError) {
      setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, {
        progress: progressMessage ? `# ${progressMessage}` : "# Stream event error",
      }));
      setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
        "Trace ID": store.getters["recipeGeneration/getTraceId"],
        detail: response,
      }));
      return;
    }

    if (isStreamOnError) {
      setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.PROGRESS, {
        progress: progressMessage ? `# ${progressMessage}` : "# Stream on error.",
      }));
      setStreamText(getStreamMessageForAdvanceOutput(CONTENT_GENERATION_TYPE.FAILURE, {
        "Trace ID": store.getters["recipeGeneration/getTraceId"],
        detail: response,
        message: response.message || response.errorMessage,
      }));
    }
  }

  function scrollToPageBottom() {
    window.scrollTo({
      top: document.body.scrollHeight,
      left: 0,
      behavior: "smooth",
    });
  }

  return {
    isLoading,
    isGenerating,
    isImageRefreshing,
    isRecipeModifying,
    isSaving,
    isGenerationComplete,
    isGeneratedCorrectly,
    flow,
    resetAt,
    setStreamText,
    setOnMessageProgressSteps,
    setInnerProgressSteps,
    clearTimer,
    abortConnection,
    setTraceId,
    handleStreamError,
    scrollToPageBottom,
  };
}
