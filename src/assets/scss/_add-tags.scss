.top-section {
  .back-button-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 18px;

    .tag-back-button {
      display: flex;
      justify-content: space-between;
      position: relative;
      top: 15px;
      margin: 20px;
      height: 20px;
      cursor: pointer;

      img {
        height: 12px;
        width: 14px;
        margin: 5px;
        cursor: pointer;
      }

      .back-button-text {
        color: $green;
        font-family: $font-family-averta;
      }
    }

    .tag-button-section {
      margin: 20px;
      display: flex;
      gap: 20px;
    }
  }

  .edit-tag-isin {
    color: $gunmetal-grey;
    position: relative;
    left: 32px;
    bottom: 6px;
  }
}

.tag-containers {
  padding-left: 26px;
  padding-right: 30px;

  .right-section {
    position: relative;
    width: 200px;
    padding-bottom: 5px;
    padding-left: 62px;

    .published {
      opacity: 0.5;
      pointer-events: none;
    }

    .publish-btn {
      width: 130px;

      .text {
        position: relative;
        left: 14px;
        top: 4px;
        font-family: $font-family-averta;
        color: $black;
      }

      .switch {
        position: relative;
        display: inline-block;
        width: 42px;
        height: 26px;
        margin-left: 20px;

        input {
          opacity: 0;
          width: 0;
          height: 0;
        }
      }

      .slider-round {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $light-white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        border-radius: 30px;

        &:before {
          position: absolute;
          content: "";
          height: 23px;
          width: 23px;
          left: 2px;
          bottom: 2px;
          background-color: $white;
          -webkit-transition: 0.4s;
          transition: 0.4s;
          border-radius: 50%;
        }
      }

      input {
        &:checked {
          + {
            .slider-round {
              background-color: $green;

              &:before {
                -webkit-transform: translateX(15px);
                -ms-transform: translateX(15px);
                transform: translateX(15px);
              }
            }
          }
        }

        &:focus {
          + {
            .slider-round {
              box-shadow: 0 0 1px $green;
            }
          }
        }
      }
    }

    .delete-btn {
      position: absolute;
      bottom: 16px;
      right: 0px;
      min-width: 130px;

      .image {
        cursor: pointer;
        width: 14px;
        height: 16px;
      }

      .text {
        cursor: pointer;
        position: relative;
        top: 1px;
        font-family: $font-family-averta;
        font-weight: 700;
        font-size: 14px;
        margin-left: 6px;
        color: $fiery-red-blaze;
      }
    }
  }

  .tag-content-mains {
    background: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    padding: 20px;
    width: 100%;
    margin-bottom: 20px;

    .tag-form-titles {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid $sliver;
      padding-bottom: 1px;

      .tag-form-texts {
        display: flex;
        font-size: 20px;
        font-weight: 700;
        color: $black;
        padding: 5px;
        padding-bottom: 8px;
        width: 20%;
        font-family: $font-family-averta;
      }
    }

    .add-title-container {
      padding: 5px;
      padding-bottom: 10px;
      margin-top: 20px;

      .compulsory-feild-tags {
        width: 9px;
        height: 10px;
        cursor: default;
        position: absolute;
        left: 128px;
        bottom: 33px;
      }

      .add-title-text {
        color: $black;
        font-family: $font-family-averta;
        width: 100%;
        border: none;
        background: $transparent;
        cursor: text;
      }

      .edit-title-text {
        color: $black;
        font-family: $font-family-averta;
        width: 88%;
        border: none;
        background: $transparent;
        cursor: text;
        text-overflow: ellipsis;
      }
    }

    .tag-variant-section {
      .tag-variants-main {
        display: flex;
        justify-content: space-between;
        margin-top: 26px;

        .add-variant-section {
          position: relative;

          .add-variant-main {
            display: flex;
            position: relative;
            right: 8px;
            cursor: pointer;

            .add-variant-btn {
              height: 18px;
              width: 18px;
              margin-top: -1px;
            }

            .add-variant-text {
              text-transform: uppercase;
              color: $green-light;
              position: relative;
              top: 2px;
              left: 5px;
            }
            .disable-add-variant-main {
              opacity: 0.5;
            }
          }
        }
        .tag-variants {
          font-weight: 400;
          font-size: 20px;
          color: $black;
          display: flex;
        }
        .tag-variant-tooltip-section {
          display: flex;
          justify-content: flex-start;
        }
        .tooltip-main-container-for-tag-variant {
          position: relative;
          display: inline-block;
          .alert-image {
            height: 16px;
            width: 16px;
            margin-bottom: 3px;
            margin-left: 2px;
          }
        }
        .add-variant-main {
          display: flex;
          position: relative;
          right: 8px;
          cursor: pointer;
          .add-variant-btn {
            height: 18px;
            width: 18px;
          }
          .add-variant-text {
            text-transform: uppercase;
            color: $green-light;
            position: relative;
            top: 2px;
            left: 5px;
          }
        }
        .disable-add-variant-main {
          opacity: 0.5;
          pointer-events: none;
        }
      }
      .add-tag-variant {
        color: $grey;
        padding-top: 12px;
      }
      .tag-variant-card-main {
        position: relative;
        width: 100%;
        display: grid;
        grid-template-columns: 25% 25% 25% 25%;
        grid-auto-rows: auto;
        padding-top: 12px;
        justify-content: space-evenly;
      }
    }
  }
}

.add-recipe-main {
  position: relative;
  padding-left: 26px;
  padding-right: 30px;
  width: 100%;
  height: 386px;
  margin-top: 26px;
  margin-bottom: 20px;

  .add-recipe-content {
    width: 100%;
    height: 100%;
    background-color: $white;
    border: 1px solid $grainsboro;
    border-radius: 8px;
    position: relative;

    .left-section {
      position: absolute;
      top: 32%;
      width: 450px;
      margin-left: 44px;

      .head-text {
        color: $black;
        margin-bottom: 8px;
      }

      .sub-text {
        color: $grey;
        margin-bottom: 30px;
      }
    }

    .right-section {
      position: absolute;
      width: 291px;
      right: 0;
      margin-top: 50px;
      margin-right: 110px;

      .image-content {
        width: 291px;
        height: 291px;

        .image {
          width: 291px;
          height: 291px;
        }
      }
    }
  }
}

.tag-recipes-table-content {
  position: relative;
  padding-left: 26px;
  padding-right: 30px;
  width: 100%;
  height: 100%;
  margin-bottom: 110px;

  .content {
    width: 100%;
    height: 100%;
    background-color: $white;
    border-radius: 8px;
    position: relative;

    .promoted-header {
      margin-top: 25px;
      margin-left: 20px;
      font-size: 24px;
      font-weight: 400;
      color: $black;
    }

    .promoted-subtitle {
      margin-left: 20px;
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 400;
      color: $grey;
    }

    .add-zero-section {
      width: 100%;
      padding: 0 20px;
      margin-bottom: 42px;

      .zero-promoted {
        width: 100%;
        text-align: center;
        margin: 0 auto;
        padding: 29px 0;
        background-color: $pearl-mist;
        border: 1px solid $grainsboro;
        border-radius: 4px;

        .bold {
          font-size: 16px;
          font-weight: 700;
          color: $spanish-gray;
        }

        .normal {
          font-size: 16px;
          font-weight: 400;
          color: $spanish-gray;
        }
      }
    }

    .tag-recipe-section {
      margin: 40px 0;
    }

    .recipe-tag {
      .recipe-header-section {
        width: 100%;
        justify-content: space-between;
        display: flex;
        padding: 20px 0px;

        .recipe-header {
          margin-left: 20px;
          color: $black;
        }

        .search-section {
          justify-content: space-between;
          display: flex;

          .search-box {
            position: relative;
            background-color: $pearl-mist;
            border: 1px solid $grainsboro;
            border-radius: 30px;
            padding: 0 0px 0 30px;
            height: 36px;
            width: 300px;
            margin-right: 20px;

            .search-input-box {
              width: 250px;
              height: 34px;
              margin: 0px 0px 0px 5px;
              padding: 0;
              background: none;
              color: $black;
              border: none;
              font-size: 16px;
              border-radius: 0;
              box-shadow: none;

              ::placeholder {
                font-size: 16px;
                color: $graphite-gray;
                font-weight: 400;
              }
            }

            .align-search-input-box {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              width: 230px;
              display: block;
            }

            .search-icon-green-image {
              position: relative;
              top: -26px;
              left: -18px;
              float: left;
              height: 18px;
              width: 18px;
              cursor: pointer;
            }

            .exit-search-icon {
              width: 16px;
              height: 16px;
              position: relative;
              top: -28px;
              left: 223px;
              cursor: pointer;
            }
          }

          .add-btn {
            cursor: pointer;
            max-width: 120px;
            height: 24px;
            margin-right: 20px;
            position: relative;
            top: 6px;

            .add-image {
              width: 18px;
              height: 18px;
            }

            .text {
              position: relative;
              top: 1px;
              color: $green;
            }
          }
        }
      }

      .recipe-table-content {
        .no-result-for-tag {
          display: flex;
          justify-content: space-around;
          margin-top: 14px;
          color: $shadow-gray;
          padding: 30px;
        }

        .new-title {
          height: 30px;
          align-items: center;
          background-color: $white-smoke;
          border-radius: 8px 8px 0px 0px;
          display: flex;
          padding-left: 88px;
          color: $spanish-gray;
          font-size: 14px;
          line-height: 1;
          text-align: left;
          margin: 0 4px;

          div {
            padding: 8px 0;
            font-weight: 700;
          }

          .category-group-isin {
            width: 133px;
          }

          .category-group-title {
            width: 369px;
          }

          .category-group-count {
            width: 177px;
          }

          @media only screen and (min-width: 1000px) and (max-width: 1200px) {
            .category-group-title {
              width: 230px;
            }

            .category-group-count {
              width: 130px;
            }
          }

          @media only screen and (min-width: 1200px) and (max-width: 1290px) {
            .category-group-title {
              width: 311px;
            }

            .category-group-count {
              width: 150px;
            }
          }

          @media only screen and (min-width: 1500px) {
            .category-group-title {
              width: 38%;
            }

            .category-group-count {
              width: 205px;
            }
          }

          @media only screen and (min-width: 1700px) {
            .category-group-title {
              width: 39%;
            }

            .category-group-count {
              width: 250px;
            }
          }

          @media only screen and (min-width: 1800px) {
            .category-group-title {
              width: 39%;
            }

            .category-group-count {
              width: 268px;
            }
          }

          @media only screen and (min-width: 2000px) {
            .category-group-title {
              width: 39.6%;
            }

            .category-group-count {
              width: 315px;
            }
          }

          @media only screen and (min-width: 2500px) {
            .category-group-title {
              width: 41%;
            }

            .category-group-count {
              width: 423px;
            }
          }

          @media only screen and (min-width: 4000px) {
            .category-group-title {
              width: 41.4%;
            }

            .category-group-count {
              width: 747px;
            }
          }

          @media only screen and (min-width: 4800px) {
            .category-group-title {
              width: 42;
            }

            .category-group-count {
              width: 1014px;
            }
          }

          .ing-count {
            span {
              margin-left: 6px;
            }
          }

          .status {
            span {
              display: flex;
              width: 240px;
              margin: auto;
              padding-left: 10px;
            }
          }
        }

        .recipe-table {
          margin-bottom: 22px;

          .table-head {
            background-color: $white-smoke;
            border-radius: 8px 8px 0px 0px;

            .title {
              color: $spanish-gray;
              font-size: 14px;
              line-height: 1;
              text-align: left;
              margin: 0 4px;

              th {
                padding: 8px 0;
                font-weight: 700;
              }

              .category-group-isin {
                width: 160px;
              }

              .ing-count {
                span {
                  margin-left: 6px;
                }
              }

              .status {
                span {
                  display: flex;
                  width: 240px;
                  margin: auto;
                  padding-left: 10px;
                }
              }
            }
          }

          .body {
            border-bottom: 1px solid $grainsboro;

            .image-recipe {
              width: 60px;
              height: 60px;
              overflow: hidden;
              border-radius: 4px;
              margin: 10px 18px;

              .image {
                object-fit: cover;
                width: 100%;
                height: 60px;
              }
            }

            .table-image-recipe {
              width: 70px;
            }

            .table-recipe-code {
              width: 70px;
            }

            .new-table-recipe-code {
              width: 130px;
            }

            .add-tag-recipe-name {
              position: relative;
            }

            .tag-recipe-name {
              position: relative;
            }

            .recipe-code {
              color: $stone-gray;
            }

            .add-tag-recipe-name-tooltip {
              display: flex;
              justify-content: flex-start;
              width: max-content;
            }

            .recipe-name {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              max-width: 320px;
              color: $black;
            }

            .recipe-subtitle {
              margin-top: 3px;
              color: $shadow-gray;
            }

            .recipe-details {
              .details {
                display: inline-block;
                width: 128px;
                color: $charcoal-gray;
              }

              .small {
                width: 88px;
              }
            }

            .ingredient-count {
              margin-left: 8px;
              width: 128px;
              display: inline-block;
              color: $charcoal-gray;
            }

            .recipe-btn {
              width: 142px;
            }

            .tag-published {
              width: 100px;
              display: flex;
              justify-content: flex-end;
              margin: auto;
              position: relative;
              top: 18px;

              .edit-btn {
                border-radius: 4px;
                box-shadow: 0px 1px 5px 0px $box-shadow;
                height: 32px;
                width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                img {
                  width: 16px;
                  height: 16px;
                }
              }

              .menu {
                position: relative;
                margin-right: 18px;
                top: 10px;

                .menu-container {
                  background-color: $white;
                  border-radius: 10px;
                  width: 28px;
                  height: 20px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;

                  .table-edit-btn {
                    width: 17px;
                    height: 5px;
                    padding: 0;
                    margin: 0 auto;
                    object-fit: cover;
                    z-index: 1;
                  }

                  &:hover {
                    background-color: $pure-white;
                  }
                }

                .menu-selected {
                  background-color: $aqua-spring;

                  &:hover {
                    background-color: $aqua-spring;
                  }
                }

                .menu-box {
                  display: block;
                  position: absolute;
                  right: 10px;
                  width: 151px;
                  top: 24px;
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 4px;
                  background: $white;

                  .menu-list {
                    list-style: none;
                    background: $white;
                    border-radius: 8px;
                    margin: 11px 5px;

                    li {
                      display: flex;
                      align-items: center;
                      height: 30px;
                      width: 141px;
                      color: $black;
                      padding-left: 10px;

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }

                    .hide-data {
                      display: none;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.pagination-container {
  margin-top: -20px;
  margin-bottom: 20px;
}

.tag-wrong-recipe-popup-main {
  display: flex;
  justify-content: center;
  z-index: 999;

  .tag-wrong-recipe-popup {
    display: flex;
    background: $light-rose;
    border: 1px solid $peachy-pink;
    width: 324px;
    height: 68px;
    border-radius: 7px;
    position: fixed;
    top: 74px;
    z-index: 999;

    .wrong-recipe-popup-container {
      display: flex;
      padding: 10px 24px;
      width: 100%;
      justify-content: space-between;
    }

    .wrong-text-container {
      display: flex;

      .wrong-recipe-popup-image {
        width: 22px;
        height: 22px;
        margin-top: 2px;
      }

      .wrong-recipe-popup-text {
        color: $jet-black;
        margin-left: 8px;
      }

      .try-again-text {
        color: $jet-black;
        margin-left: 8px;
      }
    }

    .close-icon-image {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
}
