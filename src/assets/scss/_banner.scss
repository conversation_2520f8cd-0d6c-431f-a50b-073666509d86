  .banner-body {
    border-bottom: 1px solid $bright-gray;
    max-height: max-content;

    .banner-isin {
      width: 100px;
      padding-right: 10px;
      padding-left: 10px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      color: $gunmetal-grey;
      word-break: break-all;
    }

    .banner-title {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 26px 10px;
      .banner-title-text {
        min-width: 232px;
        max-width: 242px;
        font-size: 14px;
        font-weight: 700;
        position: relative;
      }

      .banner-type-text {
        font-size: 12px;
        font-weight: 400;
      }
    }

    .banner-dates-ids {
      width: 150px;
      padding-right: 10px;
      padding-left: 10px;

      @media only screen and (max-width: 1200px) {
        width: 100px;
      }

      .banner-details {
        color: $grey;
        font-size: 14px;

        .publishdate {
          color: $black !important;
        }
      }
    }

    .banner-state-menu-option {
      padding-left: 10px;
      padding-right: 10px;
      word-break: normal;
    }

    .banner-menu {
      position: relative;
      padding: 12px 15px;
    }
  }

  .body:first-child {
    border-top: 1px solid $bright-gray;
  }

.banner-list-table-container {
  background-color: $white;
  border: 1px solid $grainsboro;
  border-radius: 8px;

  .banner-table {
    border-collapse: collapse;

    thead {
      background-color: $white-smoke;
      border-radius: 8px 8px 0 0;
      color: $spanish-gray;
      font-size: 12px;
      text-transform: uppercase;
      text-align: left;

      th {
        padding: 10px;
      }
    }
  }

  .view-expire-hero-container {
    .text-expire {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      color: $green;
    }
  }

  .simple-table-row:has(.toggle-row-cell.view-expire-hero-container) {
    height: 50px;
  }

  .banner-type-text {
    font-size: 12px;
    font-weight: 400;
  }

  .banner-title-text {
    min-width: 232px;
    max-width: 242px;
    font-size: 14px;
    font-weight: 700;
    position: relative;
  }

  .preview-type {
    border: 1px solid $green-light;
    width: fit-content;
    border-radius: 4px;
    height: 18px;
    margin-bottom: 5px;

    .preview-text {
      color: $green-light;
      font-weight: 400;
      padding: 1px 13px;
      font-size: 12px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
  }
}

.select-banner-modal {
  font-family: $font-family-averta;
  .select-banner-modal-content {
    padding: 12px 30px;
    width: 471px;

    .select-banner-modal-checkbox {
      text-align: initial;
      padding: 12px;

      .banner-text {
        width: 110px;
        height: 44px;
        margin-bottom: 10px;
      }

      .control-radio-group {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }

    .select-banner-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}

.banner-schedule-form-modal-container {
  font-family: $font-family-averta;
  width: 519px;
  height: 215px;
  overflow: visible;

  .banner-schedule-sub-container {
    .banner-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 10px 30px;

      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .banner-schedule-date-picker-container {
      display: flex;
      margin-top: 15px;
      margin-left: 30px;
    }

    .date-picker-container {
      position: relative;
    }

    .banner-schedule-bottom-container {
      position: relative;
      top: 50px;
      right: 20px;

      .banner-schedule-button-section {
        display: flex;
        gap: 10px;
        float: right;

        .disable-schedule-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
  }
}
