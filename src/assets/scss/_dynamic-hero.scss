.main-section {
  font-family: $font-family-averta;
  .add-opacity-to-create-new {
    opacity: 0.4;
    z-index: 1;
  }

  .change-background-color-for-selected {
    background-color: $aqua-spring;
  }

  .main-inner-section {
    padding: 0;
    .dynamic-hero-page-head {
      display: flex;
      justify-content: space-between;
      margin: 15px 0px 6px 0px;
      margin-bottom: 18px;

      .table-name-dynamic-hero {
        cursor: default;
        font-size: 24px;
        float: left;

        font-weight: 700;
        align-self: center;
      }

      .new-dynamic-hero-btn {
        width: 190px;
        height: 44px;
        font-size: 16px;
        color: $white;
        background-color: $green;
        border-radius: 50px;
        border: none;
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
        box-shadow: 0px 1px 2px 0px $box-shadow;
        font-weight: $font-weight-bold;
      }

      .save-btn {
        width: 120px;
        height: 44px;
        font-size: 16px;
        color: $white;
        background-color: $green;
        border-radius: 50px;
        border: none;
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
        box-shadow: 0px 1px 2px 0px $box-shadow;
        font-weight: 700;
      }

      .disable-save-btn {
        opacity: 0.3;
        pointer-events: none;
      }

      .cancel-btn {
        width: 140px;
        height: 44px;
        font-size: 16px;
        background-color: $white;
        color: $green;
        border-radius: 50px;
        position: relative;
        right: 15px;
        border: none;
        cursor: pointer;
        text-align: center;
        vertical-align: middle;
        box-shadow: 0px 1px 2px 0px $box-shadow;
        font-weight: 700;
      }
    }

    .hero-not-found {
      border-radius: 8px;
      margin-top: 20px;
      height: 80px;
      background-color: $white;
      border: 1px solid $grainsboro;
      color: $shadow-gray;
      text-align: center;
      line-height: 1;

      .no-data-hero {
        margin: auto;
        padding-top: 29px;
        color: $shadow-gray;
        font-weight: 700;
        text-align: center;
        background-color: $white;
        margin-bottom: 1px;
        .no-data-text {
          font-weight: 400;
          font-size: 16px;
          color: $shadow-gray;
          margin-top: 3px;
        }
      }
    }

    .dynamic-hero-table-main-container {
      cursor: default;
      margin-bottom: 57px;

      .dynamic-hero-content {
        width: 100%;
        height: 100%;
        background-color: $white;
        border: 1px solid $grainsboro;
        border-radius: 8px;
        position: relative;

        .dynamic-hero-table-content {
          .dynamic-hero-promote-table {
            .dynamic-hero-table-head {
              background-color: $white-smoke;
              border-radius: 8px 8px 0px 0px;

              .dynamic-hero-heading {
                color: $spanish-gray;
                font-size: 12px;
                line-height: 1;
                text-align: left;
                margin: 0 4px;
                text-transform: uppercase;

                th {
                  padding: 8px 0px 8px 10px;

                  font-weight: 700;
                }

                .dynamic-hero-status {
                  span {
                    display: flex;
                    margin: auto;
                  }
                }
              }
            }

            .dynamic-hero-body {
              border-bottom: 1px solid $bright-gray;

              .dynamic-hero-image-container {
                width: 90px;

                .dynamic-hero-image {
                  margin: 10px 0;
                  width: 60px;
                  height: 60px;
                  border-radius: 4px;
                  overflow: hidden;

                  img {
                    height: 60px;
                    width: 100%;
                    object-fit: cover;
                  }
                }
              }

              .dynamic-hero-title {
                margin-top: 10px;
                margin-bottom: 10px;
                display: flex;
                flex-direction: column;
                min-width: 260px;
                max-width: 326px;
                font-size: 14px;

                font-weight: 700;
                position: relative;
                padding-left: 10px;
                padding-right: 10px;

                .dynamic-hero-status-main-section {
                  display: flex;
                }

                .dynamic-hero-type {
                  background-color: $green-peppermint;
                  width: fit-content;
                  border-radius: 4px;
                  height: 18px;
                  margin-bottom: 5px;

                  span {
                    font-weight: 400;
                    padding: 0px 8px;
                    font-size: 12px;
                    color: $green-light;
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;
                  }
                }

                .preview-type {
                  margin-left: 9px;
                  background-color: $white;
                  border: 1px solid $green-light;

                  .preview-text {
                    color: $green-light;

                    font-weight: 400;
                    padding: 1px 13px;
                    font-size: 12px;
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;
                  }
                }

                .news-type {
                  background-color: $green-peppermint;

                  span {
                    color: $green-light;
                  }
                }

                .quiz-type {
                  background-color: $rose-white;

                  span {
                    color: $copper-rust;
                  }
                }

                .event-type {
                  background-color: $lavender-mist;

                  span {
                    color: $royal-purple ;
                  }
                }

                .advice-type {
                  background-color: $baby-blue;

                  span {
                    color: $ocean-blue ;
                  }
                }
                .content-type {
                  background-color: $alabaster;

                  span {
                    color: $mango-tango;
                  }
                }
              }

              .dynamic-hero-dates-ids {
                width: 135px;
                padding-right: 10px;
                padding-left: 10px;
                word-break: break-all;

                @media only screen and (max-width: 1200px) {
                  width: 100px;
                }

                .dynamic-hero-details {
                  color: $grey;
                  font-size: 14px;

                  .publishdate {
                    color: $black !important;
                  }
                }

                .dynamic-hero-details-date {
                  color: $grey;
                  font-size: 14px;
                }
              }

              .dynamic-hero-date-container-ids {
                width: 145px !important;
              }

              .dynamic-hero-state-menu-option {
                padding-left: 10px;
                padding-right: 10px;

                .dynamic-expired-state {
                  width: 86px;
                  height: 25px;
                }

                .dynamic-hero-published-state {
                  height: 25px;

                  font-weight: 400;
                  font-size: 12px;
                  color: $green;
                  background-color: $aqua-spring;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  border-radius: 4px;
                  padding: 4px 10px 0 10px;

                  img {
                    margin-right: 6px;
                    width: 16px;
                    height: 16px;
                  }

                  span {
                    display: flex;
                    padding-right: 16px;
                  }
                }

                .dynamic-hero-unpublished-state {
                  height: 25px;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  text-transform: capitalize;
                  color: $silver;
                  background-color: $pearl-mist;
                  border-radius: 4px;
                  padding: 4px 10px 0 10px;
                  font-size: 12px;

                  img {
                    margin-right: 6px;
                    width: 16px;
                    height: 16px;
                  }

                  span {
                    display: flex;
                    padding-right: 16px;
                  }
                }

                .dynamic-hero-live-state {
                  width: 60px;
                  height: 25px;
                }

                .dynamic-hero-draft-state {
                  height: 25px;
                  width: 78px;
                }

                .dynamic-hero-scheduled-state {
                  height: 25px;
                  width: 100px;
                }

                .selectedtrue {
                  opacity: 0.6;
                  pointer-events: none !important;
                  width: 122px;
                  height: 36px;
                  font-size: 14px;
                  background-color: $white;
                  color: $green;
                  border-radius: 50px;
                  border: none;
                  text-align: center;
                  vertical-align: middle;
                  box-shadow: 0px 1px 2px 0px $box-shadow;
                  font-weight: 700;
                }

                .select-btn {
                  width: 122px;
                  height: 36px;
                  font-size: 14px;
                  background-color: $white;
                  color: $green;
                  border-radius: 50px;
                  border: none;
                  cursor: pointer;
                  text-align: center;
                  vertical-align: middle;
                  box-shadow: 0px 1px 2px 0px $box-shadow;
                  font-weight: 700;
                }

                .selected-btn {
                  width: 122px;
                  height: 36px;
                  font-size: 14px;
                  background-color: $white;
                  color: $green;
                  border-radius: 50px;
                  border: none;
                  cursor: pointer;
                  text-align: center;
                  vertical-align: middle;
                  box-shadow: 0px 1px 2px 0px $box-shadow;
                  font-weight: 700;
                }

                .create-new-btn {
                  width: 140px;
                  height: 36px;
                  font-size: 16px;
                  color: $white;
                  background-color: $green;
                  border-radius: 50px;
                  border: none;
                  cursor: pointer;
                  text-align: center;
                  vertical-align: middle;
                  box-shadow: 0px 1px 2px 0px $box-shadow;
                  font-weight: 700;
                  position: relative;
                  right: 8px;
                }
              }

              .dynamic-hero-menu {
                position: relative;
                padding: 12px 15px;

                .dynamic-hero-menu-container {
                  background-color: $white;
                  border-radius: 10px;
                  width: 28px;
                  height: 20px;
                  cursor: pointer;
                  display: flex;
                  align-items: center;

                  .dynamic-hero-table-edit-btn {
                    width: 17px;
                    height: 5px;
                    padding: 0;
                    margin: 0 auto;
                    object-fit: cover;
                    z-index: 1;
                  }

                  &:hover {
                    background-color: $pure-white;
                  }
                }

                .dynamic-hero-menu-selected {
                  background-color: $aqua-spring;

                  &:hover {
                    background-color: $aqua-spring;
                  }
                }

                .dynamic-hero-menu-box {
                  display: block;
                  position: absolute;
                  right: 16px;
                  width: 163px;
                  top: 68%;
                  z-index: 2;
                  box-shadow: 0 4px 10px 0 $shadow-black,
                    0 3px 5px 0 $faint-black,
                    0 0 0 1px $shadowy-black;
                  border-radius: 4px;
                  background: $white;

                  .dynamic-hero-menu-list {
                    list-style: none;
                    background: $white;
                    border-radius: 8px;
                    margin: 11px 5px;

                    li {
                      display: flex;
                      align-items: center;
                      height: 30px;
                      width: 152px;
                      font-size: 16px;
                      color: $black;
                      font-weight: 700;
                      padding-left: 10px;

                      &:hover {
                        color: $white;
                        background: $green;
                        cursor: pointer;
                      }
                    }
                  }
                }
              }
            }

            .body:first-child {
              border-top: 1px solid $bright-gray;
            }
          }
        }

        .view-expire-hero-container {
          border-bottom: 1px solid $bright-gray;
          position: relative;
          height: 53px;
          display: table-row;
          text-align: center;

          .text-expire {
            width: fit-content;
            font-size: 14px;
            position: absolute;
            left: 41%;
            margin-top: 18px;
            font-weight: 700;
            cursor: pointer;
            color: $green;
          }

          .disable-expired-text {
            opacity: 0.3;
            pointer-events: none;
          }
        }
      }
    }

    .select-dynamic-hero-modal {
      .select-dynamic-hero-modal-content {
        padding: 12px 30px;
        width: 471px;

        .select-dynamic-hero-modal-heading {
          font-weight: 700;
          font-size: 20px;
          height: 44px;
          margin-right: 20px;
          color: $black;
          margin-bottom: 12px;
        }

        .select-dynamic-hero-modal-checkbox {
          display: flex;
          justify-content: space-between;
          text-align: initial;
          padding: 12px;
          .column {
            flex: 1;
            margin: 0 10px;
          }
          .dynamic-hero-label{
            margin-bottom: 18px;
            p {
              font-size: 16px;
              font-weight: 400;
              color: $black;
              font-family: $font-family-averta;
            }
          }
        }

        .select-dynamic-hero-modal-btn-container {
          display: flex;
          justify-content: flex-end;

          .select-dynamic-hero-confirm-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 21px;
            border-radius: 50px;
            border: 0;
            background-color: $green;
            text-shadow: 0 -1px 0 $faint-black;
            color: $white;
            font-weight: 900;
            box-shadow: 0 2px 4px 0 $box-shadow;
            margin: 5px;
            text-align: center;
            cursor: pointer;
            min-width: 121px;
            height: 44px;
            font-size: 14px;
          }

          .select-dynamic-hero-cancel-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 21px;
            border-radius: 50px;
            border: 0;
            background-color: $white;
            text-shadow: 0 -1px 0 $faint-black;
            color: $green;
            box-shadow: 0 1px 4px 1px $box-shadow;
            margin: 5px;
            text-align: center;
            cursor: pointer;
            min-width: 109px;
            height: 44px;
            font-size: 14px;
            font-weight: 900;
          }
        }
      }
    }
  }
}

.select-schedule-date-hero-modal {
  font-family: $font-family-averta;
  .error-delete-hero-modal-content {
    padding: 12px 30px;
    width: 471px;

    .error-cross-image-div {
      margin-right: 20px;
    }

    .error-cross-image {
      height: 80px;
      min-width: 80px;
    }

    .replacement-hero-image {
      -webkit-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      transform: rotate(90deg);
      height: 80px;
      min-width: 80px;
    }

    .select-schedule-date-main-container {
      display: flex;

      .select-schedule-date-hero-modal-heading {
        padding: 12px 0px;
        text-align: left;
        .capitalize-the-word {
          text-transform: capitalize;
        }

        .unable-schedule-text {
          font-weight: 700;
          font-size: 20px;
          color: $black;
          margin-bottom: 12px;
        }

        .delete-hero-text {
          font-weight: 700;
          font-size: 20px;
          color: $black;
          margin-bottom: 12px;
        }

        .error-delete-schedule-date-hero-modal-sub {
          .error-schedule-sub-heading {
            font-weight: 400;
            font-size: 16px;
            color: $gunmetal-grey;
          }

          .delete-schedule-sub-heading {
            font-weight: 400;
            font-size: 16px;
            color: $grey;
          }

          .quiz-draft-sub-heading {
            color: $ruby-red;

            font-weight: 400;
            font-size: 12px;
            line-height: 18px;
          }
        }
      }
    }

    .replacement-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;

      .select-schedule-date-hero-confirm-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $green;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;

        font-weight: 700;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 121px;
        height: 44px;
        font-size: 14px;
      }

      .select-schedule-date-hero-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $white;
        text-shadow: 0 -1px 0 $faint-black;
        color: $green;
        box-shadow: 0 1px 4px 1px $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 109px;
        height: 44px;
        font-size: 14px;
        font-weight: 800;
        font-weight: bolder;
      }
    }

    .error-delete-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 36px;

      .select-schedule-date-hero-okay-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50px;
        border: 0;
        background-color: $fiery-red-blaze;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        font-weight: 600;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin-left: 10px;
        text-align: center;
        cursor: pointer;
        width: 121px;
        height: 44px;
        font-size: 16px;
      }
    }

    .delete-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 36px;

      .delete-date-hero-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $white;
        text-shadow: 0 -1px 0 $faint-black;
        color: $green;
        box-shadow: 0 1px 4px 1px $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 109px;
        height: 44px;
        font-size: 14px;
        font-weight: 800;
        font-weight: bolder;
      }

      .delete-date-hero-delete-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50px;
        border: 0;
        background-color: $fiery-red-blaze;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;
        font-weight: 600;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin-left: 10px;
        text-align: center;
        cursor: pointer;
        width: 121px;
        height: 44px;
        font-size: 16px;
      }
    }
  }

  .select-schedule-date-hero-modal-content {
    padding: 12px 30px;
    width: 500px;
    height: 340px;

    .select-schedule-date-picker-container {
      display: flex;
      margin-top: 14px;
    }

    .select-schedule-date-main-container {
      display: flex;

      .select-schedule-date-hero-modal-heading {
        font-weight: 700;
        font-size: 20px;
        color: $black;
        margin-bottom: 12px;
      }

      .select-schedule-date-close-icon {
        position: absolute;
        right: 30px;

        img {
          cursor: pointer;
        }
      }
    }

    .select-schedule-date-hero-modal-btn-container {
      display: flex;
      justify-content: flex-end;
      position: absolute;
      right: 32px;
      bottom: 36px;

      .select-schedule-date-hero-confirm-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $green;
        text-shadow: 0 -1px 0 $faint-black;
        color: $white;

        font-weight: 700;
        box-shadow: 0 2px 4px 0 $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 121px;
        height: 44px;
        font-size: 14px;
      }

      .select-schedule-date-hero-cancel-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 21px;
        border-radius: 50px;
        border: 0;
        background-color: $white;
        text-shadow: 0 -1px 0 $faint-black;
        color: $green;
        box-shadow: 0 1px 4px 1px $box-shadow;
        margin: 5px;
        text-align: center;
        cursor: pointer;
        min-width: 109px;
        height: 44px;
        font-size: 14px;
        font-weight: 800;
        font-weight: bolder;
      }
    }
  }
}

.quiz-schedule-form-modal-containerr {
  width: 519px;
  height: 215px;
  overflow: visible;
  font-family: $font-family-averta;

  .quiz-schedule-sub-container {
    .quiz-schedule-top-container {
      display: flex;
      justify-content: space-between;
      padding: 10px 30px;

      .schedule-text {
        font-size: 20px;
        font-weight: 700;

        color: $black;
      }

      .close-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;

        .close-icon-image {
          height: 100%;
          width: 100%;
        }
      }
    }

    .event-quiz-schedule-date-picker-container {
      display: flex;
      margin-top: 15px;
      margin-left: 30px;
    }

    .date-picker-container {
      position: relative;
    }

    .quiz-schedule-bottom-container {
      position: relative;
      top: 50px;
      right: 20px;

      .quiz-schedule-button-section {
        float: right;

        .quiz-form-cancel-button {
          color: $green;
          background-color: $white;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
          width: 108.63px;
          height: 44px;
        }

        .quiz-form-schedule-button {
          width: 128px;
          height: 44px;
          color: $white;
          background-color: $green;
          font-size: 14px;
          border-radius: 50px;
          border: none;
          padding: 10px 25px;
          margin-left: 12px;
          font-weight: 800;
          cursor: pointer;
          box-shadow: 0px 1px 5px 0px $box-shadow;
        }

        .disable-schedule-button {
          pointer-events: none;
          opacity: 0.5;
        }
      }
    }
  }
}

.text-section-prev {
    color: $green-light;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 10px;
  }