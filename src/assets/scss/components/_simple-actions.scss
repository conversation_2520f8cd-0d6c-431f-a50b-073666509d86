.simple-actions {
  display: flex;
  gap: 10px;

  &-btn {
    position: relative;
  }

  &-tooltip-info,
  &-tooltip-warn {
    visibility: hidden;
    z-index: 9999;
    position: absolute;
    top: 50px;
    left: -50px;
    box-shadow: 0 4px 10px 0 $shadow-black, 0 3px 5px 0 $faint-black, 0 0 0 1px $shadowy-black;
    text-align: left;
  }

  &-tooltip-info {
    width: max-content;
    padding: 5px;
    border-radius: 4px;
    background-color: $sunset-orange;
    font-size: 10px;
    color: $white;
  }

  &-tooltip-warn {
    display: inline-flex;
    gap: 5px;
    left: -200px;
    width: 260px;
    padding: 10px;
    border-radius: $border-radius;
    background-color: $rose-white;
    font-size: 13px;
    color: $graphite-gray;

    > img {
      width: 14px;
      height: 14px;
      margin-top: 1px;
    }
  }

  &-btn-with-info-tooltip:hover &-tooltip-info,
  &-btn-with-warn-tooltip:hover &-tooltip-warn {
    visibility: visible;
  }
}
