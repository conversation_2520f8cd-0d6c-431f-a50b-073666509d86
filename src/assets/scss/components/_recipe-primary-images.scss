.recipe-primary-image-main {
    font-family: $font-family-averta;
    width: 300px;

    .theme-recipe-generator-page {
        border: 1px solid $dark-gray;
    }

    .theme-recipe-details-page {
        background-color: $light-mint;
    }

    .main-image-container {
        width: 279px;
        height: 279px;
        border-radius: 8px;
        position: relative;
        align-content: center;

        .image-zoom {
            cursor: zoom-in;
        }

        .main-image-section {

            &:hover .selector-slide-select{
                display: inline-block;
                pointer-events: auto;
            }

            .main-image {
                .main-image-recipe {
                    width: 100%;
                    height: 279px;
                    object-fit: cover;
                    border-radius: 8px;
                }
            }

            .image-box {
                position: relative;
                height: 100%;
                width: 100%;
                border-radius: 8px;
            }
        }
    }

    .image-top-section {
        position: absolute;
        padding: 4px;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .main-image-check {
            width: 84px;
            height: 36px;
            border-radius: 8px;
            background: $white;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .main-check-image {
                width: 20px;
            }

            .main-image-text {
                color: $graphite-gray;
                margin-bottom: 0;
            }
        }

        .delete-button-section {
            z-index: 2;

            .delete-recipe-button {
                width: 36px;
                height: 36px;
                background-color: $black;
                border-radius: 8px;
                
                .delete-button-image {
                    margin: 5px;
                }
            }
        }
    }

    .image-main-top-section {
        display: flex;
        justify-content: flex-end;
    }

    .image-info-tooltip-section {
        background-color: $shadow-gray;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        border-top: 1px solid $white;
        border-radius: 0px 0px 8px 8px;
        cursor: default;

        .image-info-text {
            display: flex;
            align-items: center;
            height: 46px;
            padding-left: 10px;
            color: #ffffff;
        }
    }

    .image-bottom-section {
        position: absolute;
        bottom: 14px;
        right: 14px;
        z-index: 9;

        .info-image {
            width: 20px;
            height: 20px;
        }
    }

    .delete-button-container {
        position: absolute;
        right: 4px;
        top: 4px;
        z-index: 9;
        background-color: $shadow-gray;
        border-radius: 25px;
        padding: 4px;
        display: flex;
        gap: 19px;
    }

    .selector-slide-select {
        display: none;
        pointer-events: none;
    }

    .primary-image-text {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-family: $font-family-averta;
        color: $semi-translucent-black;
        line-height: $line-height-16;
    }
    .white-background {
        background-color: $white;
    }

    .image-list-wrapper {
        overflow-x: auto;
        max-width: 279px;

        .image-list {
            display: flex;
            gap: 10px;
            width: max-content;
            padding: 12px 0px;
        }

        .image-container,
        .empty-block {
            width: 62px;
            height: 62px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .miniature-image {
            width: 57px;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }

        .image-selected {
            border: 2px solid $forest-green;
        }

        .image-selected-container {
            height: 100%;
            padding: 2px;
            border-radius: 8px;
            position: relative;
            
                &.hovered {
                    opacity: 1;
                }

            .image-checked {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: $green;
                border-radius: 8px 0px 0px 0px;
                width: 23px;
                height: 23px;

                .check-image {
                    width: 14px;
                    height: 14px;
                }
            }
        }
    }

    .image-list-wrapper::-webkit-scrollbar {
        height: 8px;
    }

    .image-list-wrapper::-webkit-scrollbar-thumb {
        background-color: $pale-blue;
        border-radius: 10px;
        border: 1px solid $shade-of-gray;
    }

    .image-list-wrapper::-webkit-scrollbar-track {
        background: $white;
    }


    .recipe-image-error-section {
        padding: 8px;
        border: 2px solid $red;
        border-radius: $border-radius;
    }

    .recipe-select-error-meesage {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-top: 20px;

        .text {
            color: $red;
        }
    }
}
