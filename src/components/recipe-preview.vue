<template>
  <div class="recipe-preview-page">
    <div v-if="isLoading" class="recipe-preview-loading-main">
      <loader />
    </div>
    <div v-else class="preview-main-container">
      <recipe-preview-variant-section
        v-if="isVariantExist"
        :recipePreviewVariantLanguageList="dropdownVariantData"
        :recipePreviewVariantSelectedLanguage="
          recipePreviewVariantDefaultLanguage
        "
        :isVariantDropdownVisible="isDropdownOpen"
        @openVariantDropdown="openVariantDropdown"
        @chooseRecipeVariant="chooseVariantPreviewLanguage"
      >
      </recipe-preview-variant-section>
      <div class="main-section">
        <div class="recipe-details-video-image-container">
          <div class="recipe-basic-details">
            <div class="recipe-video-image">
              <recipe-preview-media-section
                :isImageVisible="hasImage"
                :productImage="productImagePreview"
                :imageUrlLinkUpdate="recipePreviewImageUrlLinkUpdate"
                :isVideoVisible="hasVideo"
                :productVideo="productVideoPreview"
                :isVideoLoaded="isVideoLoading"
                :isLinkPresent="isLinkPreviewPresent"
                :linkURLImage="linkURLImage"
                :imageUrlDomain="hostNameImageUrlLinkUpdatePreview"
                :recipeName="recipeName"
                :defaultImage="defaultImage"
                :placeholderImage="placeholderImage"
                :showImageVideo="showImageVideo"
                :openImageLinkPage="openImageLinkPage"
                :videoPopup="videoPopup"
                :openLinkPage="openLinkPage"
                :checkVideoLoading="checkVideoLoading"
              >
              </recipe-preview-media-section>
              <recipe-preview-head-section
                :recipeName="recipeName"
                :recipeSubtitle="recipeSubtitle"
                :totalTime="getTotalTime()"
                :preprationTime="getPreparationTime()"
                :cookingTime="getCookTime()"
                :description="description"
                :recipePrice="Number(recipePrice)"
                :recipeCurrency="recipeCurrency[0].name"
                :notes="notes"
              >
              </recipe-preview-head-section>
            </div>
          </div>
        </div>
        <div class="recipe-steps-at-recipe-preview">
          <div class="recipe-steps-notes-container">
            <recipe-preview-steps-section
              :tasksDataPreview="tasksDataPreview"
              :isStepIngredientsDataLoading="isStepIngredientsDataLoading"
              :recipePreviewVariantDefaultLanguage="
                recipePreviewVariantDefaultLanguage
              "
              @openRecipeStepVideoPopup="openRecipeStepVideoPopup"
            ></recipe-preview-steps-section>
            <div class="notes-section">
              <div class="notes-heading">{{ $t("RECIPE_PREVIEW.NOTES") }}:</div>
              <div v-if="notes" class="notes-available">
                {{ notes }}
              </div>
              <div v-else class="no-notes-available">
                {{ $t("RECIPE_PREVIEW.NO_NOTES_TO_SHOW") }}
              </div>
            </div>
            <recipe-preview-nutrition-section
              :nutrientTableData="nutrientTableData"
              :nutritionServingSize="nutritionServingSize"
              :nutritionServingSizePerContainer="
                nutritionServingSizePerContainer
              "
              :isNutritionDropdownResultVisible="
                isNutritionDropdownResultVisible
              "
              :isNutritionDropDownIconVisible="isNutritionDropDownIconVisible"
              :nutritionServingList="nutritionServingList"
              :selectedNutritionType="selectedNutritionType"
              :isIngredientsDataLoading="isIngredientsDataLoading"
              :isStepIngredientsDataLoading="isStepIngredientsDataLoading"
              :choosePerServingPer100g="choosePerServingPer100g"
              @selectedNutrition="selectedNutrition"
              @showNutritionDropDown="showNutritionDropDown"
            />
          </div>
          <div class="ingredients-categories">
            <recipe-preview-ingredient-section
              :servings="Number(servings)"
              :availableServings="availableServings"
              :isIngredientsDataLoading="isIngredientsDataLoading"
              :isStepIngredientsDataLoading="isStepIngredientsDataLoading"
              :selectedServingsName="String(selectedServingsName)"
              :ingredientsDataPreview="ingredientsDataPreview"
              :ingredientsCountPreview="ingredientsCountPreview"
              :isServingsDropdownResultVisible="isServingsDropdownResultVisible"
              @showServingsData="showServingsData"
              @selectedServingsProductAsync="selectedServingsProductAsync"
            />
            <recipe-preview-filters-section
              :selectedDietsPreview="selectedDietsPreview"
              :selectedCategoriesPreview="selectedCategoriesPreview"
              :selectedTagsPreview="selectedTagsPreview"
              :isDisplayAllergens="isDisplayAllergens"
              :selectedAllergens="selectedAllergens"
              :recipePreviewVariantDefaultLanguage="
                recipePreviewVariantDefaultLanguage
              "
              :lang="lang"
            />
            <recipe-preview-slug-section
              :slugEdit="slugEdit"
              :recipeAttributionAuthor="recipeAttributionAuthor"
              :isShowPublisher="isShowPublisher"
              :selectedPublisherName="selectedPublisherName"
              :publisherImagePreview="publisherImagePreview"
              :recipeAttributionExternalId="recipeAttributionExternalId"
            />
          </div>
        </div>
      </div>
    </div>
    <recipeVideo
      v-if="isVideoModalVisible"
      :videoLink="videoLink"
      :closeModal="closeModal"
    />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import loader from "@/components/loader.vue";
import recipeVideo from "@/components/recipeVideo";
import recipePreviewVariantSection from "./recipe-preview/recipe-preview-variant-section.vue";
import recipePreviewStepsSection from "./recipe-preview/recipe-preview-steps-section.vue";
import recipePreviewMediaSection from "./recipe-preview/recipe-preview-media-section.vue";
import recipePreviewHeadSection from "./recipe-preview/recipe-preview-head-section.vue";
import recipePreviewFiltersSection from "./recipe-preview/recipe-preview-filters-section.vue";
import recipePreviewIngredientSection from "./recipe-preview/recipe-preview-ingredient-section.vue";
import recipePreviewSlugSection from "./recipe-preview/recipe-preview-slug-section.vue";
import recipePreviewNutritionSection from "./recipe-preview/recipe-preview-nutrition-section.vue";

// utility
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { useI18n } from "vue-i18n";


// declare
const store = useStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { t } = useI18n();
const { $eventBus } = useNuxtApp();

// images
import placeholderImage from "~/assets/images/Eclipse-1s-200px.gif";
import defaultOrganizationsImage from "@/assets/images/default_organizations.png";

const isVideoLoading = ref(false);
const videoLink = ref("");
const publisherImagePreview = ref("");
const isVideoModalVisible = ref(false);
const isCampaignModifiedPreview = ref(false);
const selectedTagsPreview = ref([]);
const tasksDataPreview = ref([]);
const selectedCategoriesPreview = ref([]);
const selectedDietsPreview = ref([]);
const ingredientsDataPreview = reactive({});
const productImagePreview = ref("");
const productVideoPreview = ref("");
const hasImage = ref(false);
const isPublishButtonPreviewVisible = ref(false);
const ingredientsCountPreview = ref(0);
const hasVideo = ref(false);
const linkURLImagePreview = ref("");
const urlLinkUpdatePreview = ref("");
const hostNameUrlLinkUpdatePreview = ref("");
const isLinkPreviewPresent = ref(false);
const isIngredientsDataLoading = ref(false);
const isStepIngredientsDataLoading = ref(false);
const isRecipeStepVideoOpen = ref(false);
const isServingsDropdownResultVisible = ref(false);
const selectedServingsName = ref("");
const isDropdownOpen = ref(false);
const isVariantExist = ref(false);
const dropdownVariantData = ref([]);
const lang = ref("");
const recipePreviewVariantDefaultLanguage = ref("");
const currentData = reactive({});
const recipeName = ref("");
const slugEdit = ref("");
const recipeSubtitle = ref("");
const description = ref("");
const notes = ref("");
const recipePreviewImageUrlLinkUpdate = ref("");
const defaultImage = ref("");
const hostNameImageUrlLinkUpdatePreview = ref("");
const tempSize = ref("");
const isNutritionDropDownIconVisible = ref(false);
const isNutritionDropdownResultVisible = ref(false);
const selectedNutritionType = ref("");
const nutritionServingList = ref([
  { name: "per Serving", type: "perServing" },
  { name: "per 100g", type: "per100g" },
]);
const isShowPublisher = ref(false);
const isLoading = ref(true);


// Define props
const props = defineProps({
  recipePrice: String,
  recipeCurrency: Array,
  checkVideoPopupStatus: Function,
  recipeVariantSelectedLanguage: String,
  selectedPublisherName: String,
  selectedPublisherImage: String,
  recipeAttributionAuthor: String,
  ingredientsCount: Number,
  linkURLImage: String,
  hostNameUrlLinkUpdate: String,
  hostNameImageUrlLinkUpdate: String,
  recipeImage: String,
  productVideo: String,
  hour: String,
  minute: String,
  isCampaignModified: Boolean,
  showPublishButton: Boolean,
  slugData: Object,
  cookHour: String,
  cookMinute: String,
  prepHour: String,
  prepMinute: String,
  selectedTags: Array,
  selectedCategories: Array,
  selectedDiets: Array,
  servings: Number,
  availableServings: Array,
  isDisplayAllergens: Boolean,
  selectedAllergens: Array,
  tasksData: Array,
  ingredientsData: Object,
  isLinkPresent: Boolean,
  urlLinkUpdate: String,
  recipeAttributionExternalId: String,
  availableLang: Array,
  nutrientTableData: Object,
  recipeData: Object,
  imageUrlLinkUpdate: String,
  nutritionServingSizePerContainer: String,
  nutritionServingSize: String,
  ingredientsUomList: Array,
});

// Lifecycle hooks
onMounted(async () => {
  updateRecipeCurrency();
  updateNutritionIconAndType();
  await setLanguageAndFeatureConfigAsync();
  checkVariantExistence();
  updateRecipePreviewData();
  addDocumentEventListeners();
  isLoading.value = false;
});

onUnmounted(() => {
  document.removeEventListener("input", handleClickOutside);
  document.removeEventListener("keyup", handleESCClickOutside);
});

onUpdated(() => {
  $eventBus.emit("campaignModified", isCampaignModifiedPreview.value);
});

// Methods
const updateRecipeCurrency = () => {
  if (props.recipeCurrency === $keys.KEY_NAMES.USD) {
    props.recipeCurrency = t("RECIPE_PREVIEW.DOLLARS");
  }
};

const updateNutritionIconAndType = () => {
  const { perServing, per100g } = props.nutrientTableData;
  isNutritionDropDownIconVisible.value =
    perServing.length !== 0 && per100g.length !== 0;
  if (perServing.length) {
    selectedNutritionType.value = $keys.KEY_NAMES.PER_SERVING;
  } else if (per100g.length) {
    selectedNutritionType.value = $keys.KEY_NAMES.PER_100G;
  }
};

const setLanguageAndFeatureConfigAsync = async () => {
  lang.value = store.getters["userData/getDefaultLang"];
  await getFeatureConfigAsync();
  recipePreviewVariantDefaultLanguage.value =
    props.recipeVariantSelectedLanguage;
};

const checkVariantExistence = () => {
  if (props.availableLang.length > 1) {
    isVariantExist.value = true;
    dropdownVariantData.value = [...props.availableLang];
  }
};

const updateRecipePreviewData = () => {
  if (props.recipeData) {
    Object.assign(currentData, props.recipeData);
    setPreviewData();
  }
  recipePreviewImageUrlLinkUpdate.value = props.imageUrlLinkUpdate;
  selectedTagsPreview.value = props.selectedTags;
  selectedCategoriesPreview.value = props.selectedCategories;
  selectedDietsPreview.value = props.selectedDiets;
  setIngredients();
  productVideoPreview.value = props.productVideo;
  productImagePreview.value = props.recipeImage;
  publisherImagePreview.value = props.selectedPublisherImage || defaultOrganizationsImage;
  hasImage.value = true;
  hostNameUrlLinkUpdatePreview.value = props.hostNameUrlLinkUpdate;
  linkURLImagePreview.value = props.linkURLImage;
  urlLinkUpdatePreview.value = props.urlLinkUpdate;
  isLinkPreviewPresent.value = props.isLinkPresent;
  isCampaignModifiedPreview.value = props.isCampaignModified;
  isPublishButtonPreviewVisible.value = props.showPublishButton;
  ingredientsCountPreview.value = props.ingredientsCount;
  hostNameImageUrlLinkUpdatePreview.value = props.hostNameImageUrlLinkUpdate;
  setRecipeStep();
};

const addDocumentEventListeners = () => {
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keyup", handleESCClickOutside);
};

const checkVideoLoading = () => {
  isVideoLoading.value = true;
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};

const choosePerServingPer100g = () => {
  if (selectedNutritionType.value === "perServing") {
    return props.nutrientTableData.perServing;
  } else if (selectedNutritionType.value === "per100g") {
    return props.nutrientTableData.per100g;
  }
};

const selectedNutrition = (type) => {
  selectedNutritionType.value = type;
  isNutritionDropdownResultVisible.value = false;
};

const showNutritionDropDown = () => {
  isNutritionDropdownResultVisible.value = !isNutritionDropdownResultVisible.value;
};

const setIngredients = () => {
  ingredientsDataPreview.children = [];

  props.ingredientsData?.[
    recipePreviewVariantDefaultLanguage.value
  ]?.children.forEach((data) => {
    if (data.children.length > 0) {
      const children = {
        children: data.children ? data.children : [],
        name: data.name || "",
        displayGroup: data.displayGroup || false,
      };
      ingredientsDataPreview.children.push(children);
    }
  });
  let idxForID = 0;
  ingredientsDataPreview.children.forEach((data) => {
    if (data.children.length > 0) {
      let dataChild = [];
      data.children.forEach((item) => {
        const children = {
          duplicateId: idxForID++,
          duplicateName: item.name,
          duplicateQuantity: item.quantity,
          duplicateUOM: item.UOM,
          note: item.note || "",
        };
        dataChild.push(children);
      });
      data.children = dataChild;
    }
  });
};

const setRecipeStep = () => {
  props.tasksData?.forEach((data) => {
    const langData = data?.[recipePreviewVariantDefaultLanguage.value] ?? {};
    const document = {
      [recipePreviewVariantDefaultLanguage.value]: {
        duplicateTitle: langData.title || "",
        duplicateInstructions: langData.instructions || [],
        duplicateIngredients: langData.ingredients || [],
        duplicateMedia: {
          image: langData.media?.image || "",
          video: [
            {
              url: langData.media?.video?.[0]?.url || "",
            },
          ],
        },
      },
    };
    tasksDataPreview.value.push(document);
  });
  let indexForID = 0;
  tasksDataPreview?.value?.forEach((tasksData) => {
    const ingredients =
      tasksData?.[recipePreviewVariantDefaultLanguage.value]
        ?.duplicateIngredients ?? [];
    ingredients.forEach((item) => {
      item.duplicateId = indexForID++;
    });
  });
};
const chooseVariantPreviewLanguage = (language) => {
  if (props.nutrientTableData.perServing.length !== 0) {
    selectedNutritionType.value = "perServing";
  } else if (props.nutrientTableData.per100g.length !== 0) {
    selectedNutritionType.value = "per100g";
  }

  recipePreviewVariantDefaultLanguage.value = language;
  isDropdownOpen.value = false;
  tasksDataPreview.value = [];
  setPreviewData();
  tasksDataPreview.length = 0;
  ingredientsDataPreview.children = [];
  setRecipeStep();
  setIngredients();
  showImageVideo("image");

  if (tempSize.value !== "") {
    selectedServingsName.value = tempSize.value;
  }
};

const setPreviewData = () => {
  const lang = recipePreviewVariantDefaultLanguage.value;
  recipeName.value = currentData?.title?.[lang] ?? "";
  slugEdit.value = props.slugData?.[lang]?.slugName ?? "";
  recipeSubtitle.value = currentData?.subtitle?.[lang] ?? "";
  description.value = currentData?.description?.[lang]?.abstract ?? "";
  notes.value = currentData?.description?.[lang]?.notes ?? "";
};

const openVariantDropdown = () => {
  tempSize.value = props.servings;
  isDropdownOpen.value = !isDropdownOpen.value;
};
const openLinkPage = () => {
  window.open(urlLinkUpdatePreview.value, "_blank");
};

const openImageLinkPage = () => {
  window.open(recipePreviewImageUrlLinkUpdate.value, "_blank");
};

const handleClickOutside = (event) => {
  const dropdowns = [
    { id: "#recipe-preview-variant-dropdown", state: isDropdownOpen },
    { id: "#servings-drop-down", state: isServingsDropdownResultVisible },
    { id: "#preview-nutrition-drop-down", state: isNutritionDropdownResultVisible },
  ];

  dropdowns.forEach(({ id, state }) => {
    if (state.value && !document.querySelector(id)?.contains(event.target)) {
      state.value = false;
    }
  });
};

const closeModal = () => {
  isRecipeStepVideoOpen.value = false;
  isVideoModalVisible.value = false;
  props.checkVideoPopupStatus(false);
};

const videoPopup = () => {
  videoLink.value = props.productVideo;
  isVideoModalVisible.value = true;
  props.checkVideoPopupStatus(isVideoModalVisible.value);
};

const openRecipeStepVideoPopup = (data) => {
  videoLink.value = data;
  isVideoModalVisible.value = true;
  props.checkVideoPopupStatus(isVideoModalVisible.value);
};

const getTime = (hour, minute) => {
  let time = "";
  if (hour && !minute) {
    time = `${hour} hour`;
  } else if (hour && minute) {
    const formattedMinute = minute < 10 ? `0${minute}` : minute;
    time = `${hour}.${formattedMinute} hour`;
  } else if (minute) {
    time = `${minute} min`;
  }
  return time;
};

const getTotalTime = () => getTime(props.hour, props.minute);
const getPreparationTime = () => getTime(props.prepHour, props.prepMinute);
const getCookTime = () => getTime(props.cookHour, props.cookMinute);

const showImageVideo = (file) => {
  const imageToggle = document.querySelector("#imageToggle");
  const videoToggle = document.querySelector("#videoToggle");

  if (file === "image") {
    imageToggle.style.backgroundColor = "#4DB935";
    imageToggle.style.color = "#FFFFFF";
    videoToggle.style.backgroundColor = "#FFFFFF";
    videoToggle.style.color = "#A8ABB0";
    imageToggle.style.width = "125px";
    imageToggle.style.zIndex = 10;
    videoToggle.style.zIndex = 5;
    videoToggle.style.width = "105px";
    hasImage.value = true;
    hasVideo.value = false;
  } else {
    videoToggle.style.backgroundColor = "#4DB935";
    videoToggle.style.color = "#FFFFFF";
    imageToggle.style.backgroundColor = "#FFFFFF";
    imageToggle.style.color = "#A8ABB0";
    hasVideo.value = true;
    hasImage.value = false;
    imageToggle.style.width = "105px";
    videoToggle.style.width = "125px";
    videoToggle.style.marginLeft = "-19px";
    videoToggle.style.zIndex = 10;
  }
};

// selectedServingsProductAsync function
const selectedServingsProductAsync = async (size) => {
  selectedServingsName.value = size;
  isServingsDropdownResultVisible.value = false;
  isIngredientsDataLoading.value = true;
  isStepIngredientsDataLoading.value = true;

  tasksDataPreview?.value?.forEach((tasksData) => {
    const langData = tasksData?.[recipePreviewVariantDefaultLanguage.value];
    if (langData?.duplicateIngredients) {
      langData.duplicateIngredients.forEach((item) => {
        if (item.name !== "") {
          props.ingredientsUomList.forEach((uom) => {
            if (item.UOM === uom.display) {
              item.UOM = uom.key;
            }
          });
        }
      });
    }
  });


  if (ingredientsDataPreview?.children) {
    ingredientsDataPreview.children.forEach((data) => {
      if (data?.children?.length > 0) {
        data.children.forEach((item) => {
          if (item.name !== "") {
            props.ingredientsUomList.forEach((uom) => {
              if (item.UOM === uom.display) {
                item.UOM = uom.key;
              }
            });
          }
        });
      }
    });
  }

  await getServingScaleAsync(size);
  await getTaskServingScaleAsync(size);

  isIngredientsDataLoading.value = false;
  isStepIngredientsDataLoading.value = false;
};

// getTaskServingScaleAsync function
const getTaskServingScaleAsync = async (size) => {
  let dataIngredient = [];

  props.tasksData.forEach((task) => {
    const ingredients =
      task?.[recipePreviewVariantDefaultLanguage.value]?.ingredients;
    ingredients?.forEach((item) => {
      if (item?.name) {
        dataIngredient.push({
          amount: {
            value: item.quantity ? Number(item.quantity) : 0,
            unit: item.UOM ?? "",
          },
          name: item.name,
        });
      }
    });
  });

  const payload = {
    scaleFactor: size && props.servings ? Number(size / props.servings) : 0,
    ingredients: dataIngredient,
  };

  try {
    await store.dispatch("recipePreview/getRecipeServingScaleAsync", {
      payload,
      lang: lang.value,
    });
    const response = store.getters["recipePreview/getRecipeServingScale"];

    tasksDataPreview.value.forEach((duplicateTask) => {
      let updatedData = [];
      const duplicateIngredients =
        duplicateTask?.[recipePreviewVariantDefaultLanguage.value]
          ?.duplicateIngredients;

      duplicateIngredients?.forEach((item) => {
        response?.forEach((res, index) => {
          if (Number(item?.duplicateId) === index) {
            updatedData.push({
              nameMirror: res.nameMirror || item.nameMirror,
              UOMMirror: res.UOMMirror || item.UOMMirror,
              quantityMirror: res.amount?.value
                ? Math.round(res.amount.value * 100) / 100
                : Math.round(item.quantityMirror * 100) / 100,
              foodItem: item.foodItem || "",
              modifier: item.modifier || "",
              note: item.note || "",
              duplicateId: item.duplicateId || "",
            });
          }
        });
      });

      duplicateTask[recipePreviewVariantDefaultLanguage.value].duplicateIngredients =
        updatedData;
    });

    tasksDataPreview.value.forEach((duplicateTask) => {
      const duplicateIngredients =
        duplicateTask?.[recipePreviewVariantDefaultLanguage.value]
          ?.duplicateIngredients;
      duplicateIngredients?.forEach((item) => {
        props.ingredientsUomList?.forEach((uom) => {
          if (item.UOM === uom.key) {
            item.UOM = uom.display;
          }
        });
      });
    });

    isStepIngredientsDataLoading.value = false;
  } catch (error) {
    isStepIngredientsDataLoading.value = false;
  }
};

// getServingScaleAsync function
const getServingScaleAsync = async (
  size
) => {
  let dataIngredient = [];

  props.ingredientsData?.[recipePreviewVariantDefaultLanguage.value]?.children?.forEach(
    (data) => {
      data?.children?.forEach((item) => {
        if (item?.name) {
          dataIngredient.push({
            amount: {
              value: item.quantity ? Number(item.quantity) : 0,
              unit: item.UOM || "",
            },
            name: item.name,
          });
        }
      });
    }
  );
  props.ingredientsUomList.forEach((uom) => {
    dataIngredient?.forEach((item) => {
      if (item.amount.unit === uom.display) {
        item.amount.unit = uom.key;
      }
    });
  });


  const payload = {
    scaleFactor: size && props.servings ? Number(size / props.servings) : 0,
    ingredients: dataIngredient,
  };


  try {
    await store.dispatch("recipePreview/getRecipeServingScaleAsync", {
      payload,
      lang: lang.value,
    });
    const response = store.getters["recipePreview/getRecipeServingScale"];

    ingredientsDataPreview?.children?.forEach((data) => {
      let updatedData = [];
      data?.children?.forEach((item) => {
        response?.forEach((res, index) => {
          if (Number(item?.duplicateId) === index) {
            updatedData.push({
              duplicateName: res.name || item.duplicateName,
              duplicateUOM: res.amount?.unit?.display || item.duplicateUOM,
              duplicateQuantity: res.amount?.value
                ? Math.round(res.amount.value * 100) / 100
                : Math.round(item.duplicateQuantity * 100) / 100,
              note: item.note || "",
              foodItem: item.foodItem || "",
              duplicateId: item.duplicateId || "",
            });
          }
        });
      });
      data.children = updatedData;
    });

    let data = ingredientsDataPreview;
    ingredientsDataPreview = {};
    ingredientsDataPreview = data;

    ingredientsDataPreview?.children?.forEach((data) => {
      data.children?.forEach((item) => {
        props.ingredientsUomList.forEach((uom) => {
          if (item.duplicateUOM === uom.key) {
            item.duplicateUOM = uom.display;
          }
        });
      });
    });

    isIngredientsDataLoading.value = false;
  } catch (error) {
    isIngredientsDataLoading.value = false;
  }
};

const showServingsData = () => {
  isServingsDropdownResultVisible.value =
    !isServingsDropdownResultVisible.value;
};

const getFeatureConfigAsync = async () => {
  const params = { lang: recipePreviewVariantDefaultLanguage.value };

  try {
    await store.dispatch("ingredient/getFeatureConfigAsync", { params });
    const response = store.getters["ingredient/getFeatureConfig"];
    isShowPublisher.value = response?.organizations ?? false;
  } catch (error) {
    console.error("Error in getFeatureConfigAsync", error);
  }
};
</script>
