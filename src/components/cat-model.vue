<template>
  <div v-show="isUpdateCategoryModal">
    <Modal @close="closeModal" id="isUpdateCategoryModal">
      <template #isUpdateCategoryModal>
        <div class="update-category-modal">
          <div class="title-section">
            <img alt="" class="exit" src="@/assets/images/exit-gray.png" @click="closeModal(), clearSearchInput()" />
            <div class="title">
              {{ categoriesTitle }}
            </div>
            <div class="search-container-for-category">
              <div class="search-title">
                {{ categoriesSubtitle }}
              </div>
              <div class="search-box">
                <input type="text" class="search-input-box" autocomplete="off" placeholder="Find a Category"
                  v-model="searchQueryCategory" @keypress.enter="getCategorySearch(searchQueryCategory)"
                  :class="{ 'align-search-input-box': searchQueryCategory }" />
                <img alt="" class="search-icon-green-image" @click="getCategorySearch(searchQueryCategory)"
                  src="@/assets/images/search-grey.png" />
                <img alt="" class="exit-search-icon" v-if="isSearchExitEnable"
                  @click="resetQuery(), clearSearchInput()" src="@/assets/images/exit-search.svg?skipsvgo=true" />
              </div>
            </div>
          </div>
          <div class="add-group-content" id="updateGroupContent">
            <div v-if="isTableDataLoading" class="table-image-loader">
              <div class="loader"></div>
            </div>
            <div class="container" v-if="!isTableDataLoading">
              <div class="no-recipe-result" v-if="!formatedCategoryPopUp?.length || hasNoCategoryFound">
                {{ $t('COMMON.NO_RESULTS') }}
              </div>
              <div class="card" v-for="(categories, index) in formatedCategoryPopUp" :key="index" :class="(categories.isChecked ? 'selected-categories' : '') ||
                (categories.isAlreadyAddedCategory
                  ? 'already-added-category'
                  : '')
                " @click="isChecked(categories)">
                <div class="card-image">
                  <img alt="card" class="image" :src="categories?.image || defaultImage"/>
                </div>
                <div
                  class="card-title-main"
                  :class="{
                      'simple-data-tooltip': isCatGrpNameVisible,
                    }"
                  :data-tooltip-text="isCatGrpNameVisible && categories?.name"
                >
                  <div
                    class="card-title"
                    :id="`addCategoryGroupName${index}${categories.isin}`"
                    @mouseover="checkAddCategoryGroupName(index, categories.isin)"
                    @mouseleave="hideAddCategoryGroupNameTip(index, categories.isin)"
                  >
                    {{ categories?.name ?? "" }}
                  </div>
                </div>
                <div class="total-recipe">
                  <span v-if="categories.totalRecipes > 1">{{ categories.totalRecipes }} {{ $t('COMMON.RECIPES')
                  }}</span>
                  <span v-if="categories.totalRecipes <= 1">{{ categories.totalRecipes }} Recipe</span>
                </div>
                <span class="checkmark"></span>
              </div>
            </div>
            <div class="load-button" v-if="fromPopUp + sizePopUp < updateCategoriesTotal">
              <button type="button" class="btn-green" @keydown="preventEnterAndSpaceKeyPress($event)" @click="loadUpdatePopUpAsync()">
                {{ $t('COMMON.LOAD_MORE') }}
              </button>
            </div>
          </div>
          <div class="add-group-content-space"></div>
          <div class="create-section">
            <div class="count-selected">
              Categories Selected: <b>{{ countCategoriesSelected }}</b>
            </div>
            <button type="button" class="btn-green add-button"
              @click="addCategoriesToGroupBtn && addCategoriesToGroupBtn(); clearSearchInput()"
              @keydown="preventEnterAndSpaceKeyPress($event)"
              :class="countCategoriesSelected === 0 ? 'disabled-button' : ''">
              {{ saveButtonMessage }}
            </button>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Modal from "@/components/Modal";
import { useProjectLang } from "@/composables/useProjectLang";

const searchQueryCategory = ref("");
const lang = ref("");

const { getDefaultLang } = useProjectLang();

const props = defineProps({
  saveButtonMessage: {
    type: String,
    default: "",
  },
  categoriesTitle: {
    type: String,
    default: "",
  },
  categoriesSubtitle: {
    type: String,
    default: "",
  },
  closeModal: {
    type: Function,
  },
  getCategorySearch: {
    type: Function,
  },
  isSearchExitEnable: {
    type: Boolean,
    default: false,
  },
  isTableDataLoading: {
    type: Boolean,
    default: false,
  },
  isCatGrpNameVisible: {
    type: Boolean,
    default: false,
  },
  formatedCategoryPopUp: {
    type: Array,
    default: () => [],
  },
  isChecked: {
    type: Function,
  },
  defaultImage: {
    type: String,
    default: "",
  },
  checkAddCategoryGroupName: {
    type: Function,
  },
  hideAddCategoryGroupNameTip: {
    type: Function,
  },
  fromPopUp: {
    type: Number,
    default: 0,
  },
  sizePopUp: {
    type: Number,
    default: 9,
  },
  updateCategoriesTotal: {
    type: Number,
    default: 0,
  },
  preventEnterAndSpaceKeyPress: {
    type: Function,
  },
  loadUpdatePopUpAsync: {
    type: Function,
  },
  countCategoriesSelected: {
    type: Number,
    default: 0,
  },
  addCategoriesToGroupBtn: {
    type: Function,
  },
  hasNoCategoryFound: {
    type: Boolean,
    default: false,
  },
  resetQuery: {
    type: Function,
  },
  queryCategory: {
    type: String,
    default: "",
  },
  isUpdateCategoryModal: {
    type: Boolean,
    default: false,
  },
});
onMounted(async () => {
  document.addEventListener("keyup", handleESCClickOutside);
  searchQueryCategory.value = props.queryCategory;
  lang.value = await getDefaultLang();
});
const clearSearchInput = () => {
  searchQueryCategory.value = "";
};
const handleESCClickOutside = (event) => {
  if (event && event.key === 'Escape') {
    clearSearchInput();
  }
};
onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
});
</script>
