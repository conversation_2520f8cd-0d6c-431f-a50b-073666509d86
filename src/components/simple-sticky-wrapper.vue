<template>
  <div
    ref="sticky"
    class="simple-sticky-wrapper"
    :class="{
      'simple-sticky-wrapper-stuck': stuck,
    }"
  >
    <div
      class="simple-sticky-wrapper-inner"
      :style="stuck ? stickyStyles : {}"
    >
      <slot />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  top: { type: [String, Number], default: 0 },
  left: { type: [String, Number], default: 'initial' },
  right: { type: [String, Number], default: 'initial' },
  zIndex: { type: [String, Number], default: 50 },
  width: { type: String, default: '' },
  distance: { type: Number, default: 0 },
});

const sticky = ref(null);
const stuck = ref(false);

const stickyStyles = computed(() => {
  const styles = {
    position: 'fixed',
    top: typeof props.top === 'number' ? `${props.top}px` : props.top,
    left: typeof props.left === 'number' ? `${props.left}px` : props.left,
    right: typeof props.right === 'number' ? `${props.right}px` : props.right,
    zIndex: props.zIndex,
  };

  if (props.width) {
    styles.width = props.width;
  } else if (sticky.value) {
    styles.width = `${sticky.value.offsetWidth}px`;
  }

  return styles;
});

const onScroll = () => {
  if (!sticky.value) return;
  stuck.value = window.scrollY > sticky.value.offsetTop + props.distance;
};

onMounted(() => {
  window.addEventListener('scroll', onScroll, { passive: true });
});

onUnmounted(() => {
  window.removeEventListener('scroll', onScroll);
});
</script>
