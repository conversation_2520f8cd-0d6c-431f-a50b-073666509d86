<template>
  <header class="header">
    <searchBar v-if="isSearchBarEnabled" />
    <div v-if="isAuthenticated && selectedProject" class="project-container">
      <div class="project-input-section">
        <button
          type="button"
          class="input-section project-dropdown btn-reset"
          :class="{ 'disabled': isRouteLoading }"
          @click="openProjectDropDown()"
        >
          <span class="project-name-text text-title-2 font-normal">{{ selectedProject }}</span>
          <span class="arrow">
            <img
              src="@/assets/images/arrow-right.png"
              class="project-dropdown-icon"
              :class="{
                rotate: isProjectDropdownResultVisible,
              }"
              alt="arrow-icon"
            />
          </span>
        </button>
        <ul
          v-show="isProjectDropdownResultVisible"
          class="project-autocomplete-results"
        >
          <li v-show="projectListCount > 3" class="search-project-container">
            <img alt="search-icon" src="@/assets/images/Search_Bar_Icon-web.png" />
            <input
              type="text"
              placeholder="Search"
              v-model.trim="projectSearchQuery"
              data-test-id="project-search-input"
            />
            <button
              v-show="projectSearchQuery.trim()"
              type="button"
              class="btn-reset search-exit-icon"
              @click="clearSearchQuery()"
              data-test-id="exit-icon"
            >
              <img alt="exit-icon" src="@/assets/images/exit-gray.png" />
            </button>
          </li>
          <div class="project-list-container" id="projectListResult">
            <li
              v-for="(data, index) in projectList"
              :key="index"
              class="project-autocomplete-result text-title-2 font-normal"
              :class="{ 'is-active': data.isChecked }"
            >
              <button
                type="button"
                class="select-project-button btn-reset"
                @click="selectProjectAsync(data, true)"
              >
                <span class="project-result-name">{{ data.name }}</span>
              </button>
            </li>
          </div>
        </ul>
      </div>
    </div>
    <div
      v-if="isAuthenticated"
      class="profile-icon"
      :class="{
        selected: isUserInfoModalVisible,
        'disable-profile-icon': isRouteLoading
      }"
    >
      <div
        :style="{
          backgroundColor: userProfileColor
        }"
        class="user-card"
        @click="openUserInfo"
      >
        <div v-if="user.name" class="user-name">{{ getFirstChar(user.name) }}</div>
      </div>
    </div>
    <div v-if="isUserInfoModalVisible" class="user-info-list">
      <ul>
        <li class="info-section-container" v-if="isAuthenticated">
          <div class="main-section">
            <div class="icon">
              <img src="@/assets/images/postcard-icon.png" alt="postcard icon" />
            </div>
            <div class="text">{{ user.name }}</div>
          </div>
        </li>
        <li class="info-section-container" @click="logoutFimsLite()">
          <div class="main-section">
            <div class="icon">
              <img src="@/assets/images/logout-icon.png" alt="logout icon" />
            </div>
            <div class="text">Logout</div>
          </div>
        </li>
      </ul>
    </div>

    <floating-notification-popup></floating-notification-popup>

    <Modal v-if="isConfirmLogoutVisible" @close="closeModal">
      <template #editProductMatches>
        <div class="organizations-confirmation-modal">
          <div class="organizations-confirmation-modal-content">
            <div class="confirm-exit-top-section">
              <div class="confirm-exit-image">
                <div class="confirm-exit-image-container">
                  <img alt="" :src="confirmExitImage" />
                </div>
              </div>
              <div class="confirmation-description">
                Are you sure you want to logout ?
              </div>
            </div>
            <div class="organizations-confirmation-button-container">
              <button type="button" class="btn-green-outline" @click="closeModal">
                {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button" class="btn-green" @click="userLogout">
                {{ $t('BUTTONS.CONFIRM_BUTTON') }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>

    <Teleport to="body">
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :callConfirm="confirmModalAction"
        :closeModal="closeModal"
      />
    </Teleport>
  </header>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useNuxtApp } from '#app';
import { useRoute } from 'vue-router';
import floatingNotificationPopup from "../floating-notification-popup.vue";
import searchBar from "@/components/search-bar/search-bar.vue";
import cancelModal from "@/components/cancel-modal.vue";
import Modal from "@/components/Modal.vue";
import { STORAGE_KEY } from "@/сonstants/storage-key";
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
import { useAuth0 } from '@auth0/auth0-vue';
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import confirmExitImage from '~/assets/images/confirm-exit.png';

const $emit = defineEmits();

import { useContext } from "../../composables/useContext.js";
const { t } = useI18n();
const store = useStore();
const { isAuthenticated, user } = useAuth0();
const { $eventBus, $keys } = useNuxtApp();
const route = useRoute();
const { appLogout } = useContext();
const { triggerLoading } = useCommonUtils();
const { getRef } = useRefUtils();
const { isAdmin, readyProject } = useProjectLang();

const isInnit = ref(false);
const isConfirmLogoutVisible = ref(false);
const isRouteLoading = ref(false);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const isUserInfoModalVisible = ref(false);
const isProjectDropdownResultVisible = ref(false);
const selectedData = ref({});
const isSelectedRoute = ref(false);
const projectSearchQuery = ref('');
const projectListCount = ref(0);
const isAdminCheck = ref(false);

const validRoutesForSearchBar = new Set(["recipes", "categories", "cat-group", "tags", "ingredients", "iq-users"]);
const isSearchBarEnabled = computed(() => validRoutesForSearchBar.has(route.name));

function clearSearchQuery() {
  projectSearchQuery.value = "";
}
function openProjectDropDown() {
  if (projectList.value.length > 0) {
    const scroll = getRef("projectListResult");
    scroll.scrollTo(0, 0);
  }
  isProjectDropdownResultVisible.value = !isProjectDropdownResultVisible.value;
  projectSearchQuery.value = "";
}
async function selectProjectAsync(data, isRoute) {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
    selectedData.value = data;
    isSelectedRoute.value = isRoute;
    return;
  }
  await changeProjectAsync(data, isRoute);
}
async function changeProjectAsync(data, isRoute) {
  isRouteLoading.value = true;
  triggerLoading("routeloading", true);

  if (isRoute) {
    await navigateTo("");
  }
  triggerLoading("campaignModified", false);
  triggerLoading('projectChanged');
  try {
    await useNuxtApp().$projectLang.switchProject({
      id: data.id,
      displayName: data.name
    });
    await store.dispatch('userData/fetchProjectsAsync', { isHotRefresh: true, isAdmin: isAdminCheck.value });

    await navigateTo("/overview");
  } catch (error) {
    console.error("[IQ][PageHeader] Error in changeProjectAsync", error);
    await navigateTo("/overview");
  } finally {
    isRouteLoading.value = false;
    triggerLoading("routeloading", false);
  }
}
function confirmModalAction() {
  isConfirmModalVisible.value = false;
  changeProjectAsync(selectedData.value, isSelectedRoute.value);
}
function getFirstChar(userName) {
  return userName[0];
}
function handleESCClickOutside(event) {
  if (event?.key === "Escape") {
    isConfirmLogoutVisible.value = false;
    triggerLoading("routeloading", false);
  }
}
async function userLogout() {
  isCampaignModified.value = false;
  // window.zE($keys.ZENDESK_WIDGET_EVENTS.WIDGET, $keys.ZENDESK_WIDGET_EVENTS.HIDE);
  triggerLoading("campaignModified", isCampaignModified.value);
  isConfirmLogoutVisible.value = false;
  await appLogout();
}
async function showErrorMessageAsync() {
  $eventBus.emit('show-floating-notification', t('COULD_NOT_SAVE_EDITS'), t('OPERATION_FAILED_MESSAGE'), $keys.KEY_NAMES.ERROR);
}
async function applyChangesToastAsync() {
  $eventBus.emit('show-floating-notification', t('COMMON.CHANGES_APPLIED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function calendarErrorAsync() {
  $eventBus.emit('show-floating-notification', t('COMMON.ERROR'), t('PLEASE_SELECT_DATES_BEFORE_DISABLED'), $keys.KEY_NAMES.ERROR);
}
async function collectionPublishToastAsync(isCollectionPublished) {
  const message = isCollectionPublished ? t('TEXT_POPUP.COLLECTION_FORM_IS_PUBLISHED') : t('TEXT_POPUP.COLLECTION_FORM_IS_SAVED');
  $eventBus.emit('show-floating-notification', message, '', $keys.KEY_NAMES.SUCCESS);
}
async function productAddedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_ADDED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function productPromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_PROMOTED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function productUnpromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.PRODUCT_UNPROMOTED'), '', $keys.KEY_NAMES.DELETED);
}
async function recipePromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.RECIPE_PROMOTED'), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeUnpromotedAsync() {
  $eventBus.emit('show-floating-notification', t('TEXT_POPUP.RECIPE_UNPROMOTED'), '', $keys.KEY_NAMES.DELETED);
}
async function recipeDeletedAsync(data) {
  let message = t('TEXT_POPUP.DELETED');
  if (data) {
    message = `Deleted ${data} group`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
async function newRecipeDeletedAsync(data) {
  let message = t('TEXT_POPUP.DELETED');
  if (data) {
    message = `Deleted ${data}`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
function iqUsersNotification(data) {
  const info = data?.color === $keys.KEY_NAMES.RED ? "error" : "success";
  $eventBus.emit('show-floating-notification', data?.message, '', info);
}
function newToastFloatNotification() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEW_LABEL_TEXT"), '', $keys.KEY_NAMES.LABELS);
}
function bannerRescheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_RESCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function liveBannerRescheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_DATE_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeRemovedAsync(data) {
  let message = t('TEXT_POPUP.REMOVED');
  if ((data === $keys.KEY_NAMES.DIET || data === $keys.KEY_NAMES.TAG) || (data === $keys.KEY_NAMES.DIETS || data === $keys.KEY_NAMES.TAGS)) {
    message = `Removed ${data}`;
  }
  $eventBus.emit("show-floating-notification", { popupMessage: message, popupType: $keys.KEY_NAMES.ERROR });
}
async function recipeSavedAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function recipeScheduledAsync(count) {
  const popupText = count > 1 ? t("TEXT_POPUP.RECIPES_SCHEDULED") : t("TEXT_POPUP.RECIPE_SCHEDULED");
  $eventBus.emit('show-floating-notification', popupText, '', $keys.KEY_NAMES.SUCCESS);
}
async function recipePublishedAsync(count) {
  const popupText = count > 1 ? t("TEXT_POPUP.RECIPES_PUBLISHED") : t("TEXT_POPUP.RECIPE_PUBLISHED");
  $eventBus.emit('show-floating-notification', popupText, '', $keys.KEY_NAMES.SUCCESS);
}
async function categoryNameChangedAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CATEGORY_NAME_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}
async function showIngredientAddedPopupAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADDED"), '', $keys.KEY_NAMES.SUCCESS);
}
function videoUploaded() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.UPLOADED"), '', $keys.KEY_NAMES.SUCCESS);
}

async function copyToClipboardAsync() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.COPY_TO_CLIPBOARD"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleUpdated() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
function articlePublished() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_PUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}

function articleUnpublished() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ARTICLE_UNPUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}

function draftSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.DRAFT_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function isPublishedData() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.PUBLISHED"), '', $keys.KEY_NAMES.SUCCESS);
}
function articleCategoryCreated() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEW_CATEGORY_ADDED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CONTENT_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function newsSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEWS_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function adviceSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADVICE_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}
function newScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.NEWS_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function adviceScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.ADVICE_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentLive() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function contentScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.CONTENT_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function quizSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.QUIZ_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function quizScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.QUIZ_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function eventSaved() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.EVENT_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function eventScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.EVENT_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroScheduled() {
  $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}
function bannerFormUpdated() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormScheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_SCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormUnscheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_UNSCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function bannerFormSaved() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.BANNER_FORM_SAVED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroUnscheduled() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_UNSCHEDULED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroReplaced() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.HERO_REPLACED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroChange() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_CHANGED"), '', $keys.KEY_NAMES.SUCCESS);
}

function heroUpdate() {
    $eventBus.emit('show-floating-notification', t("TEXT_POPUP.LIVE_HERO_UPDATED"), '', $keys.KEY_NAMES.SUCCESS);
}

async function recipeImageUploadFailedAsync() {
    const subMessage = t('COMMON.PLEASE_TRY_AGAIN_LATER') + t("COMMON.OR_CONTACT_US") + t("COMMON.CMS_SUPPORT");
    $eventBus.emit('show-floating-notification', t('COMMON.IMAGE_UPLOAD_ISSUE'), subMessage, $keys.KEY_NAMES.ERROR);
}

function somethingWentWrong() {
    $eventBus.emit('show-floating-notification', t('COMMON.SOMETHING_WENT_WRONG'), t('COMMON.PLEASE_TRY_AGAIN_LATER'), $keys.KEY_NAMES.ERROR);
}

function videoNameExist() {
  $eventBus.emit("show-floating-notification", {
    popupMessage: t("COMMON.ERROR"),
    popupSubMessage: t("VIDEO_ALREADY_EXISTS"),
    popupType: $keys.KEY_NAMES.ERROR
  });
}
function errorOccurred() {
  $eventBus.emit("show-floating-notification", {
    popupMessage: t("COMMON.ERROR"),
    popupSubMessage: t("COMMON.SOMETHING_WENT_WRONG"),
    popupType: $keys.KEY_NAMES.ERROR
  });
}
function videoUnexpectedError() {
    $eventBus.emit('show-floating-notification', t('COMMON.SOMETHING_WENT_WRONG'), t('COMMON.PLEASE_TRY_AGAIN_LATER'), $keys.KEY_NAMES.ERROR);
}

async function recipeImageGenerationFailedAsync() {
    const subMessage = t('COMMON.PLEASE_TRY_AGAIN_LATER') + t("COMMON.OR_CONTACT_US") + t("COMMON.CMS_SUPPORT");
    $eventBus.emit('show-floating-notification', t('COMMON.IMAGE_GENERATION_FAILED'), subMessage, $keys.KEY_NAMES.ERROR);
}
function handleClickOutsideBrandFilterPopup(event) {
  if (isProjectDropdownResultVisible.value) {
    const isOutsideDropdown = !document.querySelector(".project-dropdown").contains(event.target);
    const isClickOnSearchContainer = document.querySelector(".search-project-container")?.contains(event.target);
    const isClickOnExitIcon = event.target.closest('.search-exit-icon');
    if (isOutsideDropdown && !isClickOnSearchContainer && !isClickOnExitIcon) {
      openProjectDropDown();
    }
  }
};
function closeModal() {
  isConfirmLogoutVisible.value = false;
  isUserInfoModalVisible.value = false;
  isConfirmModalVisible.value = false;
  triggerLoading("routeloading", false);
};
function openUserInfo() {
  isUserInfoModalVisible.value = !isUserInfoModalVisible.value;
};

function handleClickOutside(event) {
  if (!document?.querySelector(".profile-icon")?.contains(event.target)) {
    isUserInfoModalVisible.value = false;
  }
};
function logoutFimsLite() {
  isConfirmLogoutVisible.value = true;
  triggerLoading("routeloading", isConfirmLogoutVisible.value);

  if (isConfirmLogoutVisible.value) {
    localStorage.removeItem(STORAGE_KEY.USER_DATA_PROJECT);
  }
};
async function innit() {
  if (isAuthenticated.value) {
    isInnit.value = true;
    document.addEventListener("keyup", handleESCClickOutside);

    const eventHandlers = {
      campaignModified: (data) => (isCampaignModified.value = data),
      routeloading: (data) => (isRouteLoading.value = data),
      iqUsersNotification,
      error_for_409: showErrorMessageAsync,
      calendarError: calendarErrorAsync,
      [ $keys.KEY_NAMES.DELETED_SUCCESS]: recipeDeletedAsync,
      [ $keys.KEY_NAMES.NEW_DELETED_SUCCESS]: newRecipeDeletedAsync,
      [ $keys.KEY_NAMES.NEW_SUCCESS]: newToastFloatNotification,
      [ $keys.KEY_NAMES.PRODUCT_PROMOTED]: productPromotedAsync,
      [ $keys.KEY_NAMES.PRODUCT_ADDED]: productAddedAsync,
      [ $keys.KEY_NAMES.RECIPE_PROMOTED]: recipePromotedAsync,
      [ $keys.KEY_NAMES.PRODUCT_UNPROMOTED]: productUnpromotedAsync,
      [ $keys.KEY_NAMES.RECIPE_UNPROMOTED]: recipeUnpromotedAsync,
      [ $keys.KEY_NAMES.DELETED]: recipeRemovedAsync,
      [ $keys.KEY_NAMES.APPLY]: applyChangesToastAsync,
      [ $keys.KEY_NAMES.SAVED_SUCCESS]: recipeSavedAsync,
      [ $keys.KEY_NAMES.RECIPE_PUBLISHED]: recipePublishedAsync,
      [ $keys.KEY_NAMES.SCHEDULE_SUCCESS]: recipeScheduledAsync,
      [ $keys.KEY_NAMES.ARTICLE_NAME_CHANGED]: categoryNameChangedAsync,
      [ $keys.KEY_NAMES.INGREDIENT_ADDED]: showIngredientAddedPopupAsync,
      [ $keys.KEY_NAMES.ARTICLE_SUCCESS]: articleSaved,
      [ $keys.KEY_NAMES.ARTICLE_UPDATED]: articleUpdated,
      [ $keys.KEY_NAMES.DRAFT_SAVED]: draftSaved,
      [ $keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS]: articlePublished,
      [ $keys.KEY_NAMES.PUBLISHED_DATE]: isPublishedData,
      [ $keys.KEY_NAMES.ARTICLE_CREATED]: articleCategoryCreated,
      [ $keys.KEY_NAMES.CLOSE_FLOATING_POPUP]: closeModal,
      [ $keys.KEY_NAMES.ARTICLE_UNPUBLISHED_SUCCESS]: articleUnpublished,
      contentSaved,
      contentScheduled,
      contentLive,
      newsSaved,
      AdviceSaved: adviceSaved,
      newScheduled,
      AdviceScheduled: adviceScheduled,
      quizSaved,
      quizScheduled,
      eventSaved,
      eventScheduled,
      collectionPublishToastAsync,
      heroScheduled,
      heroUnscheduled,
      heroReplaced,
      heroChange,
      heroUpdate,
      [ $keys.KEY_NAMES.ARTICLE_WRONG]: somethingWentWrong,
      [ $keys.KEY_NAMES.VIDEO_NAME_EXIST]: videoNameExist,
      [ $keys.KEY_NAMES.IMAGE_GENERATION_FAILED]: recipeImageGenerationFailedAsync,
      [ $keys.KEY_NAMES.IMAGE_UPLOAD_ISSUE]: recipeImageUploadFailedAsync,
      [ $keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR]: videoUnexpectedError,
      [ $keys.KEY_NAMES.CATEGORY_WRONG_CLOSED]: somethingWentWrong,
      [ $keys.KEY_NAMES.VIDEO_UPLOADED]: videoUploaded,
      copyToClipboard: copyToClipboardAsync,
      bannerFormUpdated,
      bannerFormScheduled,
      bannerFormUnscheduled,
      bannerFormSaved,
      somethingWentWrong,
      errorOccurred,
    };

    Object.entries(eventHandlers).forEach(([event, handler]) => {
      $eventBus.on(event, handler);
    });

    document.addEventListener("click", handleClickOutsideBrandFilterPopup);
    document.addEventListener("click", handleClickOutside);
  }
};
const projectList = computed(() => {
  const project = store.getters["userData/getProject"];
  const projectList = store.getters["userData/getProjectList"] || [];

  projectListCount.value = projectList.length;

  const filteredProjects = projectList.filter(({ displayName }) => {
    return !projectSearchQuery.value ||
      displayName?.toLowerCase().startsWith(projectSearchQuery.value.toLowerCase());
  });

  return filteredProjects.map(({ id, displayName }) => ({
    id,
    name: displayName,
    isChecked: id === project?.id,
  }));
});
const selectedProject = computed(() => {
  return store.getters["userData/getProject"]?.displayName || "";
});
const userProfileColor = computed(() => {
  const projectUsers = store.getters["userData/getProject"];
  const userId = user.sub;
  return projectUsers?.users?.find((user) => user.id === userId)?.profileColor;
});
onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isAdminCheck.value = await isAdmin.value;
    }
  });
  innit().catch();
});
watch(
  () => isAuthenticated.value,
  (val) => {
    if (val && !isInnit.value) {
      innit().catch((error) => {
        console.error("Error during initialization:", error);
      });
    }
  }
);
const cleanupEventListeners = () => {
  $eventBus.off($keys.KEY_NAMES.ARTICLE_NAME_CHANGED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_UPDATED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_CREATED);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_UNPUBLISHED_SUCCESS);
  $eventBus.off($keys.KEY_NAMES.ARTICLE_WRONG);
  $eventBus.off($keys.KEY_NAMES.CATEGORY_WRONG_CLOSED);
  $eventBus.off($keys.KEY_NAMES.VIDEO_UPLOADED);
  $eventBus.off($keys.KEY_NAMES.VIDEO_NAME_EXIST);
  $eventBus.off($keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR);
  $eventBus.off($keys.KEY_NAMES.SOMETHING_WENT_WRONG);
  $eventBus.off($keys.KEY_NAMES.IQ_USERS_NOTIFICATION);
  $eventBus.off($keys.KEY_NAMES.IMAGE_GENERATION_FAILED);
  $eventBus.off($keys.KEY_NAMES.PRODUCT_ADDED);
  $eventBus.off($keys.KEY_NAMES.IMAGE_UPLOAD_ISSUE);

  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  window.removeEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
  window.removeEventListener($keys.KEY_NAMES.CLICK, handleClickOutsideBrandFilterPopup);
};
onBeforeUnmount(() => {
  cleanupEventListeners();
});
</script>
