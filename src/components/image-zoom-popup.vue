<template>
    <div class="iq-r-g-slider-dialog modal-backdrop" @click="handleOutsideClick">
      <div
        ref="sliderDialogContentRef"
        class="iq-r-g-slider-dialog-content"
        :style="dialogStyles"
      >
        <button
          type="button"
          class="iq-r-g-slider-dialog-content-close btn-reset"
          data-test-id="close-dialog-content"
          @click="zoomOutImage"
        >
          <img alt="Close" src="@/assets/images/exit-gray.png" />
        </button>
        <button
          type="button"
          @click="$emit('prev-image')"
          :disabled="selectedImageIndex === 0"
          class="iq-r-g-image-selector-slider-nav-btn iq-r-g-image-selector-slider-nav-btn-prev"
        >
          <span></span>
        </button>
        <img
          :src="computedZoomedImage"
          alt="Zoomed"
          ref="imageRef"
          @error="$event.target.src = defaultImage"
        />
        <button
          type="button"
          @click="$emit('next-image')"
          :disabled="selectedImageIndex === imageList?.flat().length - 1"
          class="iq-r-g-image-selector-slider-nav-btn iq-r-g-image-selector-slider-nav-btn-next"
        >
          <span></span>
        </button>
      </div>
    </div>
  </template>

<script setup>
import { onMounted, onBeforeUnmount, computed, ref } from "vue";
import { useNuxtApp } from '#app'

const { $eventBus } = useNuxtApp();
const props = defineProps({
  zoomedImage: String,
  defaultImage: String,
  selectedImageIndex: Number,
  imageList: Array,
});
const dialogContentWidth = ref("0");
const dialogContentHeight = ref("0");
const windowScrollPosition = ref(0);
const sliderDialogContentRef = ref(null);
const imageRef = ref('');

const { getRef } = useRefUtils();
const emit = defineEmits();
const computedZoomedImage = computed(() => props.zoomedImage);

const dialogStyles = computed(() => ({
  width: dialogContentWidth.value,
  height: dialogContentHeight.value,
}));
onMounted(() => {
  handleEsc();
  disableScrolling();
  setDialogContentStyles();
  window.addEventListener("resize", setDialogContentStyles);
  window.addEventListener("click", setDialogContentStyles);
});
onBeforeUnmount(() => {
  zoomOutImage();
  window.removeEventListener("resize", setDialogContentStyles);
  window.removeEventListener('click', setDialogContentStyles)
});
const handleEsc = () => {
  const closeOnEsc = (event) => {
    if (event.keyCode === 27) {
      zoomOutImage();
    }
  };
  document.addEventListener("keyup", closeOnEsc);

  onBeforeUnmount(() => {
    document.removeEventListener("keyup", closeOnEsc);
  });
};
const setDialogContentStyles = () => {
  const maxWidth = window.innerWidth * 0.8;
  const maxHeight = window.innerHeight * 0.8;

  if (imageRef.value) {
    const { naturalWidth, naturalHeight } = imageRef.value;
    const aspectRatio = naturalWidth / naturalHeight;

    const width = Math.min(maxWidth, maxHeight * aspectRatio);
    const height = Math.min(maxHeight, maxWidth / aspectRatio);

    dialogContentWidth.value = `${aspectRatio > 1 ? width : height}px`;
    dialogContentHeight.value = `${aspectRatio > 1 ? height : width}px`;
  } else {
    dialogContentWidth.value = window.innerWidth > window.innerHeight ? "auto" : `${window.innerWidth * 0.6}px`;
    dialogContentHeight.value = window.innerWidth > window.innerHeight ? `${window.innerHeight - 80}px` : "auto";
  }
};
const disableScrolling = () => {
  windowScrollPosition.value = window.scrollY;
  const body = document.body;
  body.style.cssText = `
    overflow: hidden;
    position: fixed;
    top: -${windowScrollPosition.value}px;
    width: 100%;
  `;
};
const enableScrolling = () => {
  const body = document.body;
  body.style.cssText = `
    overflow: '';
    position: '';
    top: '';
    width: '';
  `;
  window.scrollTo(0, windowScrollPosition.value);
};
const zoomOutImage = () => {
  enableScrolling();
  dialogContentWidth.value = "0";
  dialogContentHeight.value = "0";
  
  emit("close-image-zoom");
};
const handleOutsideClick = (event) => {
  const content = sliderDialogContentRef.value;
  if (content && !content.contains(event.target)) {
    zoomOutImage();
  }
};
</script>
