<template>
  <Modal v-show="isVisible" @close="closeModal">
    <template #selectGroupType>
      <div class="select-dynamic-hero-modal">
        <div class="select-dynamic-hero-modal-content">
          <div class="select-dynamic-hero-modal-heading">
            {{ $t('DYNAMIC_HERO.DYNAMIC_HERO_TYPES') }}
          </div>

          <div class="select-dynamic-hero-modal-checkbox">
            <div v-for="(columnTypes, index) in [leftColumnTypes, rightColumnTypes]" :key="index" class="column">
              <div v-for="dynamicType in columnTypes" :key="dynamicType.key" class="column-list">
                <label class="control-radio control-radio-20 dynamic-hero-label">
                  <input type="radio" :value="dynamicType.key"
                    v-model="internalSelectedDynamicType"
                    @change="selectDynamicHero(dynamicType.key)"
                  />
                  <span class="checkmark"></span>
                  <p>{{ dynamicType.options }}</p>
              </label>
              </div>
            </div>
          </div>

          <div class="select-dynamic-hero-modal-btn-container">
            <div
              class="select-dynamic-hero-cancel-btn"
              @click="closeSelectDynamicHero"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </div>
            <div
              class="select-dynamic-hero-confirm-btn"
              @click="selectedDynamicOption()"
            >
              {{ $t('COMMON.NEXT') }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false,
  },
  leftColumnTypes: {
    type: Array,
    required: true,
  },
  rightColumnTypes: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: String,
    default: '',
  },
  selectedDynamicOption: {
    type: Function,
    required: true,
  },
});

const emit = defineEmits(['close', 'update:modelValue', 'closeSelectDynamicHero']);

const internalSelectedDynamicType = ref(props.modelValue);

watch(() => props.modelValue, (newValue) => {
  internalSelectedDynamicType.value = newValue;
});

const closeModal = () => {
  emit('close');
};

const selectDynamicHero = (key) => {
  internalSelectedDynamicType.value = key;
  emit('update:modelValue', key);
};

const closeSelectDynamicHero = () => {
  emit('closeSelectDynamicHero');
};
</script>
