<template>
  <div :class="baseClass + '-event-input'">
    <div 
      :class="[baseClass + '-event-input-container', { 'simple-data-tooltip': isEventNameInFocus }]"
      :data-tooltip-text="isEventNameInFocus && localEventName"
    >
      <input
        :class="baseClass + '-event-input-text'"
        id="eventInputField"
        v-model.trim="localEventName"
        autocomplete="off"
        placeholder="Event name in CMS"
        @mouseover="checkEventName"
        @keydown="hideEventTip"
        @mouseleave="hideEventTip"
        @input="onInputChange"
      />
      <span v-if="!localEventName" class="asterisk-input">*</span>
    </div>
    <div class="main-section">
      <div :class="baseClass + '-event-image-format'">
        <b>Event image:</b> jpg/ jpeg/ png format (max 15 MB)
        <span class="warning-asterisk-input">*</span>
      </div>

      <div
        v-if="!isReplaceLiveHero && !isLiveHero && isEditPage"
        class="delete"
        @click="deleteEvent()"
      >
        <img
          alt=""
          src="@/assets/images/delete-icon.png"
          width="15"
          height="16"
        />
        <span>{{ $t("DYNAMIC_HERO.DELETE_EVENT") }}</span>
      </div>
    </div>

    <div
      v-if="!isReplaceLiveHero && !isLiveHero"
      class="publish-button"
    >
      <div class="schedule-btn">
        <span :class="props.dateValue === '' ? 'text disabled-text' : 'text'">
          {{ $t("DYNAMIC_HERO.SCHEDULE") }}
        </span>
        <label
          class="switch"
          :class="{
            'simple-data-tooltip edge': props.dateValue == 0,
          }"
          :data-tooltip-text="props.dateValue == 0 && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
        >
          <input
            type="checkbox"
            :disabled="!props.dateValue ? true : false"
            :checked="props.dateValue != 0 && props.isEventStatus != ''"
            @click="toggleEventStatus"
          />
          <span
            :class="
              !props.dateValue ? 'slider-round disabled-slider' : 'slider-round'
            "
          ></span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  baseClass: { type: String, default: "add" },
  eventName: { type: String, required: true },
  dateValue: { type: [Date, Array, String, Object, Number], required: true },
  isReplaceLiveHero: { type: Boolean, default: false },
  isEventStatus: { type: Boolean, default: false },
  isEventNameInFocus: { type: Boolean, default: false },
  deleteEvent: { type: Function, default: () => {} },
  isEditPage: { type: Boolean, default: false },
  isLiveHero: { type: Boolean, default: false },
  checkEventName: { type: Function, default: () => {} },
  hideEventTip: { type: Function, default: () => {} },
});

const emit = defineEmits(["update:eventName", "scheduleToggle"]);

const localEventName = ref(props.eventName);

const onInputChange = (event) => {
  emit("update:eventName", event.target.value);
  props.hideEventTip();
};

const toggleEventStatus = (event) => {
  emit("scheduleToggle", event.target.checked);
};
</script>
