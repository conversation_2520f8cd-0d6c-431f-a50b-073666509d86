<template>
    <div class="dynamic-hero-page-head">
      <div v-if="!isCreateNewVisible" class="table-name-dynamic-hero color-green-dark">
        {{ $t('DYNAMIC_HERO.DYNAMIC_HEROES_LIST') }}
      </div>
      <div v-if="isCreateNewVisible" class="table-name-dynamic-hero">
        {{ $t('DYNAMIC_HERO.REPLACE_LIVE_HERO_TEXT') }}
      </div>
  
      <div v-if="!hasReloadTextCheck">
        <button
          type="button"
          v-if="!isCreateNewVisible && !isloading"
          @click="openNewHeroPopup"
          class="new-dynamic-hero-btn"
        >
          {{ $t('DYNAMIC_HERO.NEW_HERO') }}
        </button>
  
        <button
          type="button"
          v-if="isCreateNewVisible && !isloading"
          @click="backBtn"
          class="cancel-btn"
        >
          {{ $t('BUTTONS.CANCEL_BUTTON') }}
        </button>
  
        <button
          type="button"
          v-if="isCreateNewVisible && !isloading"
          @click="openSavePopup"
          :class="saveButtonClass"
        >
          {{ $t('BUTTONS.SAVE_BUTTON') }}
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue'
  
  const props = defineProps({
    isCreateNewVisible: Boolean,
    isloading: Boolean,
    isSelected: Boolean,
    hasReloadTextCheck: Boolean,
    openSavePopup: Function,
    backBtn: Function,
    openNewHeroPopup: Function,
  })
  
  const saveButtonClass = computed(() => {
    return props.isSelected ? 'save-btn' : 'disable-save-btn save-btn'
  })
  </script>
  