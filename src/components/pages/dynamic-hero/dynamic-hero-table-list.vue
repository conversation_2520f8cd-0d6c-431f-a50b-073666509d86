<template>
  <div class="dynamic-hero-content">
    <div class="dynamic-hero-table-content">
      <table class="dynamic-hero-promote-table" id="promoteTable">
        <caption></caption>
        <thead class="dynamic-hero-table-head">
          <tr class="dynamic-hero-heading">
            <th>
              <span>{{ $t("COLLECTION.PUBLISHED_DATE") }}</span>
            </th>
            <th></th>
            <th>
              <span class="category-isin">{{
                $t("DYNAMIC_HERO.HERO_ID")
              }}</span>
            </th>
            <th>
              <span>{{ $t("DYNAMIC_HERO.HERO_TYPE_TITLE") }}</span>
            </th>
            <th>
              <span>{{ $t("MODIFIED") }}</span>
            </th>
            <th v-if="!props.isCreateNewVisible" class="dynamic-hero-status">
              <span>{{ $t("COMMON.STATUS") }}</span>
            </th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-show="hero?.state != 'expired'"
            class="dynamic-hero-body"
            v-for="(hero, index) in props.dynamicHeroList"
            :class="{
              'change-background-color-for-selected':
                hero?.selectedText == $t('COMMON.SELECTED') ||
                hero?.selectedText == 'DELETE' ||
                hero?.selectedText == 'SCHEDULE' ||
                hero?.selectedText == 'UNSCHEDULE',
            }"
            :key="`hero${index}`"
          >
            <td
              :class="{
                'add-opacity-to-create-new': hero?.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-dates-ids"
            >
              <div class="dynamic-hero-details">
                <span :class="hero?.publishDate ? 'publishdate' : ''">
                  {{ hero?.publishDate ? convertTimeStamp?.(hero?.publishDate) : "no date" }}
                </span>
              </div>
            </td>
            <td
              :class="{
                'add-opacity-to-create-new': hero?.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-image-container"
            >
              <div class="dynamic-hero-image">
                <img
                  alt="hero"
                  class="image"
                  :src="hero?.image || defaultImage"
                  @error="$event.target.src = `${defaultImage}`"
                />
              </div>
            </td>
            <td
              :class="{
                'add-opacity-to-create-new': hero?.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-dates-ids"
            >
              <div class="dynamic-hero-details">
                {{ hero?.uuid || "" }}
              </div>
            </td>
            <td
              :class="{
                'add-opacity-to-create-new': hero?.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-title"
            >
              <div
                class="dynamic-hero-status-main-section"
                v-for="type in ['news', 'quiz', 'event', 'content', 'advice']"
                :key="type"
              >
                <div
                  v-if="hero?.template == type"
                  :class="`dynamic-hero-type ${type}-type`"
                >
                  <span>{{
                    $t(type.charAt(0).toUpperCase() + type.slice(1))
                  }}</span>
                </div>
                <div
                  v-if="hero?.template == type && hero?.preview"
                  class="dynamic-hero-type preview-type"
                >
                  <span class="preview-text">{{
                    $t("BUTTONS.PREVIEW_BUTTON")
                  }}</span>
                </div>
              </div>

              <div class="dynamic-hero-title-text">
                {{ hero?.title || "" }}
              </div>
            </td>
            <td
              :class="{
                'add-opacity-to-create-new': hero.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-dates-ids dynamic-hero-date-container-ids"
            >
              <div class="dynamic-hero-details">
                <span>{{ convertTimeStamp?.(hero?.lastUpdate) || "" }}</span>
              </div>
            </td>
            <td
              v-if="false"
              :class="{
                'add-opacity-to-create-new': hero.selectedText == $t('DYNAMIC_HERO.CREATE_NEW'),
              }"
              class="dynamic-hero-dates-ids"
            >
              <div class="dynamic-hero-details">
                <span></span>
              </div>
            </td>
            <td class="dynamic-hero-state-menu-option">
              <div
                v-if="!props.isCreateNewVisible"
                class="dynamic-hero-state-container"
              >
                <div :class="getStateClass(hero)" v-if="hero?.state">
                  <span>
                    <img
                      :alt="hero?.state || 'state'"
                      :src="getStateIcon(hero)"
                    />
                  </span>
                </div>
              </div>

              <div v-if="hero.selectedText">
                <button
                  v-if="
                    [$t('COMMON.SELECT'), $t('COMMON.SELECTED'), $t('DYNAMIC_HERO.CREATE_NEW')].includes(
                      hero.selectedText
                    )
                  "
                  :type="'button'"
                  :class="[
                    hero.selectedText === $t('COMMON.SELECT') ? 'select-btn' : '',
                    hero.selectedText === $t('COMMON.SELECTED') ? 'selected-btn' : '',
                    hero.selectedText === $t('DYNAMIC_HERO.CREATE_NEW') ? 'create-new-btn' : '',
                    hero.selectedText !== $t('COMMON.SELECTED') && isSelected ? 'selectedtrue' : ''
                  ]"
                  @click="buttonClickHandler(hero, index)"
                >
                  {{ hero.selectedText }}
                </button>
              </div>
            </td>

            <td class="dynamic-hero-menu">
              <div
                v-if="!props.isCreateNewVisible"
                :class="[
                  'dynamic-hero-menu-container',
                  hero.dropDown ? 'dynamic-hero-menu-selected' : '',
                ]"
                @click="props.displayOption(hero)"
              >
                <img
                  alt=""
                  v-if="hero.dropDown"
                  class="dynamic-hero-table-edit-btn"
                  src="~/assets/images/green-edit-btn.svg?skipsvgo=true"
                />
                <img
                  alt=""
                  v-if="!hero.dropDown"
                  class="dynamic-hero-table-edit-btn"
                  src="~/assets/images/edit-btn.svg?skipsvgo=true"
                />
              </div>

              <div v-if="hero.dropDown" class="dynamic-hero-menu-box">
                <ul class="dynamic-hero-menu-list">
                  <li
                    v-for="(item, index) in getMenuItems(hero)"
                    :key="index"
                    @click="item.action"
                  >
                    {{ item.label }}
                  </li>
                </ul>
              </div>
            </td>
          </tr>
          <tr
            v-if="props.expiredHeroCount > 0"
            class="view-expire-hero-container"
          >
            <div :class="computedExpireTextClass" @click="props.toggleText">
              {{ expireText }}
            </div>
          </tr>
          <tr
            class="dynamic-hero-body"
            v-for="(hero, index) in props.dynamicHeroList"
            :key="`expiredHero${index}`"
            :class="{
              'change-background-color-for-selected':
                hero.selectedText == 'DELETE',
            }"
            v-show="
              props.isShowList &&
              hero?.state === 'expired' &&
              props.expiredHeroCount > 0
            "
          >
            <td class="dynamic-hero-dates-ids">
              <div class="dynamic-hero-details">
                <span class="publishdate">{{
                  hero?.publishDate ? convertTimeStamp(hero.publishDate) : ""
                }}</span>
              </div>
            </td>
            <td class="dynamic-hero-image-container">
              <div class="dynamic-hero-image">
                <img
                  alt="hero"
                  class="image"
                  :src="hero?.image || props.defaultImage"
                  @error="$event.target.src = props.defaultImage"
                />
              </div>
            </td>
            <td class="dynamic-hero-dates-ids">
              <div class="dynamic-hero-details">
                {{ hero?.uuid || "" }}
              </div>
            </td>
            <td class="dynamic-hero-title">
              <div
                v-if="hero?.template"
                :class="`dynamic-hero-type ${hero.template}-type`"
              >
                <span>{{
                  hero.template.charAt(0).toUpperCase() + hero.template.slice(1)
                }}</span>
              </div>
              <div class="dynamic-hero-title-text">
                {{ hero?.title || "" }}
              </div>
            </td>
            <td class="dynamic-hero-dates-ids dynamic-hero-date-container-ids">
              <div class="dynamic-hero-details">
                <span>{{
                  hero?.lastUpdate ? convertTimeStamp(hero.lastUpdate) : ""
                }}</span>
              </div>
            </td>
            <td v-if="false" class="dynamic-hero-dates-ids">
              <div class="dynamic-hero-details">
                <span></span>
              </div>
            </td>
            <td class="dynamic-hero-state-menu-option">
              <div v-if="hero?.state" class="dynamic-expired-state">
                <span><img alt="" src="@/assets/images/Expired.png" /></span>
              </div>
            </td>
            <td class="dynamic-hero-menu">
              <div
                :class="
                  hero.dropDown
                    ? 'dynamic-hero-menu-container dynamic-expired-hero-menu-selected dynamic-hero-menu-selected'
                    : 'dynamic-hero-menu-container'
                "
                @click="props.displayOption(hero)"
              >
                <img
                  alt=""
                  v-if="hero.dropDown"
                  class="dynamic-hero-table-edit-btn"
                  src="~/assets/images/green-edit-btn.svg?skipsvgo=true"
                />
                <img
                  alt=""
                  v-if="!hero.dropDown"
                  class="dynamic-hero-table-edit-btn"
                  src="~/assets/images/edit-btn.svg?skipsvgo=true"
                />
              </div>
              <div class="dynamic-hero-menu-box" v-if="hero.dropDown">
                <ul
                  class="dynamic-hero-menu-list"
                  v-show="hero.state == 'expired'"
                >
                  <li
                    @click="
                      props.navigateToCreateDuplicate(hero.template, hero.uuid)
                    "
                  >
                    Create duplicate
                  </li>
                  <li
                    v-if="!hero.currentDateExpired"
                    @click="props.deletePopUpOpen(hero.uuid)"
                  >
                    Delete
                  </li>
                </ul>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import scheduled from "@/assets/images/scheduled-icon.png";
import live from "@/assets/images/live-icon.png";
import draft from "@/assets/images/draft-icon.png";
import { useI18n } from "vue-i18n";
const { convertTimeStamp } = useTimeUtils();

const { t } = useI18n();

const props = defineProps({
  isCreateNewVisible: Boolean,
  dynamicHeroList: Array,
  defaultImage: String,
  selectLiveHeroOnSelect: Function,
  selectLiveHeroOnSelected: Function,
  openNewHeroPopup: Function,
  displayOption: Function,
  navigateToEditPage: Function,
  navigateToSelectLiveHero: Function,
  navigateToCreateDuplicate: Function,
  unScheduleHeroes: Function,
  deletePopUpOpen: Function,
  scheduleHeroes: Function,
  expiredHeroCount: Number,
  toggleText: Function,
  isShowList: Boolean,
  isSelected: Boolean,
});

const computedExpireTextClass = computed(() =>
  props.isCreateNewVisible ? "text-expire disable-expired-text" : "text-expire"
);

const expireText = computed(() =>
  props.isShowList ? t("DYNAMIC_HERO.HIDE_EXPIRED_HEROES") : t("DYNAMIC_HERO.VIEW_EXPIRED_HEROES")
);

const buttonClickHandler = (hero, index) => {
  if (hero.selectedText === t("COMMON.SELECT")) {
    props.selectLiveHeroOnSelect(hero, index);
  } else if (hero.selectedText === t("COMMON.SELECTED")) {
    props.selectLiveHeroOnSelected(hero, index);
  } else if (hero.selectedText === t("DYNAMIC_HERO.CREATE_NEW")) {
    props.openNewHeroPopup(hero);
  }
};

const getStateClass = (hero) => {
  if (!hero) return "";
  const { state, publishDate } = hero;
  if (state === "scheduled") return "dynamic-hero-scheduled-state";
  if (state === "live") return "dynamic-hero-live-state";
  if (state === "draft") return "dynamic-hero-draft-state";
  if (state === "preview") {
    return publishDate
      ? "dynamic-hero-scheduled-state"
      : "dynamic-hero-draft-state";
  }
  return "";
};

const getStateIcon = (hero) => {
  if (!hero) return "";
  const { state, publishDate } = hero;
  if (state === "scheduled") return scheduled;
  if (state === "live") return live;
  if (state === "draft") return draft;
  if (state === "preview") {
    return publishDate ? scheduled : draft;
  }
  return "";
};

const getMenuItems = (hero) => {
  const stateItems = {
    live: [
      {
        label: t("BUTTONS.EDIT_BUTTON"),
        action: () => props.navigateToEditPage(hero.template, hero.uuid),
      },
      {
        label: "Replace live hero",
        action: () => props.navigateToSelectLiveHero(hero),
      },
      {
        label: "Create duplicate",
        action: () => props.navigateToCreateDuplicate(hero.template, hero.uuid),
      },
    ],
    scheduled: [
      { label: "Unschedule", action: () => props.unScheduleHeroes(hero.uuid) },
      {
        label: t("BUTTONS.EDIT_BUTTON"),
        action: () => props.navigateToEditPage(hero.template, hero.uuid),
      },
      { label: "Delete", action: () => props.deletePopUpOpen(hero.uuid) },
      {
        label: "Create duplicate",
        action: () => props.navigateToCreateDuplicate(hero.template, hero.uuid),
      },
    ],
    draft: [
      { label: "Schedule", action: () => props.scheduleHeroes(hero.uuid) },
      {
        label: t("BUTTONS.EDIT_BUTTON"),
        action: () => props.navigateToEditPage(hero.template, hero.uuid),
      },
      { label: "Delete", action: () => props.deletePopUpOpen(hero.uuid) },
      {
        label: "Create duplicate",
        action: () => props.navigateToCreateDuplicate(hero.template, hero.uuid),
      },
    ],
    expired: hero.currentDateExpired
      ? []
      : [
          {
            label: "Create duplicate",
            action: () =>
              props.navigateToCreateDuplicate(hero.template, hero.uuid),
          },
          { label: "Delete", action: () => props.deletePopUpOpen(hero.uuid) },
        ],
    preview: hero.publishDate
      ? [
          {
            label: "Unschedule",
            action: () => props.unScheduleHeroes(hero.uuid),
          },
        ]
      : [{ label: "Schedule", action: () => props.scheduleHeroes(hero.uuid) }],
  };

  return [...(stateItems[hero.state] || [])];
};
</script>
