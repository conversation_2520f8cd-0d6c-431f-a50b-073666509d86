<template>
  <div v-if="!isPageLoading" class="form-container-dynamic">
    <div class="recipe-variant-notes-text-container-dynamic">
      <div class="form-title-dynamic">
        <p class="form-title-header-dynamic">
          Text<span class="compulsory-field">*</span>
        </p>
      </div>
      <div v-if="!isLiveHero || showPreviewToggle" class="preview-section-dynamic">
        <div class="text-section-prev">
          {{ $t("DYNAMIC_HERO.HERO_PREVIEW") }}
        </div>
        <label class="switch"
          :class="{
            'simple-data-tooltip edge': !isHeroPreview
          }"
          :data-tooltip-text="!isHeroPreview && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
        >
          <input
            @change="handleHeroPreviewToggle"
            :checked="isHeroPreview"
            type="checkbox"
          />
          <span class="slider-round"></span>
        </label>
      </div>
    </div>

    <div class="description-section-dynamic" id="news_section_scroll">
      <textarea
        @input="updateNewsText"
        class="description-notes-dynamic"
        id="newsTextField"
        v-model="newsTextInput"
        maxlength="244"
      ></textarea>
      <div v-if="newsTextInput.length" class="description-length">
        {{ newsTextInput.length }}/244
      </div>
    </div>

    <div class="cta-main-container">
      <div class="cta-heading-text">CTA:</div>
      <div class="cta-input-and-url-section">
        <div class="cta-input-section">
          <div class="cta-input-area">
            <input
              @input="updateNewsCTA"
              type="text"
              maxlength="40"
              id="newsCTAField"
              v-model="newsCTAInput"
              class="cta-input-text"
              placeholder="Enter text"
              autocomplete="off"
            />
            <div v-if="newsCTAInput.length" class="cta-input-length">
              {{ newsCTAInput.length }}/40
            </div>
          </div>
        </div>

        <div class="cta-url-section">
          <div class="input-section">
            <input
              class="input-text-area"
              id="newsCTALinkField"
              :value="newsCTALinkText"
              autocomplete="off"
              @input="updateCTALink"
              placeholder="Enter link"
            />
            <div
              v-if="!isCTALinkValid && isCTALinkBroken"
              class="cta-broken-link-validation-section"
            >
              <span class="cta-link-broken-message">This link is broken.</span>
            </div>
          </div>
          <div class="cta-link-input-verify-section">
            <div v-if="props.isCTALinkInProgress" class="cta-link-progress-check">
              <div class="loader-image"></div>
            </div>
            <div class="cta-link-correct-check link-image-check">
              <img
                v-if="!props.isCTALinkInProgress && props.isCTALinkValid"
                class="correct-icon"
                alt=""
                src="@/assets/images/tick-icon.png"
              />
            </div>
            <div class="cta-link-wrong-check link-image-check">
              <img
                v-if="
                  !props.isCTALinkInProgress &&
                  !props.isCTALinkValid &&
                  props.isCTALinkBroken
                "
                class="wrong-icon"
                alt="info"
                src="@/assets/images/red-info.svg?skipsvgo=true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="info-section">
      <span class="compulsory-field">*</span> Required data for news form
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  isPageLoading: Boolean,
  isLiveHero: Boolean,
  showPreviewToggle: Boolean,
  newsText: String,
  newsCTAInput: String,
  newsCTALinkText: String,
  isHeroPreview: Boolean,
  isCTALinkValid: Boolean,
  isCTALinkBroken: Boolean,
  isCTALinkInProgress: Boolean,
});

const emit = defineEmits([
  "update:newsText",
  "update:newsCTAInput",
  "update:newsCTALinkText",
  "update:isHeroPreview",
  "validate-cta-link",
]);

const newsTextInput = ref(props.newsText);
const newsCTAInput = ref(props.newsCTAInput);

const updateNewsText = (event) => {
  emit("update:newsText", newsTextInput.value);
};

const updateNewsCTA = (event) => {
  emit("update:newsCTAInput", newsCTAInput.value);
};

const updateCTALink = (event) => {
  const newValue = event.target.value;
  emit("update:newsCTALinkText", newValue);
  emit("validate-cta-link");
};

const handleHeroPreviewToggle = () => {
  emit("update:isHeroPreview", !props.isHeroPreview);
};
</script>
