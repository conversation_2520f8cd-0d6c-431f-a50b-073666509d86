<template>
  <div class="edit-form-container-dynamic">
    <div class="recipe-variant-notes-text-container-dynamic">
      <div class="form-title-dynamic">
        <p class="form-title-header-dynamic">
          Text<span class="compulsory-field">*</span>
        </p>
      </div>
      <div v-if="!props.isLiveHero" class="preview-section-dynamic">
        <div class="text-section-prev">
          {{ $t("DYNAMIC_HERO.HERO_PREVIEW") }}
        </div>
        <label
          class="switch"
          :class="{
            'simple-data-tooltip edge': !isHeroPreview
          }"
          :data-tooltip-text="!isHeroPreview && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
        >
          <input
            @change="onToggleHeroPreview"
            :checked="isHeroPreview"
            type="checkbox"
          />
          <span class="slider-round"></span>
        </label>
      </div>
    </div>

    <div class="description-section-dynamic" id="nutrition_section_scroll">
      <textarea
        @input="onInputDescription"
        class="description-notes-dynamic"
        id="descriptionNotes"
        :maxlength="descriptionMaxLength"
        v-model="description"
      ></textarea>
      <div v-if="description.trim().length" class="description-length">
        {{ description.length }}/{{ descriptionMaxLength }}
      </div>
    </div>

    <div class="event-details">
      <div class="sub-title-container">
        <p class="sub-title-header">
          Subtext <span class="asterisk-input">*</span>
        </p>
        <textarea
          @input="onInputSubtext"
          id="eventSubText"
          class="sub-text"
          :maxlength="subtextMaxLength"
          v-model="subtext"
        ></textarea>
        <div v-if="subtext.length" class="sub-text-count">
          {{ subtext.length }}/{{ subtextMaxLength }}
        </div>
      </div>

      <div class="event-container">
        <p class="sub-title-header">
          Event date:<span class="asterisk-input">*</span>
        </p>
        <div class="sub-text">
          <input
            @input="onInputEventDate"
            id="eventDates"
            class="input"
            :maxlength="eventDateMaxLength"
            v-model="eventDate"
          />
        </div>
        <div v-if="eventDate.length" class="sub-text-count">
          {{ eventDate.length }}/{{ eventDateMaxLength }}
        </div>
      </div>
    </div>

    <div class="cta-main-container">
      <div class="cta-heading-text">CTA:</div>
      <div class="cta-input-and-url-section">
        <div class="cta-input-section">
          <div class="cta-input-area">
            <input
              @input="onInputCtaText"
              type="text"
              class="cta-input-text"
              id="newsCTAField"
              :maxlength="ctaMaxLength"
              v-model="ctaText"
              placeholder="Enter text"
              autocomplete="off"
            />
            <div v-if="ctaText.length" class="cta-input-length">
              {{ ctaText.length }}/{{ ctaMaxLength }}
            </div>
          </div>
        </div>
        <div class="cta-url-section">
          <div class="input-section">
            <input
              class="input-text-area"
              id="newsCTALinkField"
              v-model="ctaLink"
              autocomplete="off"
              @input="validateCtaLink"
              placeholder="Enter link"
            />
            <div
              v-if="!isCTALinkIsValid && isCTALinkBroken"
              class="cta-broken-link-validation-section"
            >
              <span class="cta-link-broken-message">This link is broken.</span>
            </div>
          </div>
          <div class="cta-link-input-verify-section">
            <div
              v-if="props.isCTALinkInprogress"
              class="cta-link-progress-check"
            >
              <div class="loader-image"></div>
            </div>
            <div class="cta-link-correct-check link-image-check">
              <img
                v-if="!props.isCTALinkInprogress && isCTALinkIsValid"
                class="correct-icon"
                alt=""
                src="@/assets/images/tick-icon.png"
              />
            </div>
            <div class="cta-link-wrong-check link-image-check">
              <img
                v-if="
                  !props.isCTALinkInprogress &&
                  !isCTALinkIsValid &&
                  isCTALinkBroken
                "
                class="wrong-icon"
                alt=""
                src="@/assets/images/red-info.svg?skipsvgo=true"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  isHeroPreview: { type: Boolean, default: false },
  description: { type: String, default: "" },
  descriptionMaxLength: { type: Number, default: 244 },
  subtext: { type: String, default: "" },
  subtextMaxLength: { type: Number, default: 50 },
  eventDate: { type: String, default: "" },
  eventDateMaxLength: { type: Number, default: 50 },
  ctaText: { type: String, default: "" },
  ctaMaxLength: { type: Number, default: 40 },
  ctaLink: { type: String, default: "" },
  isCTALinkBroken: { type: Boolean, default: false },
  isCTALinkInprogress: { type: Boolean, default: false },
  isCTALinkIsValid: { type: Boolean, default: false },
  isLiveHero: { type: Boolean, default: false },
});

const emit = defineEmits([
  "update:isHeroPreview",
  "update:description",
  "update:subtext",
  "update:eventDate",
  "update:ctaText",
  "update:ctaLink",
  "update:eventCTALinkText",
  "validate-cta-link",
]);

const description = ref(props.description);
const subtext = ref(props.subtext);
const eventDate = ref(props.eventDate);
const ctaText = ref(props.ctaText);
const ctaLink = ref(props.ctaLink);

const onToggleHeroPreview = () => {
  emit("update:isHeroPreview", !props.isHeroPreview);
};

const onInputDescription = (e) => {
  emit("update:description", description.value);
};

const onInputSubtext = (e) => {
  emit("update:subtext", subtext.value);
};

const onInputEventDate = (e) => {
  emit("update:eventDate", eventDate.value);
};

const onInputCtaText = (e) => {
  emit("update:ctaText", ctaText.value);
};

const validateCtaLink = (e) => {
  const newValue = e.target.value;
  emit("update:eventCTALinkText", newValue);
  emit("validate-cta-link");
};
</script>
