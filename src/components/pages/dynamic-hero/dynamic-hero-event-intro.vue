<template>
  <div :class="containerClass">
    <div class="main-section">
      <div>
        <img
          alt="event"
          :class="logoClass"
          src="@/assets/images/event-icon.png"
        />
      </div>
      <div :class="headingClass">{{ $t("DYNAMIC_HERO.EVENT_FORM") }}</div>
    </div>
    <div class="news-date-picker-container">
      <div class="start-date-text">{{ startDateLabel }}</div>
      <CalendarPicker
        :model-value="selectedDate"
        :isRange="false"
        :markers="markers"
        :disabled-dates="disabledDates"
        @update:model-value="handleDateClick"
        :isHeroLive="isLiveHero"
        :isLiveHeroReplaced="isLiveHeroReplaced"
      />
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  containerClass: {
    type: String,
    required: true,
  },
  startDateLabel: {
    type: String,
    required: true,
  },
  selectedDate: {
    type: [Date, Array, String, Object],
    default: null
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  markers: {
    type: Array,
    default: () => [],
  },
  headingClass: {
    type: String,
    required: true,
  },
  logoClass: {
    type: String,
    required: true,
  },
  isLiveHeroReplaced: {
    type: Boolean,
    required: true,
  },
  isLiveHero: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(["update:selectedDate", "date-click", "open-calendar"]);

const handleDateClick = (date) => {
  emit("update:selectedDate", date);
  emit("date-click", date);
};
</script>
