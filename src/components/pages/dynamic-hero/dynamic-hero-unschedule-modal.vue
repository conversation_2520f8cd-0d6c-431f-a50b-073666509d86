<template>
  <Modal v-if="isVisible" @close="closeModal">
    <template #selectGroupType>
      <div class="select-schedule-date-hero-modal">
        <div class="error-delete-hero-modal-content">
          <div class="select-schedule-date-main-container">
            <div class="error-cross-image-div">
              <img
                class="error-cross-image"
                src="@/assets/images/quiz-form.png"
                alt="cross"
              />
            </div>
            <div class="select-schedule-date-hero-modal-heading">
              <span class="unable-schedule-text">
                Please confirm unscheduling
                <span class="capitalize-the-word">
                  {{ dataForSelectedHero.template }}
                </span>
                form
              </span>
              <div class="error-delete-schedule-date-hero-modal-sub">
                <span class="quiz-draft-sub-heading">
                  <span class="capitalize-the-word">
                    {{ dataForSelectedHero.template }}
                  </span>
                  form will be moved to a draft.
                </span>
              </div>
            </div>
          </div>
          <div class="replacement-hero-modal-btn-container">
            <button
              class="select-schedule-date-hero-cancel-btn"
              @click="closeModal"
            >
              {{ $t("BUTTONS.CANCEL_BUTTON") }}
            </button>
            <button
              class="select-schedule-date-hero-confirm-btn"
              @click="unpatchScheduledHero()"
            >
              {{ $t("BUTTONS.CONFIRM_BUTTON") }}
            </button>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script setup>
const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  dataForSelectedHero: {
    type: Object,
    required: true,
  },
  unpatchScheduledHero: {
    type: Function,
    required: true,
  },
});

const emit = defineEmits(["close"]);

const closeModal = () => {
  emit("close");
};
</script>
