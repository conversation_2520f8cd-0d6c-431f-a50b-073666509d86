<template>
  <div class="recipe-technical-issue-modal color-graphite-gray">
    <div class="font-size-20 font-bold">
      {{ $t('TECHNICAL_ISSUES.TECHNICAL_HEADER') }}
    </div>

    <p v-if="hasUnpulishedDateTimeout">{{ $t('TECHNICAL_ISSUES.TECHNICAL_UNPUBLISH_TITLE') }}</p>
    <p v-if="hasPulishedDateTimeout">{{ $t('TECHNICAL_ISSUES.TECHNICAL_PUBLISH_TITLE') }}</p>

    <ol v-if="hasUnpulishedDateTimeout">
      <li>{{ $t('TECHNICAL_ISSUES.TECHNICAL_UNPUBLISH_DESC') }}</li>
      <li>
        {{ $t('TECHNICAL_ISSUES.CONTACT') }}
        <a :href="supportEmailLink"><b>{{ $t('TECHNICAL_ISSUES.CMS_SUPPORT_EMAIL') }}</b></a>
      </li>
    </ol>
    <ol v-if="hasPulishedDateTimeout">
      <li>
        {{ $t('TECHNICAL_ISSUES.CONTACT') }}
        <a :href="supportEmailLink"><b>{{ $t('TECHNICAL_ISSUES.CMS_SUPPORT_EMAIL') }}</b></a>
      </li>
      <li>{{ $t('TECHNICAL_ISSUES.CLEAR_SCHEDULE') }}</li>
      <li>{{ $t('TECHNICAL_ISSUES.CLEAR_SCHEDULE_DESC') }}</li>
    </ol>
    <button
      type="button"
      class="btn-green-outline"
      @click="closeAction()"
      data-test-id="cancel-button"
    >
      {{ $t('BUTTONS.CLOSE_BUTTON') }}
    </button>
  </div>
</template>

<script setup>

import { useI18n } from "vue-i18n";

defineProps({
  hasUnpulishedDateTimeout: {
    type: Boolean,
    default: false,
  },
  hasPulishedDateTimeout: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close"]);

const { t } = useI18n();

const closeAction = () => emit("close", false);

const supportEmailLink = computed(() => `mailto:${t('TECHNICAL_ISSUES.CMS_SUPPORT_EMAIL')}`);
</script>
