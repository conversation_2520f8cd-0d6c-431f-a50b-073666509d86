<template>
  <content-wrapper wrapper-classes="main" :is-body-loading="isLoading">
    <page-top-block
      v-if="!isLoading"
      :hintBlack="bannerUUID && !isCreateDuplicate ? `ID: ${bannerUUID}` : ''"
      :page-actions-back-path="'/banner'"
      :hasBackgroundImage="false"
      :page-actions-back-label="$t('BANNER.BACK_TO_BANNER_LIST')"
      :page-actions-is-continue-disabled="!enableButton()"
      :page-actions-continue-label="
        bannerState === 'live' ? $t('BUTTONS.UPDATE') : $t('BUTTONS.CONTINUE')
      "
      @page-actions-continue="openSavePopup"
      @page-actions-cancel="() => router.push('/banner')"
    >
      <div class="add-banner-form-hero">
        <div class="banner-main-content">
          <page-header-with-calendar
            title="Banner Form"
            :icon-src="headerIcon"
            icon-alt="banner icon"
            v-model:range="range"
            :start-date="startDate"
            :end-date="endDate"
            :markers="markers"
            :disabled-dates="disabledDates"
            :banner-is-live="bannerIsLive"
            :is-create-duplicate="isCreateDuplicate"
            :is-in-banner="true"
            :disableFutureDates="disableFutureDates"
            @date-change="handleDateChange"
          />
          <div class="banner-image-input-container">
            <div class="add-banner-input">
              <div class="banner-item-details-top">
                <name-field
                  v-model:name="bannerName"
                  input-id="banner-name"
                  :input-placeholder="'Banner name in CMS'"
                  @update:name="[trackChanges(), forceUpdate()]"
                />
                <div
                  v-if="!bannerIsLive || isCreateDuplicate"
                  class="schedule-btn"
                  :class="{
                    'simple-data-tooltip edge':
                      bannerStartDate === 0 && bannerEndDate === 0,
                  }"
                  :data-tooltip-text="
                    bannerStartDate === 0 &&
                    bannerEndDate === 0 &&
                    bannerScheduleTooltip
                  "
                >
                  <button-with-switcher
                    label="Schedule"
                    :is-checked="
                      bannerStartDate != 0 && bannerEndDate != 0 && eventStatus
                    "
                    :is-disabled="bannerStartDate == 0 && bannerEndDate == 0"
                    @action="toggleEventStatus"
                  />
                </div>
              </div>
              <div class="banner-head-section">
                <div class="type-of-banner-section">
                  <div class="banner-heading">
                    <span class="banner-heading-text"
                      >Type of banner:
                      <span class="compulsory-field">*</span>
                    </span>
                  </div>
                  <div class="banner-radio-button-container">
                    <div
                      class="banner-radio-button-section"
                      v-for="(data, index) in contentList"
                      :key="index"
                      @click.stop="selectContent($event, data)"
                    >
                      <label class="control-radio control-radio-20">
                        <input
                          :checked="data.isChecked"
                          :id="'select-content-' + index"
                          type="radio"
                          @click.stop.prevent
                        />
                        <span class="checkmark"></span>
                        <span class="banner-result-text">{{ data.name }}</span>
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  v-if="!isBannerFormNew && !isCreateDuplicate"
                  class="delete-banner-section"
                >
                  <div
                    class="delete-banner-button"
                    @click="deleteBannerPopup()"
                  >
                    <img
                      alt=""
                      src="@/assets/images/delete-icon.png"
                      width="15"
                      height="16"
                    />
                    <span>{{ $t("BANNER.DELETE_BANNER") }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </page-top-block>

    <simple-content-wrapper class="article-item-pages-wrapper">
      <banner-form-content
        :banner-is-live="bannerIsLive"
        :is-create-duplicate="isCreateDuplicate"
        v-model:banner-preview="bannerPreview"
        :banner-toggle-tooltip="bannerToggleTooltip"
        :selected-type="selectedType"
        :max-length-texts="maxLengthTexts"
        :max-length-c-t-a="maxLengthCTA"
        :max-text1-length="maxText1Length"
        :max-text2-length="maxText2Length"
        :initial-values="currentOption"
        @update:values="updateCurrentOption"
        @input-change="handleFormContentChange"
      />
    </simple-content-wrapper>
  </content-wrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { storeToRefs } from "pinia";
import { useNuxtApp } from "#app";
import { useStore } from "vuex";

import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import PageTopBlock from "@/components/page-top-block.vue";
import ConfirmModal from "@/components/modals/confirm-modal.vue";
import ProcessModal from "@/components/modals/process-modal.vue";
import BannerScheduleModal from "@/components/pages/banner/banner-schedule-modal.vue";
import NameField from "@/components/name-field.vue";
import ButtonWithSwitcher from "@/components/button-with-switcher.vue";
import PageHeaderWithCalendar from "@/components/page-header-with-calendar.vue";
import SimpleContentWrapper from "@/components/simple-content-wrapper.vue";
import BannerFormContent from "@/components/pages/banner/banner-form-content.vue";
import { useBaseModal } from "@/composables/useBaseModal";
import { CONFIRM_MODAL_TYPE } from "@/models/confirm-modal.model";
import { PROCESS_MODAL_TYPE } from "@/models/process-modal.model";

import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useTimeUtils } from "~/composables/useTimeUtils";
import { useBannerStore } from "@/stores/banner";
import { useProjectLang } from "@/composables/useProjectLang";
import headerIcon from "@/assets/images/banner_icon.png";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const { $t, $keys } = useNuxtApp();
const { triggerLoading, useCalendarMarkers } = useCommonUtils();
const { formatDateRangeOrSingle } = useTimeUtils();
const bannerStore = useBannerStore();
const { editBannerData, bannerList } = storeToRefs(bannerStore);
const store = useStore();
const { readyProject } = useProjectLang();
const lang = ref("");

const { openModal, closeModal } = useBaseModal({
  BannerDraftModal: ConfirmModal,
  BannerTypeChangeModal: {
    component: ConfirmModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
  BannerScheduleModal: {
    component: BannerScheduleModal,
    skipClickOutside: false,
    skipEscapeClick: false,
  },
  BannerPageProcessModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});

const bannerUUID = ref("");
const bannerName = ref("");
const bannerState = ref("");
const bannerIsLive = ref(false);
const bannerPreview = ref(false);
const isBannerFormNew = ref(true);
const isCreateDuplicate = ref(false);

const eventStatus = ref(false);
const isCampaignModified = ref(false);
const isInitializing = ref(true);
const selectedType = ref("all_text");
const bannerStartDate = ref(0);
const bannerEndDate = ref(0);
const startDate = ref("");
const endDate = ref("");
const pendingContentSelection = ref(null);
const range = ref({
  start: null,
  end: null,
});
const disabledDates = ref([]);
const disableList = ref([]);
const disableFutureDates = ref(false);
const maxLengthTexts = ref(42);
const maxLengthCTA = ref(20);
const maxText1Length = ref(42);
const maxText2Length = ref(42);
const bannerScheduleTooltip = ref(
  "Please select dates to schedule the banner."
);
const bannerToggleTooltip = ref(
  "Turn on Banner Preview to override the existing Banner in the test version of the app."
);
const contentList = ref([
  {
    name: "All Text",
    isChecked: true,
    type: "all_text",
  },
  {
    name: "Text & Button",
    isChecked: false,
    type: "text_button",
  },
]);
const currentOption = ref({
  description1: "",
  description2: "",
  bannerCTAInput: "",
  bannerCTALinkText: "",
  isCTALinkInprogress: false,
  isCTALinkIsValid: false,
  isCTALinkBroken: false,
});

const typeBackup = ref({
  all_text: null,
  text_button: null,
});

const { markers } = useCalendarMarkers(disableList, "rawTitle");

const populateDisableList = () => {
  disableList.value = [];
  disabledDates.value = [];

  if (bannerList.value && bannerList.value.length > 0) {
    bannerList.value.forEach((banner) => {
      if ((banner.state === "scheduled" || banner.state === "live") && banner.publishDate && banner.endDate) {
        if (bannerUUID.value && banner.uuid === bannerUUID.value) {
          return;
        }

        const startDisableDate = new Date(banner.publishDate * 1000);
        startDisableDate.setHours(0, 0, 0, 0);

        const endDisableDate = new Date(banner.endDate * 1000);
        endDisableDate.setHours(23, 59, 59, 999);

        const tempDisableDate = getBetweenDates(startDisableDate, endDisableDate);

        disabledDates.value.push(...tempDisableDate);

        tempDisableDate.forEach((date) => {
          disableList.value.push({
            date: new Date(date),
            title: banner.title || "",
            state: banner.state || "",
            uuid: banner.uuid,
          });
        });
      }
    });
  }
};

const getBetweenDates = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  return Array.from({ length: days }, (_, index) => {
    const currentDate = new Date(start);
    currentDate.setDate(start.getDate() + index);
    return currentDate;
  });
};

const checkForNextScheduledBanner = () => {
  if (bannerIsLive.value && !isCreateDuplicate.value) {
    const currentEndDate = bannerEndDate.value
      ? new Date(bannerEndDate.value * 1000)
      : null;

    if (currentEndDate) {
      const nextDay = new Date(currentEndDate);
      nextDay.setDate(nextDay.getDate() + 1);
      nextDay.setHours(0, 0, 0, 0);

      const nextScheduledBanner = disableList.value.find((item) => {
        if (!item.date) return false;

        const itemDate = new Date(item.date);
        itemDate.setHours(0, 0, 0, 0);

        return itemDate.getTime() === nextDay.getTime();
      });

      disableFutureDates.value = nextScheduledBanner ? currentEndDate : false;
    } else {
      disableFutureDates.value = false;
    }
  } else {
    disableFutureDates.value = false;
  }
};

const isFormValid = computed(() => {
  const hasName = bannerName.value.trim() !== "";
  const hasDescription1 = currentOption.value.description1.trim() !== "";
  const hasDescription2 = currentOption.value.description2.trim() !== "";

  if (selectedType.value === "all_text") {
    return hasName && hasDescription1 && hasDescription2;
  } else {
    const hasCTAInput = currentOption.value.bannerCTAInput.trim() !== "";
    const hasCTALink = currentOption.value.bannerCTALinkText.trim() !== "";
    const isLinkValid = currentOption.value.isCTALinkIsValid;
    const isLinkValidOrEmpty = !hasCTALink || isLinkValid;

    return hasName && hasDescription1 && hasDescription2 && hasCTAInput && isLinkValidOrEmpty;
  }
});

const hasChanges = computed(() => {
  return isCampaignModified.value;
});

const enableButton = () => {
  const formIsValid = isFormValid.value;
  if (isCreateDuplicate.value) {
    return true;
  }

  if (props.isEdit) {
    if (
      currentOption.value.bannerCTALinkText.trim() !== "" &&
      !currentOption.value.isCTALinkIsValid
    ) {
      return false;
    }
    const hasValidChanges = hasChanges.value && formIsValid;
    return hasValidChanges;
  } else {
    return formIsValid;
  }
};

const trackChanges = () => {
  if (props.isEdit && !isInitializing.value) {
    isCampaignModified.value = true;
  }
};

const forceUpdate = () => {
  if (isInitializing.value) {
    isInitializing.value = false;
  }
};

const toggleEventStatus = () => {
  eventStatus.value = !eventStatus.value;
  isCampaignModified.value = true;
  if (isInitializing.value) {
    isInitializing.value = false;
  }
};

const updateCurrentOption = (newValues) => {
  currentOption.value = { ...newValues };
  if (
    !(newValues.bannerCTALinkText.trim() !== "" &&
    !newValues.isCTALinkIsValid)
  ) {
    isCampaignModified.value = true;
  }

  if (isInitializing.value) {
    isInitializing.value = false;
  }
};

const handleFormContentChange = () => {
  if (
    !(currentOption.value.bannerCTALinkText.trim() !== "" &&
    !currentOption.value.isCTALinkIsValid)
  ) {
    trackChanges();
  }
  forceUpdate();
};

const formatDateToString = (date) => {
  if (!date) return "";
  const dateObj = date instanceof Date ? date : new Date(date);
  if (isNaN(dateObj.getTime())) {
    console.error('Invalid date provided to formatDateToString:', date);
    return "";
  }
  const formatted = formatDateRangeOrSingle(dateObj);
  return formatted;
};

const handleDateChange = (newRange) => {
  if (Array.isArray(newRange) && newRange.length === 2) {
    const [start, end] = newRange;

    if (start && end) {
      const startDateObj = new Date(start);
      startDateObj.setHours(0, 0, 0, 0);
      const endDateObj = new Date(end);
      endDateObj.setHours(23, 59, 59, 999);

      const startTimestamp = Math.floor(startDateObj.getTime() / 1000);
      const endTimestamp = Math.floor(endDateObj.getTime() / 1000);

      bannerStartDate.value = startTimestamp;
      bannerEndDate.value = endTimestamp;
      const formattedStartDate = formatDateToString(startDateObj);
      const formattedEndDate = formatDateToString(endDateObj);
      startDate.value = formattedStartDate;
      endDate.value = formattedEndDate;
      eventStatus.value = true;
      isCampaignModified.value = true;
      if (isInitializing.value) {
        isInitializing.value = false;
      }
    }
  } else if (newRange && typeof newRange === 'object' && newRange.start && newRange.end) {

    const startDateObj = new Date(newRange.start);
    startDateObj.setHours(0, 0, 0, 0);
    const endDateObj = new Date(newRange.end);
    endDateObj.setHours(23, 59, 59, 999);

    const startTimestamp = Math.floor(startDateObj.getTime() / 1000);
    const endTimestamp = Math.floor(endDateObj.getTime() / 1000);

    bannerStartDate.value = startTimestamp;
    bannerEndDate.value = endTimestamp;
    const formattedStartDate = formatDateToString(startDateObj);
    const formattedEndDate = formatDateToString(endDateObj);
    startDate.value = formattedStartDate;
    endDate.value = formattedEndDate;
    eventStatus.value = true;
    isCampaignModified.value = true;
    if (isInitializing.value) {
      isInitializing.value = false;
    }
  }
};

const handleSaveConfirmation = (state = null) => {
  openModal({
    name: "BannerPageProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.SAVING },
  });

  if (state) {
    bannerState.value = state;
  }

  isBannerFormNew.value || isCreateDuplicate.value
    ? postContentFormAsync()
    : patchContentFormAsync();
};

const openSavePopup = () => {
  if (!enableButton()) return;

  if (bannerState.value === 'live' && !isCreateDuplicate.value) {
    openModal({
      name: "BannerDraftModal",
      props: {
        title: "Please, confirm the updating live Banner",
        description: "",
        confirmBtnLabel: $t("BUTTONS.CONFIRM_BUTTON"),
        cancelBtnLabel: $t("BUTTONS.CANCEL_BUTTON"),
        modalType: CONFIRM_MODAL_TYPE.UPDATE_LIVE_BANNER,
      },
      onClose: (response) => {
        if (response) {
          handleSaveConfirmation();
        }
      },
    });
    return;
  }

  const shouldShowScheduleModal =
    eventStatus.value && bannerStartDate.value > 0 && bannerEndDate.value > 0;

  if (shouldShowScheduleModal) {
    const startDateObj = bannerStartDate.value
      ? new Date(bannerStartDate.value * 1000)
      : null;
    if (startDateObj) {
      startDateObj.setHours(0, 0, 0, 0);
    }

    const endDateObj = bannerEndDate.value
      ? new Date(bannerEndDate.value * 1000)
      : null;
    if (endDateObj) {
      endDateObj.setHours(23, 59, 59, 999);
    }

    const scheduleRange = {
      start: startDateObj,
      end: endDateObj,
    };

    openModal({
      name: "BannerScheduleModal",
      props: {
        range: scheduleRange,
        startDate: startDate.value || formatDateRangeOrSingle(scheduleRange.start),
        endDate: endDate.value || formatDateRangeOrSingle(scheduleRange.end),
        markers: Array.isArray(markers) ? markers : markers.value || [],
        disabledDates: disabledDates.value,
        disableFutureDates: disableFutureDates.value,
        onDateChange: handleDateChange,
      },
      onClose: (response) => {
        if (response && bannerStartDate.value && bannerEndDate.value) {
          handleSaveConfirmation("scheduled");
        }
      },
    });
    return;
  }

  openModal({
    name: "BannerDraftModal",
    props: {
      title: "Do you want to save as draft your Banner form?",
      description: "",
      confirmBtnLabel: $t("BUTTONS.SAVE_BUTTON"),
      cancelBtnLabel: $t("BUTTONS.CANCEL_BUTTON"),
      modalType: CONFIRM_MODAL_TYPE.DRAFT,
    },
    onClose: (response) => {
      if (response) {
        handleSaveConfirmation();
      }
    },
  });
};

const hasFormData = () => {
  const hasDescription1 = currentOption.value.description1.trim() !== "";
  const hasDescription2 = currentOption.value.description2.trim() !== "";
  const hasCTAInput = currentOption.value.bannerCTAInput.trim() !== "";
  const hasCTALink = currentOption.value.bannerCTALinkText.trim() !== "";

  return (
    hasDescription1 ||
    hasDescription2 ||
    hasCTAInput ||
    hasCTALink
  );
};

const applyContentSelection = (data) => {
  const currentType = selectedType.value;
  const targetType = data.type;
  typeBackup.value[currentType] = { ...currentOption.value };
  contentList.value.forEach((item) => {
    item.isChecked = item.name === data.name;
  });
  selectedType.value = targetType;
  if (typeBackup.value[targetType]) {
    Object.assign(currentOption.value, typeBackup.value[targetType]);
  } else {
    currentOption.value.description1 = "";
    currentOption.value.description2 = "";
    currentOption.value.bannerCTAInput = "";
    currentOption.value.bannerCTALinkText = "";
    currentOption.value.isCTALinkInprogress = false;
    currentOption.value.isCTALinkIsValid = false;
    currentOption.value.isCTALinkBroken = false;
  }

  trackChanges();
};

const selectContent = (_, data) => {
  if (selectedType.value === data.type) {
    return;
  }
  const hasData = hasFormData();

  if (hasData) {
    pendingContentSelection.value = data;
    openModal({
      name: "BannerTypeChangeModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.BANNER_TYPE_REPLACE,
        title: "Please, confirm the replacement type of banner.",
        descriptionRed: "Current data of banner will be lost.",
      },
      onClose: (response) => {
        if (response) {
          applyContentSelection(pendingContentSelection.value);
        } else {
          contentList.value.forEach((item) => {
            item.isChecked = item.type === selectedType.value;
          });
        }
        pendingContentSelection.value = null;
      },
    });
  } else {
    applyContentSelection(data);
  }
};

const deleteBannerPopup = () => {
  openModal({
    name: "BannerDraftModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: $t("BANNER.BANNER_DELETE"),
      description: $t("BANNER.BANNER_DELETE_MESSAGE"),
    },
    onClose: (response) => {
      if (response) {
        deleteBannerAsync();
      }
    },
  });
};

const deleteBannerAsync = async () => {
  openModal({
    name: "BannerPageProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.DELETING },
  });

  try {
    await bannerStore.deleteBannerAsync({
      uuid: bannerUUID.value,
      lang: lang.value,
    });
    closeModal("BannerPageProcessModal");
    isCampaignModified.value = false;

    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);

    setTimeout(() => {
      router.push("/banner");
    }, 100);
  } catch (error) {
    console.error("Error in deleteBannerAsync", error);

    closeModal("BannerPageProcessModal");

    isCampaignModified.value = false;

    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
  }
};

const postContentFormAsync = async () => {
  try {
    const payload = prepareBannerPayload();

    await bannerStore.postBannerAsync({
      payload,
      lang: lang.value,
    });

    closeModal("BannerPageProcessModal");

    isCampaignModified.value = false;

    triggerLoading("bannerFormSaved");

    setTimeout(() => {
      router.push("/banner");
    }, 100);
  } catch (error) {
    console.error("Error in postContentFormAsync", error);
    closeModal("BannerPageProcessModal");

    isCampaignModified.value = false;

    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
  }
};

const patchContentFormAsync = async () => {
  try {
    const payload = prepareBannerPayload();

    await bannerStore.patchBannerAsync({
      payload,
      uuid: bannerUUID.value,
      lang: lang.value,
    });

    closeModal("BannerPageProcessModal");

    isCampaignModified.value = false;

    triggerLoading("bannerFormUpdated");

    setTimeout(() => {
      router.push("/banner");
    }, 100);
  } catch (error) {
    console.error("Error in patchContentFormAsync", error);
    closeModal("BannerPageProcessModal");

    isCampaignModified.value = false;

    triggerLoading($keys.KEY_NAMES.ERROR_OCCURRED);
  }
};

const prepareBannerPayload = () => {
  let bannerStateValue = "draft";

  if (eventStatus.value && bannerStartDate.value && bannerEndDate.value) {
    bannerStateValue = "scheduled";
  } else if (bannerState.value === 'live' && !isCreateDuplicate.value) {
    bannerStateValue = "live";
  }

  const payload = {
    title: bannerName.value,
    line1: currentOption.value.description1,
    line2: currentOption.value.description2,
    type: selectedType.value,
    preview: bannerPreview.value,
    state: bannerStateValue,
  };

  if (bannerUUID.value && !isCreateDuplicate.value) {
    payload.uuid = bannerUUID.value;
  }

  if (eventStatus.value && bannerStartDate.value && bannerEndDate.value) {
    const startDateObj = new Date(bannerStartDate.value * 1000);
    const endDateObj = new Date(bannerEndDate.value * 1000);
    startDateObj.setHours(0, 0, 0, 0);

    endDateObj.setHours(23, 59, 59, 999);

    const adjustedStartTimestamp = Math.floor(startDateObj.getTime() / 1000);
    const adjustedEndTimestamp = Math.floor(endDateObj.getTime() / 1000);

    payload.publishDate = adjustedStartTimestamp;
    payload.endDate = adjustedEndTimestamp;
  } else if (bannerState.value === 'live' && !isCreateDuplicate.value) {
    const currentPublishDate = editBannerData.value?.publishDate || 0;
    if (currentPublishDate) {
      const newPublishDate = new Date(currentPublishDate * 1000);
      newPublishDate.setMinutes(newPublishDate.getMinutes() + 10);
      payload.publishDate = Math.floor(newPublishDate.getTime() / 1000);
      payload.endDate = editBannerData.value?.endDate || 0;
    }
  }

  if (
    selectedType.value === "text_button" &&
    currentOption.value.bannerCTAInput &&
    currentOption.value.bannerCTALinkText
  ) {
    payload.ctaText = currentOption.value.bannerCTAInput;
    payload.ctaLink = currentOption.value.bannerCTALinkText;
  }

  return payload;
};

const setBannerState = () => {
  bannerIsLive.value = bannerState.value === "live";
  if (isCreateDuplicate.value) {
    resetBannerState();
  }
};

const resetBannerState = () => {
  bannerState.value = "draft";
  resetBannerDates();
  bannerPreview.value = false;
};

const setBannerDates = (response) => {
  if (!response) return;

  const publishDate = response.publishDate || 0;
  const endDateValue = response.endDate || 0;
  if (!publishDate || !endDateValue) {
    startDate.value = "";
    endDate.value = "";
    bannerStartDate.value = 0;
    bannerEndDate.value = 0;
    eventStatus.value = false;
    range.value = { start: null, end: null };
    return;
  }

  if (publishDate && endDateValue) {
    const startDateObj = new Date(publishDate * 1000);
    startDateObj.setHours(0, 0, 0, 0);
    const endDateObj = new Date(endDateValue * 1000);
    endDateObj.setHours(23, 59, 59, 999);
    const formattedStartDate = formatDateRangeOrSingle(startDateObj);
    const formattedEndDate = formatDateRangeOrSingle(endDateObj);

    startDate.value = formattedStartDate;
    endDate.value = formattedEndDate;

    const adjustedStartTimestamp = Math.floor(startDateObj.getTime() / 1000);
    const adjustedEndTimestamp = Math.floor(endDateObj.getTime() / 1000);

    bannerStartDate.value = adjustedStartTimestamp;
    bannerEndDate.value = adjustedEndTimestamp;

    eventStatus.value = response.state === "scheduled";
    range.value = {
      start: startDateObj,
      end: endDateObj,
    };
  }

  if (bannerState.value === "draft" && publishDate && endDateValue) {
    startDate.value = "";
    endDate.value = "";
    bannerStartDate.value = 0;
    bannerEndDate.value = 0;
    eventStatus.value = false;
    range.value = { start: null, end: null };
  }
};

const resetBannerDates = () => {
  startDate.value = "";
  endDate.value = "";
  bannerStartDate.value = 0;
  bannerEndDate.value = 0;
  eventStatus.value = false;
  range.value = {
    start: null,
    end: null,
  };
};

const initializeFormData = () => {
  isInitializing.value = true;

  isCampaignModified.value = false;
  if (props.isEdit && editBannerData.value) {
    const bannerData = editBannerData.value;

    bannerUUID.value = bannerData.uuid || "";
    bannerName.value = bannerData.title || "";
    bannerState.value = bannerData.state || "draft";
    bannerIsLive.value = bannerData.state === "live";
    bannerPreview.value = bannerData.preview || false;
    selectedType.value = bannerData.type || "all_text";

    contentList.value.forEach((item) => {
      item.isChecked = item.type === selectedType.value;
    });

    currentOption.value.description1 = bannerData.line1 || "";
    currentOption.value.description2 = bannerData.line2 || "";

    if (bannerData.ctaText) {
      currentOption.value.bannerCTAInput = bannerData.ctaText;
    }

    if (bannerData.ctaLink) {
      currentOption.value.bannerCTALinkText = bannerData.ctaLink;
      currentOption.value.isCTALinkIsValid = true;
    }
    typeBackup.value[selectedType.value] = { ...currentOption.value };
    setBannerDates(bannerData);

    isBannerFormNew.value = false;
    isCreateDuplicate.value = route.query.duplicate === "true";

    if (isCreateDuplicate.value) {
      bannerUUID.value = "";
      resetBannerState();
    }
  } else {
    const bannerType = route.query[QUERY_PARAM_KEY.DATA];

    if (bannerType) {
      if (bannerType === "all-text") {
        selectedType.value = "all_text";
      } else if (bannerType === "text-button") {
        selectedType.value = "text_button";
      }

      contentList.value.forEach((item) => {
        item.isChecked = item.type === selectedType.value;
      });
    }
  }
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      if (!bannerList.value || bannerList.value.length === 0) {
        await bannerStore.getBannerListAsync({ lang: lang.value });
      }
      initializeFormData();
      if (props.isEdit) {
        setBannerState();
      }
      isInitializing.value = false;
      populateDisableList();
      checkForNextScheduledBanner();
    }
  });
});

watch(
  () => editBannerData.value,
  () => {
    if (props.isEdit && editBannerData.value) {
      initializeFormData();
      setBannerState();
      populateDisableList();
      checkForNextScheduledBanner();
    }
  },
  { deep: true }
);

watch(
  () => bannerList.value,
  () => {
    populateDisableList();
    checkForNextScheduledBanner();
  },
  { deep: true }
);

watch(
  () => disableList.value,
  () => {
    checkForNextScheduledBanner();
  },
  { deep: true }
);

watch(
  [
    bannerName,
    currentOption,
    selectedType,
    bannerStartDate,
    bannerEndDate,
    eventStatus,
    bannerPreview,
  ],
  () => {
    if (props.isEdit && !isBannerFormNew.value && !isInitializing.value) {
      if (
        !(currentOption.value.bannerCTALinkText.trim() !== "" &&
        !currentOption.value.isCTALinkIsValid)
      ) {
        isCampaignModified.value = true;
      }
    }
  },
  { deep: true }
);

onBeforeRouteLeave((_, __, next) => {
  const isProcessModalOpen = document.querySelector(
    '[data-modal-component="BannerPageProcessModal"]'
  );
  if (isProcessModalOpen || !hasChanges.value) {
    next();
    return;
  }

  openModal({
    name: "BannerDraftModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.EXIT,
      title: $t("DESCRIPTION_POPUP.EXIT_PAGE_POPUP"),
    },
    onClose: (response) => {
      next(response);
    },
  });
});
</script>
