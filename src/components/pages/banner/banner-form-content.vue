<template>
  <div class="banner-form-content">
    <div class="banner-inner-container">
      <banner-text-field
        v-model="description1"
        label="Text 1 line:"
        :is-required="true"
        :max-length="maxLengthTexts"
        input-id="nutrition_section_scroll"
        input-ref="descriptionNotesFirst"
        @input="handleDescription1Input"
      >
        <template #action>
          <div
            v-if="!bannerIsLive || isCreateDuplicate"
            class="preview-section-dynamic"
            :class="{
              'simple-data-tooltip edge': !bannerPreview,
            }"
            :data-tooltip-text="!bannerPreview && bannerToggleTooltip"
          >
            <button-with-switcher
              :green-label="true"
              label="Banner preview"
              :is-checked="bannerPreview"
              @action="toggleBannerPreview"
            />
          </div>
        </template>
      </banner-text-field>

      <banner-text-field
        v-if="selectedType === 'text_button'"
        v-model="description2"
        label="Text 2 line:"
        :is-required="true"
        :max-length="maxLengthTexts"
        input-id="nutrition_section_scroll"
        input-ref="descriptionNotesSecond"
        @input="handleDescription2Input"
      />

      <banner-text-field
        v-if="selectedType === 'all_text'"
        v-model="description2"
        label="Text 2 line:"
        :is-required="true"
        :max-length="maxText1Length"
        input-id="nutrition_section_scroll"
        input-ref="descriptionNotesSecond"
        :combined-length="description2.length + bannerCTAInput.length"
        @input="handleAllTextDescription2Input"
        @keydown="checkCTATextInput"
      />
      <div class="cta-banner-main-container">
        <div class="banner-cta-input-and-url-section">
          <div class="banner-cta-input-section">
            <banner-text-field
              v-if="selectedType === 'text_button'"
              v-model="bannerCTAInput"
              label="CTA button:"
              :max-length="maxLengthCTA"
              :is-required="true"
              :isRoundedGreen="true"
              input-ref="newsCTAFieldRef"
              placeholder="Enter text"
              :show-counter="bannerCTAInput.length > 0"
              @input="handleCTAInput"
            />

            <banner-text-field
              v-if="selectedType === 'all_text'"
              v-model="bannerCTAInput"
              label="CTA text:"
              :max-length="maxText2Length"
              input-ref="newsCTAFieldRef"
              placeholder="Enter text"
              :show-counter="bannerCTAInput.length > 0"
              :combined-length="bannerCTAInput.length + description2.length"
              @input="handleAllTextCTAInput"
              @keydown="checkInput"
            />
          </div>

          <banner-url-field
            v-model="bannerCTALinkText"
            @input="handleCTALinkInput"
            @update:validation="updateUrlValidation"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import ButtonWithSwitcher from '@/components/button-with-switcher.vue';
import BannerTextField from '@/components/text-field.vue';
import BannerUrlField from '@/components/url-field.vue';

const props = defineProps({
  bannerIsLive: {
    type: Boolean,
    default: false
  },
  isCreateDuplicate: {
    type: Boolean,
    default: false
  },
  bannerPreview: {
    type: Boolean,
    default: false
  },
  bannerToggleTooltip: {
    type: String,
    default: ''
  },
  selectedType: {
    type: String,
    default: 'all_text'
  },
  maxLengthTexts: {
    type: Number,
    default: 42
  },
  maxLengthCTA: {
    type: Number,
    default: 20
  },
  maxText1Length: {
    type: Number,
    default: 42
  },
  maxText2Length: {
    type: Number,
    default: 42
  },
  initialValues: {
    type: Object,
    default: () => ({
      description1: '',
      description2: '',
      bannerCTAInput: '',
      bannerCTALinkText: '',
      isCTALinkInprogress: false,
      isCTALinkIsValid: false,
      isCTALinkBroken: false
    })
  }
});

const emit = defineEmits([
  'update:bannerPreview',
  'update:values',
  'input-change'
]);

const description1 = ref(props.initialValues.description1 || '');
const description2 = ref(props.initialValues.description2 || '');
const bannerCTAInput = ref(props.initialValues.bannerCTAInput || '');
const bannerCTALinkText = ref(props.initialValues.bannerCTALinkText || '');
const isCTALinkInprogress = ref(props.initialValues.isCTALinkInprogress || false);
const isCTALinkIsValid = ref(props.initialValues.isCTALinkIsValid || false);
const isCTALinkBroken = ref(props.initialValues.isCTALinkBroken || false);
const showWarningMessage = ref(false);
const showWarningCTAMessage = ref(false);

const toggleBannerPreview = () => {
  emit('update:bannerPreview', !props.bannerPreview);
  emitChanges();
};

const emitChanges = () => {
  emit('update:values', {
    description1: description1.value,
    description2: description2.value,
    bannerCTAInput: bannerCTAInput.value,
    bannerCTALinkText: bannerCTALinkText.value,
    isCTALinkInprogress: isCTALinkInprogress.value,
    isCTALinkIsValid: isCTALinkIsValid.value,
    isCTALinkBroken: isCTALinkBroken.value
  });
  emit('input-change');
};

const handleDescription1Input = () => {
  if (description1.value.length > props.maxLengthTexts) {
    description1.value = description1.value.substring(0, props.maxLengthTexts);
  }
  emitChanges();
};

const handleDescription2Input = () => {
  if (description2.value.length > props.maxLengthTexts) {
    description2.value = description2.value.substring(0, props.maxLengthTexts);
  }
  emitChanges();
};

const handleAllTextDescription2Input = () => {
  updateMaxLength();
  emitChanges();
};

const handleCTAInput = () => {
  bannerCTAInput.value = bannerCTAInput.value.trim();
  emitChanges();
};

const handleAllTextCTAInput = () => {
  updateMaxLength();
  emitChanges();
};

const handleCTALinkInput = () => {
  emitChanges();
};

const updateUrlValidation = (validationState) => {
  isCTALinkInprogress.value = validationState.isValidating;
  isCTALinkIsValid.value = validationState.isValid;
  isCTALinkBroken.value = validationState.isBroken;
  emitChanges();
};

const updateMaxLength = () => {
  if (props.selectedType === 'all_text') {
    const totalLength = description2.value.length + bannerCTAInput.value.length;
    if (totalLength > props.maxLengthTexts) {
      showWarningMessage.value = true;
      showWarningCTAMessage.value = true;
    } else {
      showWarningMessage.value = false;
      showWarningCTAMessage.value = false;
    }
  }
};

const checkCombinedTextLength = (event) => {
  if (props.selectedType === 'all_text') {
    const totalLength = description2.value.length + bannerCTAInput.value.length;
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Home', 'End'];
    if (totalLength >= props.maxLengthTexts && !allowedKeys.includes(event.key)) {
      event.preventDefault();
    }
  }
};

const checkInput = checkCombinedTextLength;
const checkCTATextInput = checkCombinedTextLength;

watch(() => props.initialValues, (newValues) => {
  description1.value = newValues.description1 || '';
  description2.value = newValues.description2 || '';
  bannerCTAInput.value = newValues.bannerCTAInput || '';
  bannerCTALinkText.value = newValues.bannerCTALinkText || '';
  isCTALinkInprogress.value = newValues.isCTALinkInprogress || false;
  isCTALinkIsValid.value = newValues.isCTALinkIsValid || false;
  isCTALinkBroken.value = newValues.isCTALinkBroken || false;
}, { deep: true });
</script>
