<template>
  <div class="recipe-image-and-video-container">
    <div class="recipe-image-and-video-inner-section">
      <div v-if="config?.showNoMediaError" class="media-error-section"></div>
      <div class="recipe-image-container container-part">
        <div class="top-section">
          <div class="heading-text text-h3">{{ $t('COMMON.IMAGE') }}</div>
          <div
            class="button-section add-recipe-image-section"
            :class="{
              'simple-data-tooltip': imageList?.length >= 20,
            }"
            :data-tooltip-text="imageList?.length >= 20 && `${$t('RECIPE_PRIMARY_IMAGE.IMAGE_LIMIT')} \n ${$t('RECIPE_PRIMARY_IMAGE.DELETE_IMAGE')}`"
          >
            <button
              type="button"
              class="btn-green-outline add-recipe-image-button"
              @click="toggleAddImageButton"
              :class="{ disabled: imageList?.length >= 20 || recipeVariantSelectedLanguage !== defaultLang }"
            >
            {{ $t('COMMON.ADD_IMAGE') }}
              <img
                alt="arrow"
                src="@/assets/images/arrow-down-green.png"
                class="dropdown-icon"
                :class="{ rotate: isAddImageButtonClicked }"
              />
            </button>
            <div
              v-if="isAddImageButtonClicked"
              class="recipe-image-dropdown-section"
            >
            <button
              v-for="(data, index) in recipeButtonOption"
              :key="index"
              :id="`button${index}`"
              type="button"
              @click="index !== 1 || isAllRecipeDataFilled ? selectUploadMediaOption(data) : null"
              :class="[
                'btn-reset result-section',
                {
                  'result-disable-section': index === 1 && !isAllRecipeDataFilled,
                  'simple-data-tooltip': index === 1 && !isAllRecipeDataFilled
                }
              ]"
              :data-tooltip-text="
                index === 1 && !isAllRecipeDataFilled
                  && `${$t('NOT_ENOUGH_DATA_PROVIDED')} \n ${$t('PLEASE_FILL_THE_REQUIRED_FIELDS')}`
              "
            >
              <span class="button-text" :class="{ 'disable-text': index === 1 && !isAllRecipeDataFilled }">{{ data.name }}</span>
            </button>
            </div>
          </div>
        </div>
        <div class="media-part">
          <recipe-primary-images
            :imageList="imageList"
            :emptyImage="emptyImage"
            :config="config"
            @mainImageSelected="mainImageSelected"
            :uploadImagePercentage="uploadImagePercentage"
            :loadedImageSize="loadedImageSize"
            :uploadImageSize="uploadImageSize"
            @deleteImage="deleteImage"
            @setMainImageWarning="setMainImageWarning"
            :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
            :defaultLang="defaultLang"
          ></recipe-primary-images>
        </div>
      </div>
      <div class="recipe-video-container container-part">
        <div class="top-section">
          <div class="heading-text text-h3">{{ $t('COMMON.VIDEO') }}</div>
          <div class="button-section">
            <button
              type="button"
              class="btn-green-outline"
              @click="selectUploadMediaOption()"
              :class="dynamicButtonClasses"
            >
              {{ video || isLinkPresent ? $t('COMMON.REPLACE_VIDEO') : $t('COMMON.ADD_VIDEO') }}
            </button>
          </div>
        </div>
        <div class="media-part">
          <recipe-video
            :video="video"
            :isVideoLoaded="isVideoLoaded"
            :loadedVideoSize="loadedVideoSize"
            :uploadPercentage="uploadPercentage"
            :uploadVideoSize="uploadVideoSize"
            :isVideoPresent="isVideoPresent"
            :isLinkPresent="isLinkPresent"
            :hostNameVideoUrl="hostNameVideoUrl"
            :linkURLImage="linkURLImage"
            :config="config"
            @deleteVideo="deleteVideo"
            @openVideoPopup="openVideoPopup"
            @setVideoDimensions="setVideoDimensions"
            :recipeVariantSelectedLanguage="recipeVariantSelectedLanguage"
            :defaultLang="defaultLang"
            :openLinkPage="openLinkPage"
          ></recipe-video>
        </div>
      </div>
    </div>
    <div v-if="shouldShowErrorMessage" class="recipe-media-error-section">
      <div class="info-icon">
        <img src="@/assets/images/red-info.svg?skipsvgo=true" alt="info icon" />
      </div>
      <div class="text text-title-2 font-weight-semi-bold">
        {{ getErrorMessage }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, getCurrentInstance} from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import recipePrimaryImages from '@/components/recipe-primary-images.vue';
import recipeVideo from './recipe-video-section.vue';
import { useI18n } from 'vue-i18n';

const store = useStore();
const { t } = useI18n();
const route = useRoute();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;

// Props
const props = defineProps({
  imageList: {
    type: Array,
    default: () => [],
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  uploadImagePercentage: {
    type: Number,
    default: 0,
  },
  loadedImageSize: {
    type: Number,
    default: 0,
  },
  uploadImageSize: {
    type: Number,
    default: 0,
  },
  emptyImage: {
    type: String,
    default: "",
  },
  video: {
    type: String,
    default: "",
  },
  isVideoLoaded: {
    type: Boolean,
    default: false,
  },
  uploadPercentage: {
    type: Number,
    default: 0,
  },
  uploadVideoSize: {
    type: Number,
    default: 0,
  },
  loadedVideoSize: {
    type: Number,
    default: 0,
  },
  isAllRecipeDataFilled: {
    type: Boolean,
    default: true,
  },
  isVideoPresent: {
    type: Boolean,
    default: true,
  },
  isLinkPresent: {
    type: Boolean,
    default: true,
  },
  hostNameVideoUrl: {
    type: String,
    default: "",
  },
  linkURLImage: {
    type: String,
    default: "",
  },
  recipeVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  defaultLang: {
    type: String,
    default: "",
  },
  openLinkPage: {
    type: Function,
    default: () => {},
  },
});

// Emits
const emit = defineEmits([
  'mainImageSelected',
  'deleteImage',
  'deleteVideo',
  'openVideoPopup',
  'selectUploadMediaOption',
  'setVideoDimensions',
  'setMainImageWarning'
]);

// Reactive data
const isAddImageButtonClicked = ref(false);
const recipeButtonOption = reactive([
  {
    id: 1,
    name: "External Image",
  },
  {
    id: 2,
    name: "Generate Image",
  },
]);

// Computed properties
const getFeatureConfig = computed(() => store.getters["config/getFeatures"]);

const dynamicButtonClasses = computed(() => ({
  disabled: (props.uploadPercentage !== 0 && props.uploadPercentage !== 100) ||
            props.recipeVariantSelectedLanguage !== props.defaultLang
}));

const shouldShowErrorMessage = computed(() =>
  props.config?.showNoMediaError ||
  props.config?.showErrorMessage ||
  (props.config?.showMainErrorMessage && !props.config?.showErrorMessage)
);

const getErrorMessage = computed(() => {
  if (props.config?.showNoMediaError) {
    return t("RECIPE_PRIMARY_IMAGE.ERROR_NO_MEDIA");
  } else if (props.config?.showErrorMessage) {
    return t("RECIPE_PRIMARY_IMAGE.SELECT_MEDIA");
  } else {
    return t("RECIPE_PRIMARY_IMAGE.SELECT_MAIN_IMAGE");
  }
});

// Methods
const mainImageSelected = (data) => emit('mainImageSelected', data);
const deleteImage = (data) => emit('deleteImage', data);
const deleteVideo = () => emit('deleteVideo');
const openVideoPopup = () => emit('openVideoPopup');
const toggleAddImageButton = () => {
  isAddImageButtonClicked.value = !isAddImageButtonClicked.value;
  filterImageListOption();
};
const filterImageListOption = () => {
  if (!getFeatureConfig.value[$keys.KEY_NAMES.GENERATOR]) {
    const itemName = t("GENERATOR.GENERATE_IMAGE");
    const index = recipeButtonOption.findIndex(data => data.name === itemName);
    if (index !== -1) {
      recipeButtonOption.splice(index, 1);
    }
  }
};
const selectUploadMediaOption = (data) => {
  isAddImageButtonClicked.value = false;
  if (data) {
    emit('selectUploadMediaOption', 'image', data);
  } else {
    emit('selectUploadMediaOption', 'video');
  }
};
const handleClickOutside = (event) => {
  if (isAddImageButtonClicked.value) {
    if (!document.querySelector(".add-recipe-image-section").contains(event.target)) {
      isAddImageButtonClicked.value = false;
    }
  }
};
const setVideoDimensions = (data) => emit('setVideoDimensions', data);
const setMainImageWarning = (data) => emit('setMainImageWarning', data);

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  if (!route.query.isin) {
    recipeButtonOption.splice(1, 1);
  }
});
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>
