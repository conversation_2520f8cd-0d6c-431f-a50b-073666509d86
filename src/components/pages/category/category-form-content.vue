<template>
  <div class="left-section">
    <div class="category-name-details">
      <div
        class="category-name-input-container"
        :class="{
          'simple-data-tooltip': hasCategoryNameFocus,
        }"
        :data-tooltip-text="hasCategoryNameFocus && categoryName"
      >
        <img
          alt="compulsory"
          v-if="!categoryName"
          class="compulsory-field-category"
          src="@/assets/images/asterisk.svg"
        />
        <input
          type="text"
          class="title text-title-1"
          ref="categoryNameRef"
          autocomplete="off"
          placeholder="Name your category"
          v-model.trim="categoryName"
          @input="handleNameInput"
          @focus="hasCategoryNameFocus = true"
          @blur="hasCategoryNameFocus = false"
        />
      </div>
      <div class="slug-category-details">
        <span class="slug-category-text text-title-3">Slug:</span>
        <input
          autocomplete="off"
          id="slug-category"
          v-model="categorySlug"
          class="slug-category-input"
          @input="handleSlugInput"
        />
        <div v-if="isSlugInvalid" class="slug-exist-main">
          <p class="slug-exist text-light-h4">This slug already exists</p>
        </div>
      </div>

      <div class="image-details">
        <span class="bold text-title-2">Category Image: </span>
        <span class="normal text-title-2 font-normal"
          >jpg,png format (recom. 1MB, max 15 MB)</span
        >
        <span class="compulsory-field-category-image">*</span>
      </div>
    </div>
  </div>
  <div class="right-section">
    <div class="publish-btn">
      <span class="text text-title-2"> {{ $t('COMMON.PUBLISH') }} </span>
      <label class="switch">
        <input
          type="checkbox"
          :checked="categoryStatus === 'active'"
          @click.prevent="togglePublishStatus"
        />
        <span class="slider-round"></span>
      </label>
    </div>

    <!-- Image Upload Section -->
    <div class="category-image-upload-section">
      <div class="image-upload-container">
        <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
          <div class="upload-progress-content">
            <div class="upload-progress-icon">
              <img
                alt="upload progress"
                class="progress-icon"
                :src="getProgressIcon(uploadProgress)"
              />
            </div>
            <div class="upload-text">
              <div class="upload-heading text-light-h4">
                {{ uploadProgress < 100 ? 'Upload is in progress' : 'Uploaded' }}
              </div>
              <span class="upload-media text-light-h6">
                {{ (loadedSize / 1024000).toFixed(1) }} of
                {{ (totalSize / 1024000).toFixed(1) }} MB
              </span>
            </div>
          </div>
        </div>

        <img
          v-if="categoryImage && (uploadProgress === 0 || uploadProgress === 100)"
          alt="category image"
          class="category-display-image"
          :src="categoryImage"
        />

        <div
          v-if="uploadProgress === 0 || uploadProgress === 100"
          class="image-upload-overlay"
        >
          <input
            type="file"
            class="image-upload-input"
            title="Update Picture"
            @change="handleImageUpload"
            accept=".jpg,.png,.jpeg"
          />
        </div>
      </div>
    </div>

    <!-- Category Variants Section -->
    <div v-if="availableLanguages && availableLanguages.length > 1" class="category-variants-section">
      <div class="category-variants-header">
        <div class="category-variants-title">Category Variants:</div>
        <div
          class="add-variant-button"
          :class="{
            'simple-data-tooltip': variantLanguageList.length < 1,
          }"
          :data-tooltip-text="variantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
        >
          <div
            class="add-variant-main"
            @click="openVariantPopup()"
            :class="
              variantLanguageList.length > 0
                ? 'add-variant-main'
                : 'disable-add-variant-main add-variant-main'
            "
          >
            <div class="add-variant-btn">
              <img alt="add variant" src="@/assets/images/category-add.png" />
            </div>
            <div class="add-variant-text text-h3">Add variant</div>
          </div>
        </div>
      </div>

      <div
        class="add-category-variant text-title-2 font-normal"
        v-if="categoryVariants.length <= 0"
      >
        Add category variants to support multiple languages.
      </div>

      <div class="category-variant-cards">
        <template v-for="(variant, index) in categoryVariants" :key="index">
          <variant-card-field
            v-if="variant?.lang !== currentLang"
            v-model="variant.name"
            :prefix-label="displayLanguageCode(variant.lang)"
            input-placeholder="Enter name"
            @input-change="handleVariantChange(index)"
            @delete-action="deleteVariant(variant, index)"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useNuxtApp } from '#app';
import VariantCardField from '@/components/variant-card-field/variant-card-field.vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  isCreateDuplicate: {
    type: Boolean,
    default: false
  },
  initialValues: {
    type: Object,
    default: () => ({
      name: '',
      slug: '',
      image: '',
      status: 'draft',
      variants: [],
    })
  }
});

const emit = defineEmits(['update:values', 'input-change']);

const { $t } = useNuxtApp();

// Form data
const categoryName = ref(props.initialValues.name || '');
const categorySlug = ref(props.initialValues.slug || '');
const categoryImage = ref(props.initialValues.image || '');
const categoryStatus = ref(props.initialValues.status || 'draft');
const categoryVariants = ref(props.initialValues.variants || []);

// Upload state
const uploadProgress = ref(0);
const loadedSize = ref(0);
const totalSize = ref(0);
const isSlugInvalid = ref(false);

// UI state
const hasCategoryNameFocus = ref(false);
const categoryNameRef = ref(null);

// Language and variants
const currentLang = ref('en');
const availableLanguages = ref(['en', 'fr', 'de']); // This should come from store
const variantLanguageList = computed(() => {
  return availableLanguages.value.filter(lang =>
    lang !== currentLang.value &&
    !categoryVariants.value.some(variant => variant.lang === lang)
  );
});

// Watch for prop changes
watch(() => props.initialValues, (newValues) => {
  categoryName.value = newValues.name || '';
  categorySlug.value = newValues.slug || '';
  categoryImage.value = newValues.image || '';
  categoryStatus.value = newValues.status || 'draft';
  categoryVariants.value = newValues.variants || [];
}, { deep: true });

// Emit changes to parent
const emitUpdate = () => {
  emit('update:values', {
    name: categoryName.value,
    slug: categorySlug.value,
    image: categoryImage.value,
    status: categoryStatus.value,
    variants: categoryVariants.value,
  });
  emit('input-change');
};

const handleNameInput = () => {
  // Auto-generate slug from name if not manually edited
  if (!categorySlug.value || categorySlug.value === generateSlug(categoryName.value)) {
    categorySlug.value = generateSlug(categoryName.value);
  }
  emitUpdate();
};

const handleSlugInput = () => {
  // Validate slug format and uniqueness
  categorySlug.value = categorySlug.value.toLowerCase().replace(/[^a-z0-9-]/g, '-');
  // TODO: Check slug uniqueness
  emitUpdate();
};

const generateSlug = (name) => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
};

const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // Validate file type and size
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  const maxSize = 15 * 1024 * 1024; // 15MB

  if (!allowedTypes.includes(file.type)) {
    // Show error message
    return;
  }

  if (file.size > maxSize) {
    // Show error message
    return;
  }

  // Simulate upload progress
  uploadProgress.value = 1;
  totalSize.value = file.size;

  // TODO: Implement actual file upload
  const reader = new FileReader();
  reader.onload = (e) => {
    categoryImage.value = e.target.result;
    uploadProgress.value = 100;
    emitUpdate();
  };
  reader.readAsDataURL(file);
};

const getProgressIcon = (progress) => {
  // Return appropriate progress icon based on percentage
  if (progress <= 5) return '@/assets/images/icon-video-upload-0.svg';
  if (progress <= 11) return '@/assets/images/icon-video-upload-6.svg';
  if (progress <= 17) return '@/assets/images/icon-video-upload-12.svg';
  if (progress <= 24) return '@/assets/images/icon-video-upload-18.svg';
  if (progress <= 30) return '@/assets/images/icon-video-upload-25.svg';
  if (progress <= 36) return '@/assets/images/icon-video-upload-31.svg';
  if (progress <= 41) return '@/assets/images/icon-video-upload-37.svg';
  if (progress <= 49) return '@/assets/images/icon-video-upload-42.svg';
  if (progress <= 55) return '@/assets/images/icon-video-upload-50.svg';
  if (progress <= 61) return '@/assets/images/icon-video-upload-56.svg';
  if (progress <= 67) return '@/assets/images/icon-video-upload-62.svg';
  if (progress <= 74) return '@/assets/images/icon-video-upload-68.svg';
  if (progress <= 80) return '@/assets/images/icon-video-upload-75.svg';
  if (progress <= 86) return '@/assets/images/icon-video-upload-81.svg';
  if (progress <= 92) return '@/assets/images/icon-video-upload-87.svg';
  if (progress <= 98) return '@/assets/images/icon-video-upload-93.svg';
  return '@/assets/images/icon-video-uploaded.svg';
};

const togglePublishStatus = () => {
  categoryStatus.value = categoryStatus.value === 'active' ? 'draft' : 'active';
  emitUpdate();
};

const openVariantPopup = () => {
  if (variantLanguageList.value.length < 1) return;
  // TODO: Implement variant popup
};

const handleVariantChange = (index) => {
  emitUpdate();
};

const deleteVariant = (variant, index) => {
  categoryVariants.value.splice(index, 1);
  emitUpdate();
};

const displayLanguageCode = (lang) => {
  const langMap = {
    'en': 'EN',
    'fr': 'FR',
    'de': 'DE',
  };
  return langMap[lang] || lang.toUpperCase();
};
</script>
