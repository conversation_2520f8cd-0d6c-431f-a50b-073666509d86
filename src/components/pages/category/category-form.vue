<template>
  <content-wrapper wrapper-classes="category-item font-family-averta" :is-body-loading="props.isLoading">
    <page-top-block
      :hint="hintID"
      :page-actions-back-label="$t('CATEGORY.BACK_MESSAGE')"
      :page-actions-back-path="'/category'"
      :background-image="image"
      :page-actions-is-continue-disabled="!isCategoryValid || (isEdit && !hasChanges)"
      @page-actions-continue="openPreviewSaveModal"
      @page-actions-cancel="() => navigateTo('/category')"
    >
      <div class="category-item-head">
        <image-box
          :image-src="image"
          :loaded-image-size="loadedImageSize"
          :uploadImagePercentage="uploadImagePercentage"
          @uploadedFile="uploadFile"
        />
        <div class="category-item-details">
          <div class="category-item-details-top border-bottom-dashed">
            <name-field
              v-model:name="categoriesName"
              input-id="category-name"
              :input-placeholder="'Name your category'"
            />

            <button-with-switcher
              class="category-item-details-publish-btn"
              :label="$t('COMMON.PUBLISH')"
              :is-checked="isPublish"
              @action="publishCategoryAction"
            />
          </div>

      <div class="category-item-settings">
        <div class="category-item-settings-container">
          <div class="category-item-settings-name">
            <span class="font-size-base font-bold color-black-mine-shaft">Slug:</span>
          </div>
          <div class="category-item-settings-actions border-bottom-dashed">
            <name-field
              v-model:name="categoriesSlug"
              input-id="slug-category"
              @input="handleSlugInput"
              :isRequired="false"
            />
            <div v-if="hasSlugExist" class="slug-exist-main">
              <p class="slug-exist text-light-h4">This slug already exists</p>
            </div>
          </div>
        </div>
      </div>

          <div class="category-item-details-body">
            <div class="category-item-details-text font-size-base">
              <span class="font-bold color-grey">Category Image:</span>
              <span class="font-normal color-grey">jpg,png format (recom. 1MB, max 15 MB)</span>
              <span class="category-item-details-text-mark color-red">*</span>
            </div>
            <div v-if="isEdit">
              <button
                type="button"
                class="btn-red-text"
                @click="deleteCategory"
              >
                <img alt="delete" src="@/assets/images/delete-icon.png"/>
                <span>Delete Category</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </page-top-block>

    <!-- Add Recipes to Category Section (for new categories only when no recipes added) -->
    <simple-content-wrapper
      class="add-recipes-section"
      v-if="!props.isEdit && recipeDataForCategories.length === 0"
    >
      <div class="add-recipes-content">
        <div class="add-recipes-left">
          <div class="add-recipes-header">
            <h2 class="add-recipes-title">Add Recipes to Category</h2>
            <p class="add-recipes-subtitle">Add a group of recipes to your new category</p>
          </div>
          <div class="add-recipes-actions">
            <button
              type="button"
              class="btn-green add-recipes-btn"
              @click="addRecipeToCategory"
            >
              Add Recipes
            </button>
          </div>
        </div>
        <div class="add-recipes-right">
          <div class="add-recipes-illustration">
            <img
              alt="Pan"
              class="pan-image"
              src="@/assets/images/pan-image.png"
            />
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <!-- Combined Recipes Section -->
    <simple-content-wrapper
      class="category-recipes-combined-wrapper"
      v-if="props.isEdit || (!props.isEdit && recipeDataForCategories.length > 0)"
    >
      <!-- Promoted Recipes Section -->
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="recipe-header-text">
                <span class="font-size-20 font-bold color-black-mine-shaft">
                  {{ categoryPromotedRecipesTotal }} Promoted Recipe{{ categoryPromotedRecipesTotal !== 1 ? 's' : '' }}
                </span>
                <div class="recipe-header-subtitle">
                  <span class="color-grey">Promoted recipes are the first to be displayed in category</span>
                </div>
              </div>
            </div>

            <!-- Promoted Recipes Table -->
            <div class="recipe-table-content">
              <div
                class="add-zero-section"
                v-if="(!categoryPromotedRecipes || categoryPromotedRecipes.length === 0) && !isPageLoading && !isUpdating"
              >
                <div class="zero-promoted">
                  <span class="bold text-title-2">
                    0 PROMOTED RECIPES.
                  </span>
                  <span class="normal text-title-2 font-normal">
                    Recipes will be auto-selected.
                  </span>
                </div>
              </div>

              <simple-table
                v-if="categoryPromotedRecipes.length > 0"
                :column-names="promotedRecipeColumnNames"
                :column-keys="promotedRecipeColumnKeys"
                :data-source="categoryPromotedRecipes"
                table-class="promoted-recipe-table"
              >
                <template v-slot:image="props">
                  <img
                    :src="props.data?.media?.image || defaultImage"
                    :alt="props.data?.title"
                    class="recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="recipe-isin">{{ props.data?.isin }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="recipe-title">{{ props.data?.title }}</span>
                </template>

                <template v-slot:totalTime="props">
                  <span class="recipe-time">{{ getRecipeTime(props.data) }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="recipe-ingredients">{{ getIngredientCount(props.data) }} ingredients</span>
                </template>

                <template v-slot:actions="props">
                  <div class="recipe-actions">
                    <body-menu
                      :actions="getPromotedRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>
            </div>
          </div>
        </div>
      </div>

      <!-- Recipes in Category Section -->
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="recipe-header-text">
                <span class="font-size-20 font-bold color-black-mine-shaft">
                  {{ recipeForCategoriesTotal }} Recipe{{ recipeForCategoriesTotal !== 1 ? 's' : '' }} in Category
                </span>
              </div>
              <div class="recipe-header-actions">
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="selectRecipes"
                >
                  Select
                </button>
                <search-bar :isInForm="true" @search="handleSearch"/>
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="addRecipeToCategory()"
                >
                  <img alt="download" src="@/assets/images/category-add.png?skipsvgo=true" />
                  <span>{{ $t("BUTTONS.ADD_RECIPE") }}</span>
                </button>
              </div>
            </div>

            <!-- Recipes in Category Table -->
            <div class="recipe-table-content">
              <div
                class="add-zero-section category-recipe-section"
                v-if="recipeDataForCategories.length === 0 && !isPageLoading && (props.isEdit || (!props.isEdit && categoryPromotedRecipes.length > 0))"
              >
                <div class="zero-promoted">
                  <span class="bold text-title-2">0 RECIPES IN CATEGORY.</span>
                  <span class="normal text-title-2 font-normal">Add recipes to this category.</span>
                </div>
              </div>

              <simple-table
                v-if="recipeDataForCategories.length > 0"
                :column-names="categoryRecipeColumnNames"
                :column-keys="categoryRecipeColumnKeys"
                :data-source="recipeDataForCategories"
                table-class="category-recipe-table"
              >
                <template v-slot:image="props">
                  <img
                    :src="getRecipeImage(props.data)"
                    :alt="props.data?.title"
                    class="recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="recipe-isin">{{ props.data?.isin }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="recipe-title">{{ props.data?.title }}</span>
                </template>

                <template v-slot:totalTime="props">
                  <span class="recipe-time">{{ getRecipeTime(props.data) }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="recipe-ingredients">{{ getIngredientCount(props.data) }} ingredients</span>
                </template>

                <template v-slot:actions="props">
                  <div class="recipe-actions">
                    <button
                      type="button"
                      class="btn-green-outline"
                      @click="promoteRecipe(props.data)"
                    >
                      Promote
                    </button>
                    <body-menu
                      :actions="getCategoryRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>

              <pagination
                v-if="recipeForCategoriesTotal > sizeRecipe && recipeDataForCategories.length > 0"
                :list="recipeDataForCategories"
                :list-total="recipeForCategoriesTotal"
                :size-per-page="sizeRecipe"
                :current-page="Math.floor(fromRecipe / sizeRecipe) + 1"
                @page-change="handleRecipesPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </simple-content-wrapper>
  </content-wrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import { useStore } from 'vuex';
import { useNuxtApp } from '#app';
import ContentWrapper from '@/components/content-wrapper/content-wrapper.vue';
import PageTopBlock from '@/components/page-top-block.vue';
import SimpleContentWrapper from '@/components/simple-content-wrapper.vue';
import SimpleTable from '@/components/simple-table/simple-table.vue';
import SearchBar from '@/components/search-bar/search-bar.vue';
import Pagination from '@/components/pagination.vue';
import ImageBox from '@/components/image-box.vue';
import NameField from '@/components/name-field.vue';
import ButtonWithSwitcher from '@/components/button-with-switcher.vue';
import SaveModal from '@/components/save-modal.vue';
import CancelModal from '@/components/cancel-modal.vue';
import SavingModal from '@/components/saving-modal.vue';
import DeleteModal from '@/components/delete-modal.vue';
import AddRecipeModal from '@/components/add-recipe-modal.vue';
import BodyMenu from '@/components/body-menu.vue';
import RecipePreviewDetailModal from '@/components/pages/recipes/recipe-preview-detail-modal.vue';
import { useBaseModal } from '@/composables/useBaseModal';
import { useProjectLang } from '@/composables/useProjectLang';
import { useTimeUtils } from '@/composables/useTimeUtils';
import { useCommonUtils } from '~/composables/useCommonUtils';
import { QUERY_PARAM_KEY } from '@/сonstants/query-param-key';
import defaultImage from '@/assets/images/default_recipe_image.png';
import savePopupImage from '@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png';
import publishVariantIcon from '@/assets/images/publish-variant-icon.png';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const store = useStore();
const { $t } = useNuxtApp();
const { readyProject } = useProjectLang();
const { parseDurationString } = useTimeUtils();

// Modal setup (matching banner form pattern)
const { openModal, closeModal } = useBaseModal({
  CategorySaveModal: SaveModal,
  CategoryCancelModal: CancelModal,
  CategorySavingModal: SavingModal,
  CategoryDeleteModal: DeleteModal,
  CategoryAddRecipeModal: AddRecipeModal,
  CategoryRecipePreviewModal: {
    component: RecipePreviewDetailModal,
    hideCloseBtn: false,
    modalWrapperClass: "recipe-preview-detail-modal-wrapper",
    skipClickOutside: true,
  },
});

// Category form variables (matching article-item.vue structure)
const categoriesName = ref('');
const categoriesSlug = ref('');
const image = ref("");
const isPublish = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const hasSlugExist = ref(false);
const hasChanges = ref(false);
const categoryISIN = ref('');

// Recipe data from APIs
const recipeDataForCategories = ref([]);
const categoryPromotedRecipes = ref([]);
const searchConfig = ref({});
const recipeForCategoriesTotal = ref(0);
const categoryPromotedRecipesTotal = ref(0);
const fromRecipe = ref(0);
const sizeRecipe = ref(10);
const fromPromotedRecipe = ref(0);
const sizePromotedRecipe = ref(10);
const isPageLoading = ref(false);
const isUpdating = ref(false);

// Search and filtering variables (matching original edit-categories.vue)
const searchcopy = ref('');
const totalPromotedRemovedIsin = ref([]);
const addedIsins = ref([]);
const lang = computed(() => store.getters['userData/getDefaultLang']);

// Add Recipe Modal variables (matching edit-categories.vue)
const selectedCategoryRecipe = ref([]);
const recipesAfterPageChange = ref([]);
const recipeMatchesIsinsTagRemove = ref([]);

// Action constants for dropdown menus
const RECIPE_ACTION_CASE = {
  PREVIEW: 'preview',
  REMOVE: 'remove',
  PROMOTE: 'promote',
  UNPROMOTE: 'unpromote',
};

// Table column configurations
const promotedRecipeColumnNames = ['', 'Recipe ISIN', 'Recipe Title', 'Total Time', 'Ingredient Count', ''];
const promotedRecipeColumnKeys = ['image', 'isin', 'title', 'totalTime', 'ingredientCount', 'actions'];

const categoryRecipeColumnNames = ['', 'Recipe ISIN', 'Recipe Title', 'Total Time', 'Ingredient Count', ''];
const categoryRecipeColumnKeys = ['image', 'isin', 'title', 'totalTime', 'ingredientCount', 'actions'];

// Combined recipes for display (promoted + regular)
const sampleRecipes = computed(() => {
  const promoted = categoryPromotedRecipes.value || [];
  const regular = recipeDataForCategories.value || [];
  return [...promoted, ...regular];
});

// Computed properties
const hintID = computed(() => {
  return props.isEdit && categoryISIN.value ? `ISIN: ${categoryISIN.value}` : '';
});

const isCategoryValid = computed(() => {
  return categoriesName.value.trim() !== '' && image.value !== defaultImage;
});

// Functions (matching article-item.vue structure)
const uploadFile = (file) => {
  // Handle file upload
  if (file) {
    image.value = URL.createObjectURL(file);
    hasChanges.value = true;
    uploadImagePercentage.value = 100;
    loadedImageSize.value = file.size;
  }
};

const handleSlugInput = () => {
  hasChanges.value = true;
  // TODO: Add slug validation logic
};

const publishCategoryAction = () => {
  isPublish.value = !isPublish.value;
  hasChanges.value = true;
};

const deleteCategory = () => {
  if (!props.isEdit || !categoryISIN.value) return;

  openModal({
    name: 'CategoryDeleteModal',
    props: {
      closeModal: () => closeModal('CategoryDeleteModal'),
      productInfoTitle: 'Delete Category?',
      productDescriptionOne: 'Do you want to delete this',
      productDescriptionTwo: 'category?',
      deleteItem: deleteCategoryConfirm,
      availableLanguage: 0,
      buttonText: 'Delete',
    },
  });
};

const deleteCategoryConfirm = async () => {
  try {
    // Close delete modal and show saving modal
    closeModal('CategoryDeleteModal');

    openModal({
      name: 'CategorySavingModal',
      props: {
        status: 'saving', // Could also be 'deleting' if you have that status
      },
    });

    // Delete the category using Vuex store
    await store.dispatch('categories/deleteCategoryAsync', {
      isin: categoryISIN.value,
    });

    // Close saving modal
    closeModal('CategorySavingModal');

    // Reset form state
    hasChanges.value = false;

    // Navigate back to category list
    setTimeout(() => {
      router.push('/category');
    }, 100);

  } catch (error) {
    console.error('[IQ][CategoryForm] Error deleting category:', error);
    closeModal('CategorySavingModal');
    // TODO: Show error message
  }
};

const openPreviewSaveModal = () => {
  if (!isCategoryValid.value) return;

  // Determine modal type based on publish status (matching original edit-categories.vue)
  const isPublishing = isPublish.value;
  const buttonName = isPublishing ? 'Publish' : 'Save';
  const description = isPublishing
    ? 'Do you want to publish your Category form?'
    : 'Do you want to save as draft your Category form?';
  const imageName = isPublishing ? publishVariantIcon : savePopupImage;

  openModal({
    name: 'CategorySaveModal',
    props: {
      closeModal: () => closeModal('CategorySaveModal'),
      saveAndPublishFunction: saveButtonClickAsync,
      availableLang: [],
      buttonName,
      description,
      imageName,
      slugCheckConfirm: false,
      hasSlugExist: hasSlugExist.value,
    },
  });
};

const saveButtonClickAsync = async () => {
  try {
    // Close save modal and show saving modal
    closeModal('CategorySaveModal');

    openModal({
      name: 'CategorySavingModal',
      props: {
        status: isPublish.value ? 'publishing' : 'saving',
      },
    });

    // Prepare payload (matching original edit-categories.vue)
    const payload = {
      name: categoriesName.value,
      slug: categoriesSlug.value,
      image: image.value !== defaultImage ? image.value : '',
      state: isPublish.value ? 'active' : 'hidden',
    };

    const lang = store.getters['userData/getDefaultLang'];

    if (props.isEdit && categoryISIN.value) {
      // Update existing category
      await store.dispatch('categories/patchCategoryAsync', {
        payload,
        isin: categoryISIN.value
      });
    } else {
      // Create new category
      await store.dispatch('categories/postCategoryOrCategoryGroupAsync', {
        payload,
        lang
      });
    }

    // Close saving modal
    closeModal('CategorySavingModal');

    // Reset form state
    hasChanges.value = false;

    // Navigate back to category list
    setTimeout(() => {
      router.push('/category');
    }, 100);

  } catch (error) {
    console.error('[IQ][CategoryForm] Error saving category:', error);
    closeModal('CategorySavingModal');
    // TODO: Show error message
  }
};

// Recipe table functions
const addRecipeToCategory = () => {
  // Open Add Recipe Modal (matching edit-categories.vue pattern)
  openModal({
    name: 'CategoryAddRecipeModal',
    props: {
      closeModal: () => handleAddRecipeModalClose(),
      recipeDataForCategories: recipeDataForCategories.value || [],
      categoryPromotedRecipes: categoryPromotedRecipes.value || [],
      selectedCategoryRecipe: selectedCategoryRecipe.value || [],
      recipesAfterPageChange: recipesAfterPageChange.value || [],
      recipeMatchesIsinsTagRemove: recipeMatchesIsinsTagRemove.value || [],
      removeRecipeList: [],
      removeRecipeTagList: [],
      isEditCategories: props.isEdit,
      isAddCategory: !props.isEdit,
      isPageLoading: false,
      isAddTag: false,
      isEditTag: false,
      preventEnterAndSpaceKeyPress: (event) => {
        if (event.key === 'Enter' || event.key === ' ') {
          event.preventDefault();
        }
      },
      campaignModifiedAddRecipe: () => {
        hasChanges.value = true;
      },
      onRecipeAdded: (addedRecipes) => {
        handleRecipeAdded(addedRecipes);
      }
    },
  });
};

// Handle modal close and update recipe lists
const handleAddRecipeModalClose = () => {
  closeModal('CategoryAddRecipeModal');
  // Refresh recipe data after modal closes
  if (props.isEdit && categoryISIN.value) {
    getRecipeDataForCategoriesAsync();
    getPromotedRecipesForCategoriesAsync();
  }
};

// Handle when recipes are added from modal
const handleRecipeAdded = (addedRecipes) => {
  console.log('[IQ][CategoryForm] Recipes added:', addedRecipes);

  if (addedRecipes && addedRecipes.length > 0) {
    // Process each added recipe
    addedRecipes.forEach(recipe => {
      // Check if recipe is not already in the list
      const existsInRegular = recipeDataForCategories.value.find(r => r.isin === recipe.isin);
      const existsInPromoted = categoryPromotedRecipes.value.find(r => r.isin === recipe.isin);

      if (!existsInRegular && !existsInPromoted) {
        // Format the recipe data to match the expected structure
        const formattedRecipe = {
          isin: recipe.isin,
          title: recipe.title?.[lang.value] || recipe.title || '',
          totalTime: recipe.time?.total || '',
          ingredientCount: recipe.ingredients?.[lang.value]?.length || 0,
          image: recipe.media?.[lang.value]?.image || recipe.media?.[lang.value]?.externalImageUrl || defaultImage,
          media: recipe.media,
          time: recipe.time,
          ingredients: recipe.ingredients,
          isPromoted: false
        };

        // Add to regular recipes list
        recipeDataForCategories.value.unshift(formattedRecipe);

        // Add ISIN to selected list for tracking
        if (!selectedCategoryRecipe.value.includes(recipe.isin)) {
          selectedCategoryRecipe.value.push(recipe.isin);
        }
      }
    });

    // Mark form as changed
    hasChanges.value = true;

    // Update total count
    recipeForCategoriesTotal.value = recipeDataForCategories.value.length;

    console.log('[IQ][CategoryForm] Updated recipe count:', recipeForCategoriesTotal.value);
    console.log('[IQ][CategoryForm] Current recipes:', recipeDataForCategories.value);
  }
};

const selectRecipes = () => {
  // TODO: Implement recipe selection functionality
  console.log('Select recipes');
};

// Search functionality (matching original edit-categories.vue)
const handleSearch = (searchTerm) => {
  searchcopy.value = searchTerm;
  // Reload recipes with new search term
  if (props.isEdit && categoryISIN.value) {
    const langValue = store.getters['userData/getDefaultLang'];
    getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
  }
};

const addRecipeToAddedIsins = (recipeIsin) => {
  if (!addedIsins.value.includes(recipeIsin)) {
    addedIsins.value.push(recipeIsin);
  }
};

// Helper function to extract image URL from media object
const getRecipeImage = (recipe) => {
  if (!recipe) return defaultImage;

  // If there's a direct image property, use it
  if (recipe.image) {
    return recipe.image;
  }

  // If there's a media object, extract the image
  if (recipe.media) {
    const currentLang = store.getters['userData/getDefaultLang'] || 'fr-FR';

    // Try to get image for current language
    if (recipe.media[currentLang]?.image) {
      return recipe.media[currentLang].image;
    }

    // Try to get thumbnail image for current language
    if (recipe.media[currentLang]?.thumbnailImageUrl) {
      return recipe.media[currentLang].thumbnailImageUrl;
    }

    // Try to get first image from imageList for current language
    if (recipe.media[currentLang]?.imageList?.length > 0) {
      return recipe.media[currentLang].imageList[0].url;
    }

    // Fallback to any available language
    const availableLanguages = Object.keys(recipe.media);
    for (const lang of availableLanguages) {
      if (recipe.media[lang]?.image) {
        return recipe.media[lang].image;
      }
      if (recipe.media[lang]?.thumbnailImageUrl) {
        return recipe.media[lang].thumbnailImageUrl;
      }
      if (recipe.media[lang]?.imageList?.length > 0) {
        return recipe.media[lang].imageList[0].url;
      }
    }
  }

  // Fallback to default image
  return defaultImage;
};

// Helper function to format recipe time from time object
const getRecipeTime = (recipe) => {
  if (!recipe) return '';

  // If there's a direct totalTime property (string), use it
  if (recipe.totalTime && typeof recipe.totalTime === 'string') {
    return recipe.totalTime;
  }

  // If there's a time object with total property
  if (recipe.time?.total) {
    return parseDurationString(recipe.time.total);
  }

  // If there's a time object with cook and prep
  if (recipe.time) {
    const { cook, prep } = recipe.time;
    if (cook && prep) {
      const cookTime = parseDurationString(cook);
      const prepTime = parseDurationString(prep);
      if (cookTime && prepTime) {
        return `${prepTime} prep + ${cookTime} cook`;
      }
    }

    // If only cook time
    if (cook) {
      return parseDurationString(cook);
    }

    // If only prep time
    if (prep) {
      return parseDurationString(prep);
    }
  }

  // Fallback
  return '';
};

// Helper function to get ingredient count from ingredients array
const getIngredientCount = (recipe) => {
  console.log('recipe'  , recipe);
  if (!recipe) return 0;

  const currentLang = store.getters['userData/getDefaultLang'] || 'fr-FR';

  // Priority 1: Language-specific ingredients array
  if (recipe.ingredients && typeof recipe.ingredients === 'object') {
    // Check if ingredients is organized by language
    if (recipe.ingredients[currentLang] && Array.isArray(recipe.ingredients[currentLang])) {
      return recipe.ingredients[currentLang].length;
    }

    // If ingredients is a direct array (not language-specific)
    if (Array.isArray(recipe.ingredients)) {
      return recipe.ingredients.length;
    }

    // Fallback to any available language
    const availableLanguages = Object.keys(recipe.ingredients);
    for (const lang of availableLanguages) {
      if (Array.isArray(recipe.ingredients[lang])) {
        return recipe.ingredients[lang].length;
      }
    }
  }

  // Fallback
  return 0;
};

// Helper functions for dropdown menu actions
const getPromotedRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: 'Preview',
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.UNPROMOTE, recipe.isin],
      label: 'Unpromote',
    },
  ];
};

const getCategoryRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: 'Preview',
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.REMOVE, recipe.isin],
      label: 'Remove',
    },
  ];
};

// Handle dropdown menu actions
const handleRecipeAction = ([actionType, recipeIsin]) => {
  const recipe = [...categoryPromotedRecipes.value, ...recipeDataForCategories.value]
    .find(r => r.isin === recipeIsin);

  if (!recipe) {
    console.error('[IQ][CategoryForm] Recipe not found for action:', actionType, recipeIsin);
    return;
  }

  switch (actionType) {
    case RECIPE_ACTION_CASE.PREVIEW:
      openModal({
        name: 'CategoryRecipePreviewModal',
        props: {
          recipeIsin: recipeIsin,
          checkRecipePreviewVideo: () => {},
        },
      });
      break;
    case RECIPE_ACTION_CASE.PROMOTE:
      promoteRecipe(recipe);
      break;
    case RECIPE_ACTION_CASE.UNPROMOTE:
      removePromotedRecipe(recipe);
      break;
    case RECIPE_ACTION_CASE.REMOVE:
      removeRecipeFromCategory(recipe);
      break;
    default:
      console.warn('[IQ][CategoryForm] Unknown action type:', actionType);
  }
};

// Pagination handlers (matching original edit-categories.vue)
const handlePromotedRecipesPageChange = (page) => {
  fromPromotedRecipe.value = (page - 1) * sizePromotedRecipe.value;
  if (props.isEdit && categoryISIN.value) {
    const langValue = store.getters['userData/getDefaultLang'];
    getPromotedRecipesForCategoriesAsync(categoryISIN.value, langValue);
  }
};

const handleRecipesPageChange = (page) => {
  fromRecipe.value = (page - 1) * sizeRecipe.value;
  if (props.isEdit && categoryISIN.value) {
    const langValue = store.getters['userData/getDefaultLang'];
    getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
  }
};

const removeRecipeFromCategory = (recipe) => {
  // Remove recipe from the category recipes
  const index = recipeDataForCategories.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    recipeDataForCategories.value.splice(index, 1);

    // Update filtering arrays (matching original edit-categories.vue)
    const isinIndex = addedIsins.value.indexOf(recipe.isin);
    if (isinIndex > -1) {
      addedIsins.value.splice(isinIndex, 1);
    }

    hasChanges.value = true;
    console.log('Removed recipe from category:', recipe.title);
  }
};

const removePromotedRecipe = (recipe) => {
  // Remove recipe from promoted recipes
  const index = categoryPromotedRecipes.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    const unpromotedRecipe = { ...recipe, isPromoted: false };
    categoryPromotedRecipes.value.splice(index, 1);

    // Add to category recipes list
    recipeDataForCategories.value.unshift(unpromotedRecipe);

    // Update totals
    categoryPromotedRecipesTotal.value -= 1;
    recipeForCategoriesTotal.value += 1;

    // Update filtering arrays (matching original edit-categories.vue)
    if (!totalPromotedRemovedIsin.value.includes(recipe.isin)) {
      totalPromotedRemovedIsin.value.push(recipe.isin);
    }

    hasChanges.value = true;
    console.log('Unpromoted recipe:', recipe.title);
  }
};

const promoteRecipe = (recipe) => {
  // Move recipe from category to promoted
  const index = recipeDataForCategories.value.findIndex(r => r.isin === recipe.isin);
  if (index > -1) {
    const promotedRecipe = { ...recipe, isPromoted: true };
    recipeDataForCategories.value.splice(index, 1);
    categoryPromotedRecipes.value.push(promotedRecipe);

    // Update totals
    categoryPromotedRecipesTotal.value += 1;
    recipeForCategoriesTotal.value -= 1;

    // Update filtering arrays (matching original edit-categories.vue)
    // Remove from totalPromotedRemovedIsin if it was there
    const removedIndex = totalPromotedRemovedIsin.value.indexOf(recipe.isin);
    if (removedIndex > -1) {
      totalPromotedRemovedIsin.value.splice(removedIndex, 1);
    }

    hasChanges.value = true;
    console.log('Promoted recipe:', recipe.title);

    // Refresh category recipes to reflect the exclusion of the newly promoted recipe
    if (props.isEdit && categoryISIN.value) {
      const langValue = store.getters['userData/getDefaultLang'];
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    }
  }
};

// API Functions (matching original edit-categories.vue)
const getPromotedRecipesForCategoriesAsync = async (isin, lang) => {
  try {
    await store.dispatch('categories/getPromotedRecipesForCategoriesAsync', {
      isin,
      lang
    });

    const response = store.getters['categories/getPromotedRecipesForCategories'];
    if (response) {
      // Handle different response formats
      if (Array.isArray(response)) {
        categoryPromotedRecipes.value = response.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.length;
      } else if (response.promotedRecipes && Array.isArray(response.promotedRecipes)) {
        // Handle the specific format: {isin: '1170934', promotedRecipeIsins: Array(1), promotedRecipes: Array(1)}
        categoryPromotedRecipes.value = response.promotedRecipes.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.promotedRecipes.length;
      } else if (response.results && Array.isArray(response.results)) {
        categoryPromotedRecipes.value = response.results.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.results.length;
      } else if (response.data && Array.isArray(response.data)) {
        categoryPromotedRecipes.value = response.data.map(recipe => ({
          ...recipe,
          isPromoted: true
        }));
        categoryPromotedRecipesTotal.value = response.total || response.data.length;
      } else {
        console.warn('[IQ][CategoryForm] Unexpected promoted recipes response format:', response);
        categoryPromotedRecipes.value = [];
        categoryPromotedRecipesTotal.value = 0;
      }
    } else {
      categoryPromotedRecipes.value = [];
      categoryPromotedRecipesTotal.value = 0;
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading promoted recipes:', error);
    categoryPromotedRecipes.value = [];
  }
};

const getRecipeDataForCategoriesAsync = async (isin, langParam) => {
  console.log('totalPromotedRemovedIsin', totalPromotedRemovedIsin.value)
  try {
    // Get ISINs of currently promoted recipes to exclude them from regular recipes
    const promotedRecipeIsins = categoryPromotedRecipes.value.map(recipe => recipe.isin);

    // Combine promoted recipe ISINs with manually removed ISINs
    const allExcludingIsins = [
      ...totalPromotedRemovedIsin.value,
      ...promotedRecipeIsins
    ].filter(Boolean); // Remove any empty values

    console.log('[IQ][CategoryForm] Excluding ISINs:', {
      promotedRecipeIsins,
      totalPromotedRemovedIsin: totalPromotedRemovedIsin.value,
      allExcludingIsins
    });

    const payload = {
      country: langParam.split('-')[1] || 'FR',
      q: searchcopy.value.trim(),
      excludingIsins: allExcludingIsins.join(','),
      groupsIncludingIsins: addedIsins.value.join(','),
      groups: isin,
      from: fromRecipe.value,
      size: sizeRecipe.value,
      sort: 'lastMod',
    };

    await store.dispatch('categories/getRecipeForCategoriesAsync', { payload });

    const response = store.getters['categories/getRecipeForCategories'];
    if (response?.results && Array.isArray(response.results)) {
      recipeDataForCategories.value = response.results.map(recipe => ({
        ...recipe,
        isPromoted: false
      }));
      recipeForCategoriesTotal.value = response.total || 0;
    } else {
      console.warn('[IQ][CategoryForm] Unexpected recipes response format:', response);
      recipeDataForCategories.value = [];
      recipeForCategoriesTotal.value = 0;
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading recipes for categories:', error);
    recipeDataForCategories.value = [];
    recipeForCategoriesTotal.value = 0;
  }
};

const getSearchConfigAsync = async () => {
  try {
    await store.dispatch('editSearch/getEditSearchAsync');
    const response = store.getters['editSearch/getEditSearch'];
    if (response) {
      searchConfig.value = response;
    } else {
      searchConfig.value = {};
    }
  } catch (error) {
    console.error('[IQ][CategoryForm] Error loading search config:', error);
    searchConfig.value = {};
  }
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      const lang = store.getters['userData/getDefaultLang'];

      if (props.isEdit) {
        const isin = route.params.isin;
        categoryISIN.value = isin;

        // Load all category data (matching original edit-categories.vue)
        try {
          // 1. Load category basic data
          await store.dispatch('categories/getCategoryDataAsync', {
            isin,
            lang
          });

          const categoryData = store.getters['categories/getCategoryData'];
          if (categoryData) {
            categoriesName.value = categoryData.name || '';
            categoriesSlug.value = categoryData.slug || '';
            image.value = categoryData.image || defaultImage;
            isPublish.value = categoryData.state === 'active';
          }

          // 2. Load promoted recipes for this category
          await getPromotedRecipesForCategoriesAsync(isin, lang);

          // 3. Load regular recipes for this category
          await getRecipeDataForCategoriesAsync(isin, lang);

          // 4. Load search configuration
          await getSearchConfigAsync();

        } catch (error) {
          console.error('[IQ][CategoryForm] Error loading category data:', error);
        }
      } else {
        // For create mode, still load search config
        await getSearchConfigAsync();
      }
    }
  });
});

// Navigation guard for unsaved changes
onBeforeRouteLeave((_, __, next) => {
  if (hasChanges.value) {
    openModal({
      name: 'CategoryCancelModal',
      props: {
        availableLang: [],
        isCampaignModifiedFromShoppableReview: false,
        callConfirm: () => {
          closeModal('CategoryCancelModal');
          hasChanges.value = false;
          next();
        },
        closeModal: () => {
          closeModal('CategoryCancelModal');
          next(false);
        },
      },
    });
  } else {
    next();
  }
});

// Watch for name changes to auto-generate slug
watch(categoriesName, (newName) => {
  if (newName && !categoriesSlug.value) {
    categoriesSlug.value = newName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  }
  hasChanges.value = true;
});
</script>

<style lang="scss" scoped>
// Import SCSS variables and settings
@import '@/assets/scss/common/palettes';
@import '@/assets/scss/common/settings';
@import '@/assets/scss/common/fonts';

.category-item {
  font-family: $font-family-averta;

  .category-item-head {
    display: flex;
    align-items: flex-start;
    gap: 20px;

    .category-item-details {
      flex: 1;

      .category-item-details-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .category-item-details-body {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .category-item-details-text {
          margin-bottom: 12px;

          .category-item-details-text-mark {
            margin-left: 4px;
          }
        }
      }
    }
  }

  .category-item-hr {
    border: none;
    border-top: 1px solid $grainsboro;
    margin: 24px 0;
  }

  .category-item-settings {
    .category-item-settings-container {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .category-item-settings-actions {
        flex: 1;

        .slug-exist-main {
          margin-top: 4px;

          .slug-exist {
            color: $ruby-red;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// Recipes table styles (matching original edit-categories.scss)
.recipes-table-content {
  .content {
    .recipe-category {
      .recipe-header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .recipe-table-content {
        .add-zero-section {
          text-align: center;
          padding: 40px 20px;
          background-color: $white-smoke;
          border-radius: 8px;
          margin-bottom: 20px;

          .zero-promoted {
            .bold {
              font-weight: 700;
              color: $black;
              display: block;
              margin-bottom: 8px;
            }

            .normal {
              color: $grey;
              font-weight: 400;
            }
          }
        }

        .recipe-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 22px;

          .table-head {
            background-color: $white-smoke;
            border-radius: 8px 8px 0px 0px;

            .title {
              color: $spanish-gray;
              font-size: 14px;
              line-height: 1;
              text-align: left;
              margin: 0 4px;

              th {
                padding: 8px 12px;
                font-weight: 700;
                text-align: left;
              }

              .category-group-title {
                width: 318px;
              }

              .category-group-isin {
                width: 109px;
              }

              .category-group-total-time {
                width: 119px;
              }

              .ing-count {
                width: 230px;
              }
            }
          }

          .body {
            border-bottom: 1px solid $grainsboro;

            td {
              padding: 12px;
              vertical-align: middle;

              .recipe-number,
              .recipe-isin,
              .recipe-title,
              .recipe-time,
              .recipe-ingredients {
                font-size: 14px;
                color: $black;
              }
            }
          }
        }
      }
    }
  }
}


.recipe-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}
.recipe-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  display: flex;
  justify-content: space-evenly;
}
.category-recipes-wrapper, .promoted-recipes-wrapper {
  padding: 0;
}
.recipe-header-actions {
  display: flex;
  align-items: center;
}
</style>
