<template>
<div class="simple-actions">
  <button
    v-if="isEditBtnDisplayed"
    type="button"
    class="btn-icon simple-actions-btn"
    :class="{
      'simple-actions-btn-with-info-tooltip': isEditInfoTooltipShowing,
      'simple-actions-btn-with-warn-tooltip': isEditBtnDisabled && !isEditInfoTooltipShowing,
    }"
    :disabled="isEditBtnDisabled"
    @click="clickEdit"
  >
    <img src="@/assets/images/edit-icon.png" alt="edit icon">

    <span v-if="isEditInfoTooltipShowing" class="simple-actions-tooltip-info">{{ $t("SIMPLE_ACTIONS_EDIT_INFO_TOOLTIP") }}</span>
    <span v-if="editBtnWarnTooltipText" class="simple-actions-tooltip-warn">
      <img alt="info icon" src="@/assets/images/info.svg?skipsvgo=true" />
      <span>
        {{ editBtnWarnTooltipText }}
      </span>
    </span>
  </button>
  <button
    v-if="isDeleteBtnDisplayed"
    type="button"
    class="btn-icon simple-actions-btn"
    :class="{
      'simple-actions-btn-with-info-tooltip': isDeleteInfoTooltipShowing,
      'simple-actions-btn-with-warn-tooltip': isDeleteBtnDisabled && !isDeleteInfoTooltipShowing,
    }"
    :disabled="isDeleteBtnDisabled"
    @click="clickDelete"
  >
    <img src="@/assets/images/delete-icon.png" alt="delete icon">

    <span v-if="isDeleteInfoTooltipShowing" class="simple-actions-tooltip-info">{{ $t("SIMPLE_ACTIONS_DELETE_INFO_TOOLTIP") }}</span>
    <span v-if="deleteBtnWarnTooltipText" class="simple-actions-tooltip-warn">
      <img alt="info icon" src="@/assets/images/info.svg?skipsvgo=true" />
      <span>
        {{ deleteBtnWarnTooltipText }}
      </span>
    </span>
  </button>
</div>
</template>

<script setup>
const props = defineProps({
  isEditBtnDisplayed: {
    type: Boolean,
    required: false,
    default: true,
  },
  isEditBtnDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  isEditInfoTooltipShowing: {
    type: Boolean,
    required: false,
    default: false,
  },
  editBtnWarnTooltipText: {
    required: false,
  },

  isDeleteBtnDisplayed: {
    type: Boolean,
    required: false,
    default: true,
  },
  isDeleteBtnDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
  isDeleteInfoTooltipShowing: {
    type: Boolean,
    required: false,
    default: false,
  },
  deleteBtnWarnTooltipText: {
    required: false,
  },
});

const emit = defineEmits(["editOnClick", "deleteOnClick"]);

const clickEdit = () => {
  if (!props.isEditBtnDisabled) {
    emit('editOnClick');
  }
};

const clickDelete = () => {
  if (!props.isDeleteBtnDisabled) {
    emit('deleteOnClick');
  }
};
</script>
