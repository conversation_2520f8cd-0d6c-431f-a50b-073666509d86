<template>
  <div class="recipe-preview-media-main-container">
    <div class="media-container">
      <div class="image-section" v-if="isImageVisible">
        <img alt="recipe" v-if="productImage && !imageUrlLinkUpdate" :src="`${productImage}`" />
        <div v-if="isImageVisible && imageUrlLinkUpdate">
          <div class="link-section">
            <div class="link-main-container">
              <div class="image-container">
                <img
                  class="image"
                  :src="`${imageUrlLinkUpdate}`"
                  @error="$event.target.src = `${defaultImage}`"
                  alt="urlLink"
                />
                <button
                  class="text-container btn-reset"
                  @click="openImageLinkPage()"
                  type="button"
                >
                  <span class="text upper-text-container">
                    {{ imageUrlDomain }}
                  </span>
                  <span class="text lower-text-container">
                    {{ recipeName }}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <img
          v-if="!productImage && !imageUrlLinkUpdate"
          src="@/assets/images/recipe-detail-upload.png"
          alt="recipe-upload-icon"
        />
      </div>
      <div v-if="isVideoVisible" class="video-section">
        <div v-if="!isLinkPresent" class="video-inner-section">
          <img
            alt="video"
            v-show="!isVideoLoaded && productVideo"
            class="video-loader"
            :src="placeholderImage"
          />
          <video
            v-show="productVideo && isVideoLoaded"
            :src="`${productVideo}`"
            type="video/mp4"
            id="video-id-data"
            @canplaythrough="checkVideoLoading"
            aria-label="Instruction Video"
            title="Video description"
          >
            <track
              v-if="productVideo?.subtitles"
              :src="productVideo.subtitles"
              kind="subtitles"
              srclang="en"
              label="English"
            />
            <track
              v-if="productVideo?.description"
              :src="productVideo.description"
              kind="description"
              srclang="en"
              label="English"
            />
          </video>
          <button
            v-if="productVideo"
            type="button"
            class="btn-reset play-icon-button"
            @click="videoPopup()"
          >
            <img
              alt="video-icon"
              v-if="isVideoVisible && productVideo"
              class="video-icon-play"
              src="@/assets/images/videoPlayBtn.png"
            />
          </button>
          <img
            v-if="!productVideo"
            src="@/assets/images/empty-video.png"
            alt="empty-icon"
          />
        </div>
        <div v-if="isLinkPresent">
          <div class="link-section">
            <div class="link-main-container">
              <div class="image-container">
                <img alt="videoLink" class="image" :src="`${linkURLImage}`" />
                <div class="text-container" @click="openLinkPage()">
                  <div class="text upper-text-container">
                    {{ hostNameUrlLinkUpdate }}
                  </div>
                  <div class="text lower-text-container text-title-5">
                    {{ recipeName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="image-video-toggle-section">
      <button
        type="button"
        class="image-toggle toggle btn-reset"
        id="imageToggle"
        @click="showImageVideo('image')"
      >
        {{ $t("COMMON.IMAGE") }}
      </button>
      <button
        type="button"
        class="video-toggle toggle btn-reset"
        id="videoToggle"
        @click="showImageVideo('video')"
      >
        {{ $t("COMMON.VIDEO") }}
      </button>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  isImageVisible: {
    type: Boolean,
    default: true,
  },
  isVideoVisible: {
    type: Boolean,
    default: false,
  },
  isVideoLoaded: {
    type: Boolean,
    default: false,
  },
  productImage: {
    type: String,
    default: "",
  },
  productVideo: {
    type: String,
    default: "",
  },
  imageUrlLinkUpdate: {
    type: String,
    default: "",
  },
  linkURLImage: {
    type: String,
    default: "",
  },
  isLinkPresent: {
    type: Boolean,
    default: false,
  },
  imageUrlDomain: {
    type: String,
    default: "",
  },
  recipeName: {
    type: String,
    default: "",
  },
  hostNameUrlLinkUpdate: {
    type: String,
    default: "",
  },
  defaultImage: {
    type: String,
    default: "",
  },
  placeholderImage: {
    type: String,
    default: "",
  },
  showImageVideo: {
    type: Function,
    default: () => {},
  },
  checkVideoLoading: {
    type: Function,
  },
  videoPopup: {
    type: Function,
    default: () => {},
  },
  openImageLinkPage: {
    type: Function,
    default: () => {},
  },
  openLinkPage: {
    type: Function,
    default: () => {},
  },
});
</script>
