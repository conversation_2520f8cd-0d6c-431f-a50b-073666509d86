<template>
  <div class="shoppable-review-component">
    <div v-if="isProductIdCopiedToClipboard" class="shoppable-product-copy-to-clipboard-information">
      <span class="copy-to-clipboard-successfully">
        <img class="green-correct" alt="" src="@/assets/images/green-correct-icon.png" />
        Product ID copied to the clipboard
        <img class="close-icon" alt="" src="@/assets/images/exit-gray.png" />
      </span>
    </div>
    <div class="top-section-shoppable-review">
      <div class="shoppable-review-top-container">
        <div class="shoppable-review-top-button">
          <p class="back-button-text text-h2">Shoppable Review</p>
        </div>
        <div v-if="isSaveClicked" class="saved-recipe-popup">
          <div class="saved-recipe-popup-container">
            <img alt=""
              class="saved-recipe-popup-image"
              src="~/assets/images/green-correct-icon.png"
            />
            <p class="saved-recipe-popup-text text-title-2 font-normal">{{ $t('COMMON.SAVED') }}</p>
          </div>
        </div>
        <div class="shoppable-review-section text-title-2">
          <button type="button"
            @click="cancelShoppableReview()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            class="btn-green-outline"
          >Cancel</button>
          <button type="button"
            @click="savePopup()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            :class="
              isCampaignModified ? 'btn-green' : 'disabled-button btn-green'
            "
          >Save</button>
        </div>
      </div>
    </div>
    <div class="container-shoppable-review">
      <div class="shoppable-review-first-section">
        <div class="shoppable-review-first-section-content">
          <div class="shoppable-image-container">
            <img alt="" v-if="recipeImage" class="image" :src="`${recipeImage}`" />
            <div v-if="!recipeImage && recipePreviewImageUrlLinkUpdate">
              <img
                class="image"
                :src="`${recipePreviewImageUrlLinkUpdate}`"
                @error="$event.target.src = `${defaultImage}`"
                alt=""
              />
              <div
                v-if="!recipePreviewImageUrlLinkUpdate"
                class="text-container-shoppable-review"
                @click="openImageLinkPage()"
              >
                <div class="upper-text-container-shoppable-review">
                  {{ hostNameImageUrlLinkUpdate }}
                </div>
                <div class="lower-text-container-shoppable-review">
                  {{ recipeName }}
                </div>
              </div>
            </div>
            <img alt=""
              v-if="!recipeImage && !recipePreviewImageUrlLinkUpdate"
              class="image"
              src="@/assets/images/recipe-detail-upload.png"
            />
          </div>
          <div class="shoppable-review-first-section-details">
            <div class="shoppable-review-first-section-id">
              <span>{{ recipeID ? recipeID : "" }}</span>
            </div>
            <div class="shoppable-review-first-section-name text-title-2">
              <span>{{ recipeName ? recipeName : "" }}</span>
            </div>
            <div class="shoppable-review-first-section-desc text-title-2 font-normal">
              <span>{{ recipeSubtitle ? recipeSubtitle : "" }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="ingredient-product-section">
        <div class="ingredient-product-head">{{ $t('COMMON.INGREDIENTS_AND_PRODUCTS') }}</div>
        <button  type="button" @click="viewIngredientDropDown" class="show-hide-section btn-reset">
          <span v-if="isAllIngredientVisible">{{ $t('COMMON.HIDE_ALL') }}</span>
          <span v-if="!isAllIngredientVisible">{{ $t('COMMON.VIEW_ALL') }}</span>
            <img
              alt="collapse-expand-icon"
              :class="isAllIngredientVisible ? 'collapse-image' : ''"
              class="expand-collapse-image"
              src="@/assets/images/arrow-down-green.png"
          />
        </button>
      </div>
    </div>
    <div
      class="shoppable-main-container"
      id="shoppable-main-container"
      :class="{'shoppable-review-margin' : hasLastUOMClicked}"
      v-for="(ingredientGroup, groupIndex) in shoppableIngredientsData.children"
      :key="groupIndex"
    >
      <div class="shoppable-group-name-parent-div">
        <div
          class="shoppable-group-name text-title-2"
          v-if="ingredientGroup.displayGroup && ingredientGroup.name.trim().length"
        >
          {{ ingredientGroup.name }}
        </div>
        <div
          v-show="ingredientGroup.children.length"
          v-for="(ingredient, index) in ingredientGroup.children"
          :key="index"
          class="lastloop"
        >
          <div
            class="margin-if-group-not-present"
            v-if="!ingredientGroup.displayGroup"
          ></div>
          <div class="shoppable-main-card-section-details">
            <div class="shoppable-review">
              <div class="shoppable-review-title-section">
                <div class="shoppable-review-title">{{ ingredient.name }}</div>
                <div
                  v-if="
                    ingredient &&
                    ingredient.campaignData &&
                    ingredient.campaignData.recipe
                  "
                  class="info-image"
                >
                  <div
                    class="info-icon-image simple-data-tooltip"
                    :data-tooltip-text="shoppableIngredientOverwritten"
                  >
                    <img alt="" src="@/assets/images/informationSymbol.png" />
                  </div>
                </div>
              </div>
              <div
                @click="closeViewDetails(ingredient)"
                class="shoppable-review-view"
              >
                <span v-if="ingredient.showView">{{ $t('COMMON.HIDE') }}</span>
                <span v-else>{{ $t('COMMON.VIEW') }}</span>
                <img alt="arrow-icon"
                  src="@/assets/images/arrow-down-green.png"
                  class="view-dropdown-icon"
                  :class="{
                    rotate: ingredient.showView,
                  }"
                />
              </div>
            </div>
            <div v-if="ingredient && ingredient.productId && ingredient.showView " class="shoppable-reference-product-container">
              <div class="shoppable-reference-product-text-clipboard">
                <div class="shoppable-reference-product-section">
                  <u v-if="ingredient.referenceProductData" class="shoppable-underline-reference-product-text text-light-h3" @click="previewReferenceProductAsync(ingredient)">Ref. Product&nbsp;<span :id="`refrenceProduct${index}`">{{ingredient.productId}}</span></u>
                  <u v-if="!ingredient.referenceProductData" class="shoppable-underline-reference-no-product-text text-light-h3">Ref. Product&nbsp;<span :id="`refrenceProduct${index}`">{{ingredient.productId}}</span></u>
                  <div v-if="ingredient.referenceProductData" class="shoppable-reference-product-preview-section">
                    <div class="shoppable-reference-product-data-section">
                      <div class="shoppable-reference-top-section">
                        <div class="shoppable-reference-product-image">
                          <img :src="ingredient.referenceProductData.image && ingredient.referenceProductData.image.url ? ingredient.referenceProductData.image.url : defaultImage " alt="">
                        </div>
                      </div>
                      <div class="shoppable-reference-bottom-section">
                        <div class="shoppable-reference-product-name text-h3">{{ setIngredientName(ingredient && ingredient.referenceProductData && ingredient.referenceProductData.brand ? ingredient.referenceProductData.brand : '', ingredient && ingredient.referenceProductData && ingredient.referenceProductData.name ? ingredient.referenceProductData.name : '').name }} </div>
                        <div class="shoppable-reference-product-info text-light-h4">
                          <div class="shoppable-refrence-product-weight">{{ ingredient.referenceProductData.size && ingredient.referenceProductData.size.value && ingredient.referenceProductData.size.unit && ingredient.referenceProductData.size.unit.abbreviation ? ingredient.referenceProductData.size.value + " " + ingredient.referenceProductData.size.unit.abbreviation : ingredient.referenceProductData.size.value ? ingredient.referenceProductData.size.value : ""  }}</div>
                          <div class="shoppable-refrence-product-id">{{ ingredient && ingredient.referenceProductData && ingredient.referenceProductData.externalId ? ingredient.referenceProductData.externalId : "" }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="shoppable-reference-product-copy-clipboard simple-data-tooltip"
                  :data-tooltip-text="copyProductId"
                >
                  <div class="clipboard-image" @click="copyReferenceProduct(ingredient,index)">
                    <img v-if="!ingredient.copyReferenceProductID" src="~/assets/images/copy_focussed.png" alt="">
                    <img v-if="ingredient.copyReferenceProductID" src="~/assets/images/copy_default.png" alt="">
                  </div>
                </div>
              </div>
            </div>
            <div v-show="ingredient.showView">
              <div class="Shoppable-review-table">
                <div class="shoppable-review-table-head">
                  <div class="shoppable-review-table-head-row text-light-h4">
                    <div
                      style="width: 25%"
                      class="shoppable-ingredient-heading th"
                    >
                      Ingredient Name
                    </div>
                    <div style="width: 25%" class="shoppable-notes-heading th">
                      Notes
                    </div>
                    <div
                      style="width: 10%"
                      class="shoppable-quantity-heading th"
                    >
                      Quantity
                    </div>
                    <div style="width: 15%" class="quantity-uom-heading th">
                      Uom
                    </div>
                    <div style="width: 25%" class="shoppable-heading th">
                      Shoppable
                    </div>
                  </div>
                </div>
                <div class="shoppable-review-table-body">
                  <div class="shoppable-review-table-body-row text-light-h3">
                    <div
                      style="width: 25%"
                      class="shoppable-ingredient-data td"
                    >
                      <span
                        class="ing-tool"
                        @mouseover="shoppableIngredientName(ingredient.id)"
                        @mouseleave="shoppableIngredientNamehide(ingredient.id)"
                        :class="{
                          'simple-data-tooltip': isShoppableIngredientNameVisible
                        }"
                        :data-tooltip-text="isShoppableIngredientNameVisible && ingredient.name"
                      >
                        <input
                          v-model.trim="ingredient.name"
                          @input="setCampaignModified(), debounceIngredientName(groupIndex,index,ingredient), changeReferenceIdToNull(ingredient)"
                          @keydown="shoppableIngredientNamehide(ingredient.id)"
                          type="text"
                          :id="`foodItemName${ingredient.id}`"
                          :disabled="
                            ingredient.loading ||
                            isReadOnlyProvider(recipeProvider)
                          "
                          class="filter-select-input text-light-h3"
                          autocomplete="off"
                          tabindex="0"
                          dir=""
                          autocapitalize="off"
                          placeholder="Enter Ingredient Name"
                          @keypress="preventSpecialCharacters($event)"
                        />
                      </span>
                    </div>
                    <div style="width: 25%" class="shoppable-notes-data td">
                      <span
                        class="ing-note"
                        @mouseover="shoppableIngredientNotes(ingredient.id)"
                        @mouseleave="shopabbleIngredientNotesHide(ingredient.id)"
                        :class="{
                          'simple-data-tooltip': isNoteTooltipVisible
                        }"
                        :data-tooltip-text="isNoteTooltipVisible && ingredient.note"
                      >
                        <input
                          :id="`ingredientNote${ingredient.id}`"
                          :disabled="
                            ingredient.loading ||
                            isReadOnlyProvider(recipeProvider)
                          "
                          v-model="ingredient.note"
                          @input="setCampaignModified()"
                          @keydown="shopabbleIngredientNotesHide(ingredient.id)"
                          type="text"
                          class="filter-select-input text-light-h3"
                          autocomplete="off"
                          tabindex="0"
                          dir=""
                          autocapitalize="off"
                          placeholder="Ingredient note (optional)"
                        />
                      </span>
                    </div>
                    <div style="width: 10%" class="shoppable-quantity-data td">
                      <input
                        type="Number"
                        :id="`quantity${ingredient.id}`"
                        :disabled="
                          ingredient.loading ||
                          isReadOnlyProvider(recipeProvider)
                        "
                        v-model="ingredient.quantity"
                        @input="setCampaignModified()"
                        @keypress="restrictNumericInput($event)"
                        @change="
                          handleIngredientQuantityChange(ingredient),
                            disableScroll()
                        "
                        @paste="handlePaste($event,'shoppableQuantity',index,groupIndex)"
                        v-on:blur="getQuantity(ingredient)"
                        class="filter-select-input text-light-h3 no-scroll"
                        autocomplete="off"
                        tabindex="0"
                        dir=""
                        min="0"
                        max="1000"
                        autocapitalize="off"
                      />
                    </div>
                    <div
                      :id="`uomDropDown${ingredient.id}`"
                      style="width: 15%"
                      class="quantity-uom-data td"
                    >
                      <input
                        :id="`ingredientUom${ingredient.id}`"
                        :disabled="
                          ingredient.loading ||
                          isReadOnlyProvider(recipeProvider)
                        "
                        :class="isReadOnlyProvider(recipeProvider) ? '' : 'left-ing'"
                        v-model="ingredient.UOM"
                        type="text"
                        class="filter-select-input text-light-h3 uom-input"
                        autocomplete="off"
                        tabindex="0"
                        dir=""
                        autocapitalize="off"
                        @blur="closeUomDropdownAsync(ingredient)"
                        @input="
                          toggleDropdownUOM(ingredient),
                            searchUOMList(ingredient),
                            saveButtonEnable()
                        "
                        @keyup.down="
                          ingredientUomAutocompleteArrowDown(ingredient)
                        "
                        @keyup.up="ingredientUomAutocompleteArrowUp(ingredient)"
                        @keyup.enter="
                          ingredientUomAutocompleteEnter(ingredient)
                        "
                        @click.stop="
                          toggleDropdownUOM(ingredient),
                            searchUOMList(ingredient)
                        "
                        @keypress="restrictToAlphabets($event)"
                      />
                      <div
                        class="filter-icon-box"
                        v-if="!isReadOnlyProvider(recipeProvider)"
                        :class="{
                          'dropdown-disabled':
                            ingredient.loading ||
                            isReadOnlyProvider(recipeProvider),
                        }"
                      >
                        <img alt=""
                          src="@/assets/images/arrow-right.png"
                          @click="toggleDropdownAsync(ingredient, index)"
                          class="dropdown-icon"
                          :class="{
                            rotate:
                              ingredient.uomAutocomplete && ingredientsUomList,
                          }"
                        />
                      </div>
                      <div
                        class="autocomplete-results-nouom text-title-2 font-normal"
                        v-show="
                          ingredient.uomAutocomplete && !searchedUomText == ''
                        "
                      >
                        {{ $t('COMMON.NO_RESULT') }}
                      </div>
                      <ul
                        v-if="ingredient.uomAutocomplete"
                        class="autocomplete-results text-title-2 font-normal"
                        id="ingredientsUomList"
                      >
                        <div v-if="searchedUomText == ''">
                          <li
                            v-for="(result, i) in ingredientsUomList"
                            :key="i"
                            :class="{
                              'autocomplete-result': true,
                              'is-active':
                                i === ingredientUomAutocompleteArrowCounter,
                            }"
                            @click.prevent="
                              setIngredientUomResult(result, ingredient)
                            "
                          >
                            {{ result.display }}
                          </li>
                        </div>
                        <div v-if="searchedUomText !== ''">
                          <li
                            v-for="(result, i) in searchedUomList"
                            :key="i"
                            :class="{
                              'autocomplete-result': true,
                              'is-active':
                                i === ingredientUomAutocompleteArrowCounter,
                            }"
                            @click.prevent="
                              setIngredientUomResult(result, ingredient)
                            "
                          >
                            {{ result.display }}
                          </li>
                        </div>
                      </ul>
                    </div>
                    <div style="width: 25%" class="shoppable-data td">
                      <div
                        :id="`shoppableDropDown${ingredient.id}`"
                        class="shoppable-container"
                      >
                        <div class="shoppable-sub-container"
                          @click.stop="openShoppableDropDown(ingredient)">
                          <img alt=""
                            src="@/assets/images/arrow-right.png"
                            class="dropdown-shoppable-data"
                            :class="{
                              rotate: ingredient.isShoppableDropDown,
                              'dropdown-disabled': ingredient.loading,
                            }"
                          />
                          <div
                            v-if="ingredient.isShoppableDropDown"
                            class="autocomplete-results-shoppable"
                          >
                            <div
                              v-for="(item, index) in shoppableListData"
                              :key="index"
                            >
                              <div
                                :class="{
                                  'autocomplete-result-shoppable': true,
                                  'is-active': ingredient&&ingredient.campaignData&&ingredient.campaignData.shoppableFlag &&(ingredient.campaignData.shoppableFlag==item.key),
                                }"
                                @click="selectedShoppable(ingredient, item.key)"
                              >
                                {{ item.data }} {{ item.subName }}
                              </div>
                            </div>
                          </div>
                          <div class="shoppable-text-area">
                            <p class="shoppable-name-text text-light-h3">
                              {{ getIngredientShoppable(ingredient) }} &nbsp;
                            </p>
                            <p class="shoppable-sub-name-text text-light-h3">
                              {{ getIngredientShoppableSubName(ingredient) }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="ingredient.showTableDetails"
                    class="show-more-detail"
                  >
                    <div class="line"></div>
                    <div class="shoppable-review-ingredient" v-if="isAdmin">
                      <div class="shoppable-review-ingredient-keywords">
                        <span
                          v-if="
                            ingredient.globalKeyword.length > 0 &&
                            ingredient.keywords.length == 0
                          "
                          class="ingredient-keyword-text text-h3"
                        >
                          Global Keywords:
                        </span>
                        <span
                          v-if="ingredient.keywords.length > 0"
                          class="ingredient-keyword-text text-h3"
                        >
                          Keywords:
                        </span>
                        <span
                          v-if="
                            ingredient.globalKeyword.length == 0 &&
                            ingredient.keywords.length == 0
                          "
                          class="ingredient-keyword-text text-h3"
                        >
                          Keywords:
                        </span>
                        <div class="head-container-breadcrumb">
                          <div
                            v-if="
                              ingredient.globalKeyword.length > 0 &&
                              ingredient.keywords.length == 0
                            "
                            class="main-container-breadcrumb"
                          >
                            <div
                              v-for="(data, index) in ingredient.globalKeyword"
                              :key="index"
                              class="breadcrumb"
                            >
                              <div
                                class="data-name"
                                :id="`ingredientPopProductNamesrt${ingredient.id}${groupIndex}${index}`"
                                @mouseover="
                                  checkIngredientPopNameSrt(
                                    ingredient.id,
                                    groupIndex,
                                    index
                                  )
                                "
                                @mouseleave="
                                  hideIngredientNamePopTipSrt(
                                    ingredient.id,
                                    groupIndex,
                                    index
                                  )
                                "
                                :class="{
                                  'simple-data-tooltip': isIngredientProductNameVisible
                                }"
                                :data-tooltip-text="isIngredientProductNameVisible && data"
                              >
                                {{ data }}
                              </div>
                            </div>
                            <div
                              v-if="
                                ingredient.globalKeyword.length > 0 &&
                                ingredient.keywords.length == 0
                              "
                              class="edit-breadcrumb text-h3"
                              @click="
                                ingredientKeywordsPopup(
                                  groupIndex,
                                  index,
                                  ingredient.name,
                                  ingredient.UOM,
                                  ingredient.quantity,
                                  ingredient.note,
                                  ingredient.keywords,
                                  ingredient.globalKeyword,
                                  ingredient,
                                  OVERRIDE
                                )
                              "
                            >
                              Override
                            </div>
                          </div>
                          <div
                            v-if="ingredient.keywords.length > 0"
                            class="main-container-breadcrumb"
                          >
                            <div
                              v-for="(data, index) in ingredient.keywords"
                              :key="index"
                              class="breadcrumb"
                            >
                              <div
                                class="data-name"
                                :id="`ingredientPopProductName${ingredient.id}${groupIndex}${index}`"
                                @mouseover="
                                  checkIngredientPopName(
                                    ingredient.id,
                                    groupIndex,
                                    index
                                  )
                                "
                                @mouseleave="
                                  hideIngredientNamePopTip(
                                    ingredient.id,
                                    groupIndex,
                                    index
                                  )
                                "
                                :class="{
                                  'simple-data-tooltip': isIngProductNameVisible
                                }"
                                :data-tooltip-text="isIngProductNameVisible && data"
                              >
                                {{ data }}
                              </div>
                            </div>
                            <div
                              class="edit-breadcrumb text-h3"
                              @click="
                                ingredientKeywordsPopup(
                                  groupIndex,
                                  index,
                                  ingredient.name,
                                  ingredient.UOM,
                                  ingredient.quantity,
                                  ingredient.note,
                                  ingredient.keywords,
                                  ingredient.globalKeyword,
                                  ingredient,
                                  EDIT
                                )
                              "
                            >
                              {{ $t('BUTTONS.EDIT_BUTTON') }}
                            </div>
                          </div>
                          <div class="main-container-breadcrumb">
                            <div
                              v-if="
                                ingredient.globalKeyword.length == 0 &&
                                ingredient.keywords.length == 0
                              "
                              class="edit-breadcrumb text-h3"
                              @click="
                                ingredientKeywordsPopup(
                                  groupIndex,
                                  index,
                                  ingredient.name,
                                  ingredient.UOM,
                                  ingredient.quantity,
                                  ingredient.note,
                                  ingredient.keywords,
                                  ingredient.globalKeyword,
                                  ingredient,
                                  ADD
                                )
                              "
                            >
                              Add
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        v-if="
                          !ingredient?.weightInGrams &&
                          !ingredient?.volumeInMl
                        "
                        class="ingredient-size"
                      >
                        <span class="ingredient-size-text text-h3">{{ $t("SHOPPABLE_REVIEW.SIZE") }}:</span>
                        <div class="ingredient-add-size-container text-h3">
                          <button
                            type="button"
                            class="btn-reset add-button text-head-title-1"
                            @click="
                              showAddQuantityMethod(
                                groupIndex,
                                index,
                                ingredient.name,
                                ingredient.UOM,
                                ingredient.quantity,
                                ingredient.note
                              )
                            "
                            >{{ $t("SHOPPABLE_REVIEW.ADD") }}</button
                          >
                        </div>
                      </div>
                      <div
                        v-if="
                          ingredient.weightInGrams ||
                          ingredient.volumeInMl
                        "
                        class="ingredient-numeric-size"
                      >
                        <div class="ingredient-numeric-heading text-h3">Size:</div>
                          <div class="numeric-text">
                            <template v-if="ingredient?.weightInGrams">
                              <span class="ing-count">{{ ingredient.weightInGrams }}</span>
                              <span class="ing-value">g</span>
                            </template>

                            <template v-if="ingredient?.volumeInMl">
                              <span class="ing-count">{{ ingredient.volumeInMl }}</span>
                              <span class="ing-value">ml</span>
                            </template>

                            <button type="button" class="close-icon btn-reset" @click="removeIngredientWeightAsync(groupIndex, index)">
                              <img
                                alt="Remove"
                                src="@/assets/images/exit-search.svg?skipsvgo=true"
                              />
                            </button>
                          </div>
                        </div>
                      <div v-if="isAdmin" class="computed-size">
                        <span class="computed-size-text text-h3">Computed size:</span>
                        <div v-if="!ingredient.loading && !ingredient.size.weightInGrams && !ingredient.size.volumeInMl" class="computed-size-no-data text-light-h3">
                          no data
                        </div>
                        <div v-if="!ingredient.loading && Object.keys(ingredient.size).length > 0" class="computed-size-with-data text-light-h3">
                          <span v-if="ingredient.size.weightInGrams">{{ ingredient.size.weightInGrams }}</span>
                          <span v-if="ingredient.size.weightInGrams" class="computed-unit">g</span>
                          <span v-if="ingredient.size.weightInGrams && ingredient.size.volumeInMl">&nbsp;/&nbsp;</span>
                          <span v-if="ingredient.size.volumeInMl"> {{ ingredient.size.volumeInMl }}</span>
                          <span v-if="ingredient.size.volumeInMl" class="computed-unit">ml</span>
                        </div>
                      </div>
                    </div>
                    <div class="shoppable-admin-weight" v-if="!isAdmin">
                      <div
                        v-if="
                          ingredient.weightInGrams == 0 &&
                          ingredient.volumeInMl == 0
                        "
                        class="ingredient-size"
                      >
                        <span class="ingredient-size-text text-h3">Size:</span>
                        <div class="ingredient-add-size-container text-h3">
                          <span
                            @click="
                              showAddQuantityMethod(
                                groupIndex,
                                index,
                                ingredient.name,
                                ingredient.UOM,
                                ingredient.quantity,
                                ingredient.note
                              )
                            "
                            >Add</span
                          >
                        </div>
                      </div>
                      <div
                        v-if="
                          ingredient.weightInGrams > 0 ||
                          ingredient.volumeInMl > 0
                        "
                        class="ingredient-numeric-size"
                      >
                        <div class="ingredient-numeric-heading text-h3">Size:</div>
                        <div class="numeric-text">
                          <span
                            class="ing-count"
                            v-if="ingredient.weightInGrams"
                          >
                            <span>
                              {{
                                ingredient && ingredient.weightInGrams
                                  ? ingredient.weightInGrams
                                  : ""
                              }}
                            </span>
                          </span>
                          <span
                            class="ing-value"
                            v-if="ingredient.weightInGrams"
                            >g</span
                          >
                          <span
                            class="ing-count"
                            v-if="ingredient.volumeInMl"
                          >
                            <span>
                              {{
                                ingredient && ingredient.volumeInMl
                                  ? ingredient.volumeInMl
                                  : ""
                              }}
                            </span>
                          </span>
                          <span
                            class="ing-value"
                            v-if="ingredient.volumeInMl"
                            >ml</span
                          >
                          <div
                            class="close-icon"
                            @click="removeIngredientWeightAsync(groupIndex, index)"
                          >
                            <img alt=""
                              width="10px"
                              height="12px"
                              src="@/assets/images/exit-search.svg?skipsvgo=true"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <img alt=""
                    @click="showMoreDetails(ingredient)"
                    src="@/assets/images/arrow-down-green.png"
                    class="dropdown-icon-show-more-detail"
                    :class="{
                      rotate: ingredient.showTableDetails,
                    }"
                  />
                </div>
              </div>
              <div
                class="not-shoppable-products"
                v-show="
                  ingredient &&
                  ingredient.campaignData &&
                  ingredient.campaignData.shoppableFlag &&
                  ingredient.campaignData.shoppableFlag == 'nonShoppable'
                "
              >
                <span class="not-shoppable-text text-light-h3"
                  >This ingredient is not shoppable. Make shoppable to view
                  products.</span
                >
              </div>
              <div
                v-if="ingredient.promotedTotalProducts == 0 && ingredient.totalProducts == 0 &&
                ingredient.products.length == 0 && ingredient.displayAllOption && ingredient.shoppableSearchQuery == '' && !ingredient.isSearchOn"
                class="non-products"
              >
                <span class="text text-light-h3"> No Promoted Products. No Product Matches.</span>
              </div>
              <div
                v-else
                class="main-product-container"
                >
                <div
                  :class="isFilterDropdownVisible ?'shoppable-promoted-products':'shoppable-promoted-products end'"
                  v-show="
                    ingredient &&
                    ingredient.campaignData &&
                    ingredient.campaignData.shoppableFlag &&
                    ingredient.campaignData.shoppableFlag != 'nonShoppable'
                  "
                >
                  <div :id="`scrollTag${ingredient.id}`" v-if="isFilterDropdownVisible" class="filter text-h3">
                      <div class="filter-text"> TAG FILTER</div>
                      <div class="filter-search-tag-popup">
                        <div
                          class="brand-details"
                          @click.stop="setFilterBrandPopupVisible(ingredient, $event)"
                        >
                          <div class="brand-selected-name">
                            <span
                              v-if="ingredient.displayAllOption"
                              class="brand-selected"
                              >{{ $t('ALL') }}</span>
                            <span
                              v-if="ingredient.displayBrand != ''"
                              class="brand-selected"
                              >{{ingredient.displayBrand == "Gluten Free Verified" ? "Gluten Free" : ingredient.displayBrand}}</span>
                            <span
                              v-if="ingredient.displaycount > 1 && ingredient.displayBrand == ''"
                              class="brand-selected"
                              >{{ingredient.displaycount}}</span>
                          </div>
                          <div class="arrow"
                            >
                            <img alt=""
                              src="@/assets/images/orangeicondown.png"
                              class="brand-dropdown-icon"
                              :class="{
                                rotate:
                                ingredient.isFilterBrandPopupVisible
                              }"
                            />
                          </div>
                        </div>
                        <div
                        v-if="ingredient.displayCrossIcon"
                          class="line"
                        ></div >
                        <div
                        v-if="ingredient.displayCrossIcon"  class="exit-box-section" >
                        <img alt=""
                          class="exit-brand-icon"
                          @click.stop="resetSearchBrandAsync(ingredient)"
                          src="@/assets/images/close-red.png"
                        />
                      </div>
                    </div>
                    <div v-if="ingredient.isFilterBrandPopupVisible" :id="`filter-brand-popup-${ingredient.id}`" class="tag-filter-search-brand-main">
                    <div class="ingredient-brand-search-bar">
                      <div class="search-bar-content">
                        <img
                          alt=""
                          @click="searchBrand(ingredient)"
                          class="search-icon-grey-image"
                          src="@/assets/images/search-grey.png"
                        />
                        <input
                        autocomplete="off"
                        v-model.trim="ingredient.brandSearchQuery"
                        @input="searchBrand(ingredient)"
                        type="text"
                        class="search-bar-text text-title-2 font-normal"
                        placeholder="Search by Tags"
                        @keyup.enter="searchBrand(ingredient)"
                        @keyup.down="dietAutocompleteArrowDown()"
                        @keyup.up="dietAutocompleteArrowUp()"
                        :class="{ 'align-search-input-box': brandSearchQuery }"
                        />
                        <img v-if=" ingredient.brandSearchQuery.length > 0" alt="" id="ingredient-search-brand-reset-query-icon"
                        class="exit-search-icon" @click="resetSearchQuery(ingredient)" src="@/assets/images/exit-gray.png" />
                      </div>
                    </div>
                    <div ref="dropdown" class="ingredient-search-brand-data-main" :id="`button`">
                      <div v-if="isFilterBrandLoading" class="table-image-loader">
                        <div class="loader"></div>
                      </div>
                      <div  ref="addTable" v-if="!isFilterBrandLoading && ingredient.brandSearchQuery.length == 0" class="brand-details-checkbox">
                        <div data-ignore-outside-click="true" :class="{
                        'shoppable-brand-data-add-background': ingredient.isAllSelected}"  class="ingredient-brand-data" @click="selectAllFilter(ingredient, $event)" id="selectedAllBrand">
                          <div class="rounded">
                            <span class="recipe-checkbox-section">
                              <label class="container-promote" for="recipe-checkbox">
                                <input
                                  type="checkbox"
                                  id="recipe-checkbox"
                                  @click="selectAllFilter(ingredient, $event)"
                                  v-model="ingredient.isAllSelected"
                                />
                                <span class="checkmark"></span>
                              </label>
                            </span>
                          </div>
                          <div  class="brand-search-list-data">
                            <div @click="selectAllFilter(ingredient, $event)" class="search-brand-sort-name text-title-2 font-normal">
                              All
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        ref="addTable"
                        v-if="!isFilterBrandLoading"
                        class="brand-details-checkbox"
                      >
                        <div
                          v-for="(info, index) in ingredient.filteredFilterList?.length ? ingredient.filteredFilterList : ingredient.filterList"
                          :key="`searchListData${index}`"
                          ref="optionItems"
                          :id="`selected${index}`"
                          data-ignore-outside-click="true"
                          class="ingredient-brand-data"
                          :class="{
                            'shoppable-brand-data-add-background': info.isChecked,
                            'is-active': ingredient.filteredFilterList?.length && index === dietAutocompleteArrowCounter,
                          }"
                          @click="checkedBrandName(info, ingredient)"
                          @click.stop
                        >
                          <div class="rounded">
                            <span class="recipe-checkbox-section">
                              <label class="container-promote">
                                <input
                                  type="checkbox"
                                  v-model="info.isChecked"
                                  aria-label="Recipe Checkbox"
                                />
                                <span class="checkmark"></span>
                              </label>
                            </span>
                          </div>
                          <div class="brand-search-list-data">
                            <div class="search-brand-sort-name text-title-2 font-normal">
                              {{ info.display === "Gluten Free Verified" ? "Gluten Free" : info.display }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="no-result-for-brand text-title-2" v-if="!isFilterBrandLoading && ingredient.filteredFilterList.length == 0 && ingredient.brandSearchQuery.length >0 ">
                      {{ $t('COMMON.NO_RESULTS') }}
                    </div>
                    <div class="apply-btn-container">
                      <div class="filter-save-btn">
                        <button
                          type="button"
                          class="filter-btn text-h3"
                          :class="{ disabled: !isFilterApplyButtonEnabled }"
                          @click="applyFilterAsync(ingredient,true)" >
                          {{ $t('BUTTONS.APPLY_BUTTON') }}
                        </button>
                      </div>
                    </div>
                  </div>
                      </div>
                  <div
                    v-show="
                      ingredient &&
                      ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag &&
                      ingredient.campaignData.shoppableFlag !== 'nonShoppable'
                    "
                    class="show-only-promoted-products text-h3"
                    >
                    <div class="toggle-switch-promoted-section">
                      <span class="toggle-top" :class="{ disable: ingredient?.promotedTotalProducts === 0 }">{{ $t('SHOW_ONLY_PROMOTED_PRODUCTS') }}</span>
                      <label
                        :class="[
                          'switch',
                          { 'simple-data-tooltip edge': ingredient?.promotedTotalProducts === 0 }
                        ]"
                        :data-tooltip-text="ingredient?.promotedTotalProducts === 0 ? $t('COMMON.PROMOTE_RECIPE_WARNING') : null"
                        aria-label="switchIngredient"
                      >
                        <input
                          type="checkbox"
                          id="switch-ingredient"
                          @click="toggleOnlyPromoted(ingredient)"
                          :disabled="ingredient?.promotedTotalProducts === 0"
                          :checked="ingredient?.campaignData?.onlyPromoted || false"
                        />
                        <span
                          :class="[
                            'slider-round',
                            { 'inactive-button': ingredient?.promotedTotalProducts === 0 }
                          ]"
                        ></span>
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  v-show="ingredient &&
                    ingredient.campaignData &&
                    ingredient.campaignData.shoppableFlag &&
                    ingredient.campaignData.shoppableFlag != 'nonShoppable'"
                  class="promoted-products-count-section"
                  :id="`promoted-products-count-section-id-${ingredient.id}`"
                >
                  <div class="count">
                    {{ingredient && ingredient.promotedTotalProducts ? ingredient.promotedTotalProducts : 'No'}}&nbsp;
                    <span v-if="ingredient.promotedTotalProducts == 1">Promoted Product</span>
                    <span v-else>Promoted Products</span>
                  </div>
                  <div
                    v-if="ingredient.promotedTotalProducts == 0 &&
                      ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag &&
                      ingredient.campaignData.shoppableFlag != 'nonShoppable'"
                    class="tooltip-zero-promoted-message"
                    >
                      <div
                        class="tooltip-icon simple-data-tooltip"
                        :data-tooltip-text="promoteProductTooltip"
                      >
                        <img alt="info" src="@/assets/images/informationSymbol.png"/>
                      </div>
                  </div>
                </div>
                <div class="no-product-line"
                  v-if="ingredient.promotedTotalProducts == 0 &&
                  ingredient.campaignData &&
                  ingredient.campaignData.shoppableFlag &&
                  ingredient.campaignData.shoppableFlag != 'nonShoppable'"
                  >
                </div>
                <div
                  v-if="!ingredient.showPromotedLoader"
                  v-show="
                    ingredient &&
                    ingredient.campaignData &&
                    ingredient.campaignData.shoppableFlag &&
                    ingredient.campaignData.shoppableFlag != 'nonShoppable'
                  "
                  class="shop-preview-popup"
                  :class="ingredient.promotedTotalProducts != 0 ? 'extra-top-margin' : ''"
                >
                <draggable
                  :list="ingredient.promotedResult"
                  class="list-group"
                  :scroll-sensitivity="200"
                  :force-fallback="true"
                  ghost-class="hidden-list"
                  @update="updateArrayAsync(ingredient)"
                  @change="disableTip()"
                >
                  <div
                    class="slider-main-section-for-pagination"
                    v-for="(slide, index) in ingredient.promotedResult"
                    :key="index"
                  >
                    <div  class="card-container-for-shoppable" id="Slider">
                      <div :id="slide && slide.isDisabled && slide.isDisabled ? 'disabled' : 'nd'" :class="!isAdmin ? 'decrease-height-shop-preview-card' : ''" class="shop-preview-card card-container-promoted">
                        <div class="shop-preview-card-navigation">
                          <div
                            class="qty recipe-name-tooltip-qty simple-data-tooltip"
                            @click="setProductForReport(ingredient, slide)"
                            :data-tooltip-text="clickToReportTooltip"
                          >
                            {{
                              slide.shoppable && slide.shoppable.quantity
                                ? slide.shoppable.quantity + " "
                                : ""
                            }}qty
                          </div>
                          <span class="shop-preview-card-span-image"
                            ><img
                              class="shop-preview-card-image"
                              :src="
                                slide.image &&
                                slide.image.sizes &&
                                slide.image.sizes[300]
                                  ? slide.image.url
                                  : ''
                              "
                              alt=""
                          /></span>
                          <div
                            :id="`changePosition${slide.id}`"
                            class="promoted-product-counting-container"
                          >
                            <span
                              v-if="!slide.changePositionPopupOpened"
                              @click.stop="openPromotedCountingBox(slide,ingredient,countingOfPromotedIngrediet(ingredient) + index + 1)"
                              class="promoted-product-counting"
                            >
                              {{ countingOfPromotedIngrediet(ingredient) + index + 1 }}
                            </span>
                            <span
                              v-if="slide.changePositionPopupOpened"
                              @click="closeAllPromotedCountingBox()"
                              class="promoted-product-counting"
                            >
                            {{ countingOfPromotedIngrediet(ingredient) + index + 1 }}
                            </span>
                            <div
                              v-if="slide.changePositionPopupOpened"
                              class="autocomplete-results-promoted"
                            >
                              <div
                                v-for="number in ingredient.setPromotedCountingList"
                                :key="`positionCounting${number.position}${index}`"
                              >
                              <div
                                v-if="number.position != (countingOfPromotedIngrediet(ingredient) + index + 1)
                                  "
                                  :class="{
                                    'autocomplete-result-promoted': true,
                                    'is-active': '',
                                  }"
                                  @click="
                                    selectedPositionAsync(
                                      ingredient,
                                      slide,
                                      number.position
                                    )
                                  "
                                >
                                  <span>{{ number.position }}</span>
                                </div>
                                <div
                                  class="disable-current-position"
                                  @click="
                                    selectedPositionAsync(
                                      ingredient,
                                      slide,
                                      number.position
                                    )
                                  "
                                  v-if="number.position == (countingOfPromotedIngrediet(ingredient) + index + 1)
                                  "
                                >
                                  <span>{{ number.position }}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <span style="width: 23px; height: 17px"></span>
                        </div>
                        <div class="promoted-recipe-logo">
                          <span class="promoted-recipe-text">Promoted</span>
                        </div>
                        <div
                          class="recipe-name-tooltip"
                          v-if="slide.name || slide.brand"
                        >
                          <span
                            v-if="
                              setIngredientName(slide.brand, slide.name)
                                .nameLength <= 30
                            "
                          >
                            {{ setIngredientName(slide.brand, slide.name).name }}
                          </span>
                          <span
                            v-if="
                              setIngredientName(slide.brand, slide.name)
                                .nameLength > 30
                            "
                          >
                            {{
                              setIngredientName(
                                slide.brand,
                                slide.name
                              ).name.substring(0, 30) + "..."
                            }}
                          </span>
                          <span
                            v-if="
                              setIngredientName(slide.brand, slide.name)
                                .nameLength > 30
                            "
                            class="tool-tip-text"
                          >
                            {{ setIngredientName(slide.brand, slide.name).name }}
                          </span>
                        </div>
                        <div class="shop-preview-ingredient-price-info-container">
                          <div
                            v-if="
                                slide.inventory &&
                                slide.inventory[0] &&
                                slide.inventory[0].price
                              "
                              class="shop-preview-card-price"
                            >
                            <span
                            >
                              $&nbsp;{{
                                slide.inventory &&
                                slide.inventory[0] &&
                                slide.inventory[0].price
                                  ? slide.inventory[0].price
                                  : ""
                              }}
                            </span>
                          </div>
                          <div v-if="displayInfoIcon" class="shop-preview-info-icon-section">
                            <span
                              @mouseover="showInfoTip(slide,index,slide && slide.labels.desirable && slide.labels.desirable.length >0 ? slide.labels.desirable : '')"
                              @mouseleave="hideInfoTip(slide)"
                              @click="openProductTag(ingredient, slide, true)"
                              class="info-icon-image"
                            >
                              <img alt="" src="@/assets/images/info-icon-blue.svg?skipsvgo=true"/>
                              <span :style="styleForTooltip" :id="`product${slide.id}`" class="info-tooltip">
                                <div v-show="tagText.length>0" class="info-tooltip-text-intro">
                                  <div class="info-text-heading">{{ $t('TAG.TAG_TEXT')  }}:</div>
                                  <div class="info-text-content">
                                    <div v-if="tagText" class="info-tag-content">
                                      {{ tagText }}
                                    </div>
                                  </div>
                                </div>
                                <div class="info-availability-text">*availability to buy in <span class="info-count">{{ slide.numberOfStores }}</span> stores.</div>
                              </span>
                            </span>
                          </div>
                        </div>
                        <div class="shop-preview-card-quantity">
                          <span
                            v-if="
                              slide.size &&
                              slide.size.value &&
                              slide.size.unit &&
                              (slide.size.unit.abbreviation ||
                                slide.size.unit.display)
                            "
                          >
                            {{ slide.size.value }}
                            {{
                              slide.size.unit.abbreviation
                                ? slide.size.unit.abbreviation
                                : slide.size.unit.display.toLowerCase()
                            }}
                          </span>
                        </div>
                        <div v-if="isAdmin" class="shop-preview-card-size">
                          <span v-if="slide.size.weightInGrams">{{slide.size.weightInGrams}}g</span>
                          <span class="parallel-icon" v-if="slide.size.weightInGrams && slide.size.volumeInMl">|</span>
                          <span v-if="slide.size.volumeInMl">{{slide.size.volumeInMl}}ml</span>
                        </div>
                        <div class="shop-preview-card-number">
                          {{ getProductId(slide) }}
                        </div>
                        <div class="shop-preview-card-btn-container">
                          <button
                            class="btn-green-outline"
                            @click="unPromoteProductAsync(ingredient,slide)"
                            @keydown="preventEnterAndSpaceKeyPress($event)"
                          >
                            {{ $t('COMMON.UNPROMOTE') }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  </draggable>
                  <div
                    v-show="
                      ingredient &&
                      ingredient.promotedResult &&
                      ingredient.promotedResult.length
                    "
                    class="empty-card-for-shoppable"
                    v-for="(recipe, index) in numProductMatches -
                    (ingredient &&
                    ingredient.promotedResult &&
                    ingredient.promotedResult.length
                      ? ingredient.promotedResult.length
                      : 0)"
                    :key="`empty${index}`"
                  ></div>
                </div>
                <div
                  class="loader-main-container-shoppable-review"
                  v-show="ingredient.showPromotedLoader"
                  @close="closeModal"
                >
                  <div class="loading-content">
                    <div class="content">
                      <div class="input-loading">
                        <div class="loader-image"></div>
                      </div>
                      <div class="loading-text">
                        <p>{{ $t('LOADER.LOADING') }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  @click="onPaginationPromoted(ingredient)"
                  v-show="
                    ingredient &&
                    ingredient.campaignData &&
                    ingredient.campaignData.shoppableFlag &&
                    ingredient.campaignData.shoppableFlag != 'nonShoppable'
                  "
                >
                <paginate
                  id="pagination-block"
                  v-if="(ingredient &&ingredient.promotedTotalProducts
                  ? ingredient.promotedTotalProducts
                  : 0) > numProductMatches  && !ingredient.showPromotedLoader"
                  v-model="ingredient.currentPagePromoted"
                  :total-rows="ingredient && ingredient.promotedTotalProducts
                  ? ingredient.promotedTotalProducts
                  : 0"
                  :page-range="pageRange"
                  :per-page="numProductMatches"
                  :page-count="
                    Math.ceil(
                      (ingredient &&ingredient.promotedTotalProducts
                        ? ingredient.promotedTotalProducts
                        : 0) / numProductMatches
                    )
                  "
                  prev-text="<"
                  next-text=">"
                  :prev-class="'prev-promoted'"
                  :next-class="'next-promoted'"
                  :click-handler="pageChangePromotedAsync"
                  :container-class="'pagination-unpromoted'"
                  :page-class="'page-item-unpromoted'"
                  :page-link-class="'page-link-unpromoted'"
                  :disabled-class="'disabled-pagination'"
                  :active-class="'active-promoted'"
                  :margin-pages="marginPages"
                >
                </paginate>
                </div>
                <div
                  class="product-matches-section"
                >
                  <div
                    class="product-matches-title-search"
                    :id="`product-matches-title-search-id-${ingredient.id}`"
                    v-show="
                      ingredient &&
                      ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag &&
                      ingredient.campaignData.shoppableFlag != 'nonShoppable'
                    "
                  >
                    <div v-show="
                      ingredient &&
                      ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag &&
                      ingredient.campaignData.shoppableFlag != 'nonShoppable' &&
                      ingredient.totalProducts !== 0
                    " class="product-matches-title">
                    {{ ingredient&&ingredient.showUnpromotedLoader?'...':((ingredient&&ingredient.currentPageUnPromoted?ingredient.currentPageUnPromoted-1:0)*numProductMatches)+(ingredient&&ingredient.products&&ingredient.products.length?ingredient.products.length:0) }}/{{
                        ingredient.totalProducts ? ingredient.totalProducts : 0
                      }}
                      Product Matches
                    <span v-if="ingredient.campaignData.onlyPromoted" class="not-applicable">(not applicable)</span>
                    </div>
                    <div
                      v-show="
                        ingredient &&
                        ingredient.campaignData &&
                        ingredient.campaignData.shoppableFlag &&
                        ingredient.campaignData.shoppableFlag != 'nonShoppable' &&
                        ingredient.totalProducts == 0 &&
                        ingredient.products.length == 0
                      " class="product-matches-title"
                      >
                        No Product Matches
                        <div v-if="!ingredient.displayAllOption" class="tag-filter-zero-product-tooltip-section">
                          <div class="tooltip-icon simple-data-tooltip"
                            :data-tooltip-text="zeroMatchesFilterTooltip"
                          >
                            <img alt="info" src="@/assets/images/informationSymbol.png"/>
                          </div>
                        </div>
                    </div>
                  </div>
                  <div v-show="ingredient.totalProducts == 0 && ingredient.products.length == 0" class="zero-product-matches-line"></div>
                  <div
                    class="product-matches-title-search"
                    v-show="
                      ingredient &&
                      ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag &&
                      ingredient.campaignData.shoppableFlag == 'nonShoppable'
                    "
                  >
                    <div class="product-matches-title">
                      Product Matches
                    </div>
                  </div>
                  <div
                    v-show="
                      ingredient && ingredient.campaignData &&
                      ingredient.campaignData.shoppableFlag != 'nonShoppable' &&
                      ((!ingredient.isSearchOn && ingredient.totalProducts != 0 && ingredient.shoppableSearchQuery == '') || (ingredient.shoppableSearchQuery != ''))
                    "
                    class="filter-section"
                    :class="isProductSortEnabled ? '' : 'filter-section-no-sort'"
                    >
                    <div v-if="isProductSortEnabled" class="sort-by-section-main-container">
                      <div class="sort-by-section-main">
                        <div class="sort-by-heading text-h3">
                          {{ $t('SORT_BY') }}
                        </div>
                        <div :id="`sortByDropDown${ingredient.id}`" class="sort-by-result-main" @click.stop="openSortByDropdown(ingredient)">
                          <div class="sort-by-result-text text-title-2 font-normal">
                            <span v-if="ingredient.sortByDataName == ''">Default</span>
                            <span v-if="ingredient.sortByDataName != ''">{{ ingredient.sortByDataName }}</span>
                            <div class="sort-by-arrow-icon">
                              <img
                                alt=""
                                src="@/assets/images/arrow-right.png"
                                :class="
                                  ingredient.sortByDropdownOpen
                                    ? 'sort-by-dropdown-icon-open'
                                    : 'sort-by-dropdown-icon-close'
                                "
                              />
                            </div>
                          </div>
                          <div v-if="ingredient.sortByDropdownOpen" class="sort-by-dropdown-result">
                            <div v-for="(data, index) in productSortOptionsData" :key="index" class="sort-by-result-main-container" @click="selectSortByAsync(ingredient,data)"
                              :class="{'is-active': data && data.display === ingredient.sortByDataName}">
                              <div class="sort-by-result-content">
                                <span class="sort-by-result-content-text text-title-2 font-normal">
                                  {{data.display}}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="product-matches-search">
                      <div class="search-box-shoppable-review">
                        <input
                          autocomplete="off"
                          type="text"
                          class="shoppable-review-input-box"
                          placeholder="Search by"
                          v-model.trim="ingredient.shoppableSearchQuery"
                          :disabled="ingredient.loading"
                          @keyup.enter="ingredient.loading ?  '' : searchProductMatchesAsync(ingredient)"
                          @input="checkSearchProductMatches(ingredient)"
                          :class="{
                            'align-search-input-box':
                              ingredient.shoppableSearchQuery,
                          }"
                        />
                        <div>
                          <button v-if="ingredient.isSearchOn" class="exit-shoppable-review-ingredient btn-reset"
                            @click="resetShoppableProductsAsync(ingredient)">
                            <img alt="cross" src="@/assets/images/exit-gray.png" />
                          </button>

                          <button :disabled="ingredient.loading || !ingredient.shoppableSearchQuery"
                            class="shoppable-review-icon-green-edit-product-image btn-reset"
                            @click="searchProductMatchesAsync(ingredient)">
                            <img alt="search" src="@/assets/images/search-icon-green.png" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                    <!-- dropdown -->
                  <div class="product-matches-box-container">
                    <div
                      class="loading-product-matches-shoppable"
                      v-if="ingredient.errorLoading"
                    >
                      Error Loading Product Matches
                    </div>
                    <div
                      class="loading-product-matches-shoppable"
                      v-else-if="ingredient.loading && ingredient.name != ''"
                    >
                      Loading Product Matches
                    </div>
                    <div
                      class="no-product-matches-shoppable"
                      v-else-if="
                        ingredient.products &&
                        ingredient.products.length == 0 &&
                        ingredient.isSearchOn
                      "
                    >
                      {{ $t('COMMON.NO_RESULTS') }}
                    </div>
                    <div
                      :class="
                        ingredient &&
                        ingredient.campaignData &&
                        ingredient.campaignData.shoppableFlag &&
                        ingredient.campaignData.shoppableFlag == 'nonShoppable'
                          ? 'shop-preview-popup product-disable'
                          : 'shop-preview-popup'"
                      v-else-if="!ingredient.showUnpromotedLoader"
                    >
                      <div
                        class="slider-main-section-for-pagination"
                        v-for="(slide, index) in ingredient.products"
                        :key="index"
                      >
                        <div class="card-container-for-shoppable" id="Slider">
                          <div :class="!isAdmin ? 'decrease-height-shop-preview-card' : ''" class="shop-preview-card">
                            <div class="shop-preview-card-navigation">
                              <div
                                class="qty recipe-name-tooltip-qty simple-data-tooltip"
                                @click.stop="setProductForReport(ingredient, slide)"
                                :data-tooltip-text="clickToReportTooltip"
                              >
                                {{
                                  slide.shoppable && slide.shoppable.quantity
                                    ? slide.shoppable.quantity + " "
                                    : ""
                                }}qty
                              </div>
                              <span class="shop-preview-card-span-image"
                                ><img
                                  class="shop-preview-card-image"
                                  :src="
                                    slide.image &&
                                    slide.image.sizes &&
                                    slide.image.sizes[300]
                                      ? slide.image.url
                                      : ''
                                  "
                                  alt=""
                              /></span>
                              <span style="width: 23px; height: 17px"></span>
                              <span class="menu">
                                <div
                                  @click.stop="setProductForRemove(ingredient, slide)"
                                >
                                  <img
                                  alt=""
                                    class="edit-btn"
                                    src="~/assets/images/delete-icon.png"
                                  />
                                </div>
                              </span>
                            </div>
                            <div
                              class="recipe-name-tooltip"
                              v-if="slide.name || slide.brand"
                            >
                              <span
                                v-if="
                                  setIngredientName(slide.brand, slide.name)
                                    .nameLength <= 30
                                "
                              >
                                {{
                                  setIngredientName(slide.brand, slide.name).name
                                }}
                              </span>
                              <span
                                v-if="
                                  setIngredientName(slide.brand, slide.name)
                                    .nameLength > 30
                                "
                              >
                                {{
                                  setIngredientName(
                                    slide.brand,
                                    slide.name
                                  ).name.substring(0, 30) + "..."
                                }}
                              </span>
                              <span
                                v-if="
                                  setIngredientName(slide.brand, slide.name)
                                    .nameLength > 30
                                "
                                class="tool-tip-text"
                              >
                                {{
                                  setIngredientName(slide.brand, slide.name).name
                                }}
                              </span>
                            </div>
                            <div class="shop-preview-ingredient-price-info-container">
                              <div
                                  v-if="
                                    slide.inventory &&
                                    slide.inventory[0] &&
                                    slide.inventory[0].price
                                  "
                                  class="shop-preview-card-price"
                                >
                                <span
                                >
                                  $&nbsp;{{
                                    slide.inventory &&
                                    slide.inventory[0] &&
                                    slide.inventory[0].price
                                      ? slide.inventory[0].price
                                      : ""
                                  }}
                                </span>
                              </div>
                              <div v-if="displayInfoIcon" class="shop-preview-info-icon-section">
                                <span @mouseover="showInfoTip(slide,index,slide && slide.labels.desirable && slide.labels.desirable.length>0 ?slide.labels.desirable :'')" @mouseleave="hideInfoTip(slide)" @click="openProductTag(ingredient,slide, false)" class="info-icon-image">
                                  <img alt="" src="@/assets/images/informationSymbol.png"/>
                                  <span :style="styleForTooltip" :id="`product${slide.id}`" class="info-tooltip">
                                    <div v-show="tagText.length>0" class="info-tooltip-text-intro">
                                      <div class="info-text-heading">{{ $t('TAG.TAG_TEXT')  }}:</div>
                                      <div class="info-text-content">
                                        <div v-if="tagText" class="info-tag-content">
                                          {{ tagText }}
                                        </div>
                                      </div>
                                    </div>
                                    <div class="info-availability-text">*availability to buy in <span class="info-count">{{ slide.numberOfStores }}</span> stores.</div>
                                  </span>
                                </span>
                              </div>
                            </div>
                            <div class="shop-preview-card-quantity">
                              <span
                                v-if="
                                  slide.size &&
                                  slide.size.value &&
                                  slide.size.unit &&
                                  (slide.size.unit.abbreviation ||
                                    slide.size.unit.display)
                                "
                              >
                                {{ slide.size.value }}
                                {{
                                  slide.size.unit.abbreviation
                                    ? slide.size.unit.abbreviation
                                    : slide.size.unit.display.toLowerCase()
                                }}
                              </span>
                            </div>
                            <div v-if="isAdmin" class="shop-preview-card-size">
                              <span v-if="slide.size.weightInGrams">{{slide.size.weightInGrams}}g</span>
                              <span class="parallel-icon" v-if="slide.size.weightInGrams && slide.size.volumeInMl">|</span>
                              <span v-if="slide.size.volumeInMl">{{slide.size.volumeInMl}}ml</span>
                            </div>
                            <div class="shop-preview-card-number">
                              {{ getProductId(slide) }}
                            </div>
                            <div class="shop-preview-card-btn-container">
                              <button
                                class="btn-green-outline"
                                @click="promoteProductAsync(ingredient, slide)"
                                @keydown="preventEnterAndSpaceKeyPress($event)"
                              >
                                {{ $t('COMMON.PROMOTE') }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        v-show="
                          ingredient &&
                          ingredient.products &&
                          ingredient.products.length
                        "
                        class="empty-card-for-shoppable"
                        v-for="(recipe, index) in numProductMatches -
                        (ingredient &&
                        ingredient.products &&
                        ingredient.products.length
                          ? ingredient.products.length
                          : 0)"
                        :key="`empty${index}`"
                      ></div>
                    </div>
                    <div
                      class="loader-main-container-shoppable-review"
                      v-show="ingredient.showUnpromotedLoader"
                      @close="closeModal"
                    >
                      <div class="loading-content">
                        <div class="content">
                          <div class="input-loading">
                            <div class="loader-image"></div>
                          </div>
                          <div class="loading-text">
                            <p>{{ $t('LOADER.LOADING') }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      v-show="
                        ingredient &&
                        ingredient.campaignData &&
                        ingredient.campaignData.shoppableFlag &&
                        ingredient.campaignData.shoppableFlag != 'nonShoppable'
                      "
                      @click="onPaginationUnpromoted(ingredient)"
                    >
                    <paginate
                      v-if="(ingredient.totalProducts > numProductMatches) && ingredient.totalProducts > 0 && !ingredient.loading && !ingredient.showUnpromotedLoader"
                      v-model="ingredient.currentPageUnPromoted"
                      :total-rows="ingredient.totalProducts"
                      :page-range="pageRange"
                      :per-page="numProductMatches"
                      :page-count=Math.ceil(ingredient.totalProducts/numProductMatches)
                      prev-text="<"
                      next-text=">"
                      :prev-class="'prev-unpromoted'"
                      :next-class="'next-unpromoted'"
                      :click-handler="pageChangeUnPromotedAsync"
                      :container-class="'pagination-unpromoted'"
                      :page-class="'page-item-unpromoted'"
                      :page-link-class="'page-link-unpromoted'"
                      :disabled-class="'disabled-pagination'"
                      :active-class="'active-unpromoted'"
                      :margin-pages="marginPages"
                    >
                    </paginate>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <hr
            v-show="
              !ingredient.showView && ingredientGroup.displayGroup
            "
            class="bottomborder"
          />
        </div>
      </div>
    </div>
    <Modal v-if="isErrorOccuredModalVisible" @close="closeModal">
  <template #nutrition>
    <div class="error-save-recipe-modal">
      <div class="publish-content">
        <div class="publish-head">
          <div>
            <span class="unable-to-save">Unable to Save Recipe</span><br>
            <span class="unable-to-save-title">
              The following field(s) cannot be empty:
            </span>
            <br />
            <ul class="error-list">
              <li class="unable-to-save-list">
                Ingredient's Food Item
              </li>
            </ul>
          </div>
          <div class="button-container">
            <button type="button" class="btn-green" @click="closeModal()">Okay</button>
          </div>
        </div>
      </div>
    </div>
  </template>
</Modal>

    <Modal v-show="isSaveModalVisible" @close="closeModal">
      <template #nutrition>
        <div class="shoppable-publish-modal shoppable-save-modal">
          <div class="nutrition-info-popup-container">
            <div class="nutrition-image">
              <div class="nutrition-image-container">
                <img alt=""
                  src="~/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png"
                />
              </div>
            </div>
          </div>
          <div class="shoppable-publish-content">
            <div class="shoppabl-head-content">
              <div class="shoppable-publish-head">What do you want to do?</div>
              <img alt=""
                class="shoppable-exit-btn"
                @click="closeModal()"
                src="@/assets/images/exit-gray.png"
              />
            </div>
            <div class="shoppable-publish-subtitle text-light-h3">
              Reminder: saving your changes will overwrite all default settings.
            </div>
            <div class="shoppable-button-container">
              <button type="button" class="btn-green-outline" @click="closeModal()">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
              <button type="button"
                class="btn-green"
                @click="checkIngredientName()"
              >
              {{ $t('BUTTONS.SAVE_BUTTON') }}
            </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <cancelModal
    v-if="isCancelModalVisible"
    :availableLang="[]"
    :isCampaignModifiedFromShoppableReview="false"
    :callConfirm="backToMaster"
    :closeModal="closeModal"
    />
    <Modal v-show="isProductConfirmationDeleted" @close="closeModal">
      <template #deleteShoppableReviewProduct>
        <div class="delete-shoppable-review-modal">
          <div class="delete-shoppable-review-image">
            <img alt="" src="~/assets/images/delete.png" />
          </div>
          <div class="delete-shoppable-review-content">
            <div class="delete-shoppable-review-title">Remove Product?</div>
            <div class="delete-shoppable-review-description text-title-2 font-normal">
              {{ $t('REMOVE_INGREDIENT_TEXT') }}
            </div>
            <div class="delete-shoppable-review-button-container">
              <button type="button"
                class="btn-green-outline"
                @click="closeModal"
              >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button"
                class="btn-red"
                @click="removeProductConfirmedAsync()"
              >
                {{  $t('BUTTONS.REMOVE_BUTTON')  }}
            </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="isReportModalVisible" @close="closeModal" id="problemModal">
      <template #problemModal>
        <div class="problem-modal">
          <div class="problem-modal-content">
            <div class="problem-modal-warning">Please select a problem</div>
            <div class="problem-modal-checkbox">
              <template v-for="issueType in reportIssueTypes">
                <input
                  type="radio"
                  :id="issueType.key"
                  class="issue-type"
                  :value="issueType.key"
                  v-model="selectedIssueType"
                />
                <label
                  style="
                    font-size: 16px;
                    font-weight: 400;
                    width: 182px;
                    height: 42px;
                    font-family: $font-family-averta;
                  "
                  :for="issueType.key"
                  >&nbsp;{{ issueType.display }}</label
                >
                <br />
              </template>
            </div>
            <div class="problem-modal-input">
              <textarea
                type="text"
                class="problem-input text-light-h3"
                placeholder="Comments/notes (optional)"
                v-model="issueDescription"
              >
              </textarea>
            </div>
            <div class="problem-modal-btn-container">
              <button type="button"
                class="btn-green-outline"
                @click="closeModal()"
              >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
              <button type="button"
                class="btn-green"
                @click="reportProductAsync()"
              >
                Send
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-if="showAddQuantity" @close="closeModal">
      <template #nutrition>
        <div class="add-ingredient-weights-popup">
          <div class="add-ingredient-headin-text-and-cross-logo">
            <div class="add-ingredient-heading-text">
              Add the total size ingredient
            </div>
            <img
              class="add-ingredient-cross-image"
              @click="closeModal"
              src="~/assets/images/exit-gray.png"
              alt="cross icon"
            />
          </div>
          <div class="sub-text-container">
            <p class="sub-text text-title-4">
              {{ tempingdata.quantity }} {{ tempingdata.uom }}
              {{ tempingdata.notes }}
              <span class="sub-text-bold-text">{{ tempingdata.name }}</span>
            </p>
          </div>
          <div class="input-container">
            <div class="input-box-container text-h3 font-normal">
              <input
                autocomplete="off"
                class="input-box"
                v-model.trim="inputIngredientWeight"
                placeholder="Qty (whole number only)"
                maxlength="4"
                @keypress="restrictNumericInput($event), restrictSpecialCharacters($event)"
                name="text-box"
              />
            </div>
            <div
              v-for="(info, index) in searchListData"
              :key="index"
              :class="
                info.isChecked
                  ? 'add-ingredients-background radio-button-container-one'
                  : 'radio-button-container-one'
              "
            >
              <div class="round" @click="selectedRecipeStatuses(info)">
                <input v-if="info.isChecked" id="selected-recipe" type="radio" />
                <label for="selected-recipe" aria-label="selectedRecipe"></label>
              </div>
              <div class="radio-btn-text-container">
                <div class="radio-btn-text text-title-2 font-normal" @click="selectedRecipeStatuses(info)">
                  {{ info.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="btn-div">
            <button type="button" class="btn-green-outline" @click="closeModal">Cancel</button>
            <button type="button"
              :class="
                inputIngredientWeight !== '' &&
                disableAddButton(inputIngredientWeight) &&
                Number(inputIngredientWeight) != 0
                  ? 'btn-green'
                  : 'btn-green disabled-button'
              "
              @click="setIngredientWeightAsync()"
            >
              Add
            </button>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-show="isIngredientKeywordsPopupModal" @close="closeModal">
      <template #nutrition>
        <div class="main-ingredient-keywords-popup-modal">
          <div class="main-edit-keyword-section">
            <div class="edit-keyword-section">Add keywords* to ingredient</div>
            <div class="close-image-icon">
              <img alt=""
                @click="closeModal"
                src="~/assets/images/exit-gray.png"
              />
            </div>
          </div>
          <div class="ingredients-quantity-section text-title-2 font-normal">
            {{tempIngredientDataQuantity }} {{ tempIngredientDataUom }},
            {{ tempIngredientDataNotes }}
            <span class="ingredients-text-section text-title-2">{{ tempIngredientDataName }}</span>
          </div>
          <div class="input-container">
            <div
              :class="
                keywordData.length > 0
                  ? 'ingredient-keyword-after-edit text-light-h3'
                  : 'ingredient-keyword-container text-light-h3'
              "
              @click="focusKeywordInput($event)"
            >
              <img alt=""
                :class="
                  keywordData.length > 0 ? 'cross-icon-edit' : 'cross-icon-off'
                "
                src="~/assets/images/exit-gray.png"
                @click="removeAllKeywords()"
              />
              <div class="selected-ingredient-keyword-main">
                <div
                  v-for="(data, index) in keywordData"
                  class="selected-ingredient-keyword-popup text-h4 font-light"
                  :key="index"
                >
                  <div class="ingredient-data-keyword">
                    <div
                      :id="`ingredientePopProductName${index}`"
                      @mouseover="checkIngredientNamePopup(index)"
                      @mouseleave="hideIngredientNamePopupTooltip(index)"
                      class="ingredient-data-keyword-section"
                      :class="{
                        'simple-data-tooltip': isPopupProductNameVisible
                      }"
                      :data-tooltip-text="isPopupProductNameVisible && data"
                    >
                      <span class="data-truncated"> {{ data }}</span>
                    </div>
                    <img alt=""
                      @click="removeKeyWord(index)"
                      class="remove-ingredient-keyword-image"
                      src="@/assets/images/close.svg?skipsvgo=true"
                    />
                  </div>
                </div>
                <input
                  type="text"
                  :class="
                    keywordData.length > 0 ? 'keywords' : 'keywords-full-version text-h3'
                  "
                  v-model="tempKeyword"
                  @blur="inputDataKeyword(tempKeyword)"
                  @keyup.enter="inputDataKeyword(tempKeyword)"
                  id="keywordsName"
                  autocomplete="off"
                  tabindex="0"
                  dir=""
                  autocapitalize="off"
                  :placeholder="
                    keywordData.length == 0
                      ? 'List keywords separated by a comma (optional)'
                      : 'Enter keyword(s)'
                  "
                />
              </div>
            </div>
          </div>
          <div class="keyword-article">
            *Keywords set at the recipe level will override the global keywords
            (when applicable).
          </div>
          <div class="global-keywords">Global keywords:</div>
          <div
            v-if="recievedKeywords.length > 0"
            class="main-section-breadcrumb-global-keywords"
          >
            <div
              v-for="(data, index) in recievedKeywords"
              :key="index"
              class="breadcrumb-global-keywords"
            >
              <span
                :id="`ingredientePopupProductName${index}`"
                @mouseover="checkIngredientPopupName(index)"
                @mouseleave="hideIngredientNamePopupTip(index)"
                class="display-keyword"
                :class="{
                  'simple-data-tooltip': isIngredientNameTooltipVisible
                }"
                :data-tooltip-text="isIngredientNameTooltipVisible && data"
                >
                  {{ data }}
              </span>
            </div>
          </div>
          <div
            v-if="!recievedKeywords.length"
            class="global-keywords-empty"
          >
            {{ $t('NONE') }}
          </div>
          <div class="button-div-container">
          <div class="btn-div">
            <button type="button"
             class="btn-green-outline"
             @click="closeModal"
            >
             {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
          <div class="btn-div">
            <button type="button"
              :class="!isKeywordModified ? 'btn-green disabled-button' : 'btn-green'"
              @click="addKeywordBackword()"
            >
              {{ buttonString }}
            </button>
          </div>
        </div>
      </div>
      </template>
    </Modal>
    <Modal v-show="isPreviewReferenceProductDataVisible" @close="closeModal">
      <template #previewReferenceIngredient>
        <div class="shoppable-preview-reference-ingredient-container">
          <div class="shoppable-preview-reference-ingredient-top-section">
            <div class="reference-product-section-name text-h2">Reference product</div>
            <div class="reference-product-section-close-button" @click="closeModal()">
              <img class="close-icon" alt="" src="@/assets/images/exit-gray.png" />
            </div>
          </div>
          <div class="shoppable-preview-reference-ingredient-middle-section">
            <div class="shoppable-preview-reference-ingredient-name text-title-2 font-normal">
              Ingredient name «{{currentReferenceIngredientName ? currentReferenceIngredientName : ""}}»
            </div>
          </div>
          <div class="shoppable-preview-reference-ingredient-bottom-section">
            <div class="reference-preview-ingredient-table-data">
              <table class="reference-preview-table" aria-label="reference-preview-table">
                <thead class="reference-ingredient-table-head">
                  <tr class="reference-ingredient-table-head-row">
                    <th class="ingredient-image-heading"></th>
                    <th class="ingredient-product-id-heading">Product Id</th>
                    <th class="ingredient-product-title-heading">Product Title</th>
                    <th class="ingredient-product-weight-heading">WEIGHT</th>
                    <th class="ingredient-product-store-count-heading">{{ $t('STORE_COUNT') }}</th>
                  </tr>
                </thead>
                <tbody class="reference-ingredient-table-body">
                  <tr class="reference-ingredient-table-body-row">
                    <td class="ingredient-image">
                      <img :src="currentReferenceProductImage ? currentReferenceProductImage : defaultImage " alt="" srcset="">
                    </td>
                    <td class="ingredient-product-id text-light-h4">{{ currentReferenceProductId ? currentReferenceProductId : "" }}</td>
                    <td class="ingredient-product-title text-h3"> <div class="product-title">{{ currentReferenceProductName ? currentReferenceProductName : "" }} </div></td>
                    <td class="ingredient-product-weight text-light-h3">{{ currentReferenceProductQuantity ? currentReferenceProductQuantity : "" }}</td>
                    <td class="ingredient-product-store-count text-light-h3">{{ currentReferenceStoreCount ? currentReferenceStoreCount : 0 }}</td>
                  </tr>
                </tbody>
              </table>
              <div class="reference-tags-main-container">
                <div v-show="currentRefrenceTags.length>0" class="reference-ingredient-tags-heading text-title-2">
                  {{ $t('TAG.TAG_TEXT')  }}:
                </div>
                <div class="reference-tags-list-container">
                  <div v-for="(tags, index) in currentRefrenceTags" :key="index" class="ingredient-reference-tags-card">
                    <div v-if="false" class="ingredient-reference-tags-image">
                    </div>
                    <div class="ingredient-reference-tags-name text-title-2 font-normal">
                      {{ tags && tags.display ? tags.display : "" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="shoppable-preview-reference-ingredient-cancel-button">
              <button type="button" class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
    <Modal v-if="isProductTagOpen" @close="closeModal">
      <template #previewReferenceIngredient>
        <div class="shoppable-ingredient-product-tag-container">
          <div class="shoppable-ingredient-reference-top-section">
            <div class="product-tag-section-heading">Product details</div>
            <div class="product-tag-section-close-button" @click="closeModal()">
              <img class="close-icon" alt="" src="@/assets/images/exit-gray.png" />
            </div>
          </div>
          <div class="product-reference-ingredient-middle-section">
            <div  v-if="currentProductTagStoreIngredientName" class="product-reference-ingredient-name">
              Ingredient name «{{currentProductTagStoreIngredientName}}»
            </div>
          </div>
          <div class="product-reference-ingredient-bottom-section">
            <div id="product-reference-ingredient-table" class="product-reference-ingredient-table-data">
              <table class="reference-product-table" aria-label="reference-product-table">
                <thead class="reference-product-ingredient-table-head">
                  <tr class="reference-product-ingredient-table-head-row">
                    <th class="reference-ingredient-product-image-heading"></th>
                    <th class="reference-ingredient-product-id-heading">Product Id</th>
                    <th class="reference-ingredient-product-title-heading">Product Title</th>
                    <th class="reference-ingredient-product-weight-heading">WEIGHT</th>
                    <th class="reference-ingredient-product-quantity-heading">{{ $t('QUANTITY') }}</th>
                    <th class="reference-ingredient-product-store-count-heading">{{ $t('STORE_COUNT') }}</th>
                  </tr>
                </thead>
                <tbody v-if="currentProductTagData" class="reference-product-ingredient-table-body">
                  <tr class="reference-product-ingredient-table-body-row">
                    <td class="ingredient-product-image">
                      <img :src="currentProductTagData.image && currentProductTagData.image.url ? currentProductTagData.image.url : defaultImage " alt="" srcset="">
                      <div v-if="isPromotedProductTagIconVisible" class="ingredient-promoted-recipe-logo">
                        <span class="promoted-recipe-text">Promoted</span>
                      </div>
                    </td>
                    <td class="ingredient-product-id text-light-h4">{{currentProductTagData.externalId ? currentProductTagData.externalId : ""}}</td>
                    <td class="ingredient-product-title text-h3">
                      <div class="product-title">{{ setIngredientName(currentProductTagData && currentProductTagData.brand ? currentProductTagData.brand : '', currentProductTagData && currentProductTagData.name ? currentProductTagData.name : '').name }} </div>
                    </td>
                    <td class="ingredient-product-weight text-light-h3">
                      <div v-if="currentProductTagData.size && currentProductTagData.size.value && currentProductTagData.size.unit && currentProductTagData.size.unit.abbreviation" class="product-weight">{{currentProductTagData.size.value + " " + currentProductTagData.size.unit.abbreviation }}</div>
                      <div v-else-if="currentProductTagData.size && currentProductTagData.size.value" class="product-weight">{{currentProductTagData.size.value }}</div>
                      <div v-else class="product-weight"></div>
                    </td>
                    <td class="ingredient-product-quantity text-light-h3">{{ currentProductTagData.shoppable && currentProductTagData.shoppable.quantity ? currentProductTagData.shoppable.quantity : "" }}</td>
                    <td class="ingredient-product-store-count text-light-h3">{{ currentProductTagData && currentProductTagData.numberOfStores ? currentProductTagData.numberOfStores:'0' }}</td>
                  </tr>
                </tbody>
              </table>
              <div class="shoppable-tag-and-nutrition-main-container">
                <div class="shoppable-ingredient-reference-tags-main-container">
                  <div v-show="currentProductTagData?.labels?.desirable?.length"  class="shoppable-ingredient-tags-heading text-title-2">
                    {{ $t('TAG.TAG_TEXT')  }}:
                  </div>
                  <div v-if="currentProductTagData?.labels?.desirable" class="shoppable-ingredient-reference-tags-list-container">
                    <div v-for="(tag, index) in currentProductTagData.labels.desirable" :key="index" class="shoppable-ingredient-reference-tags-card">
                      <div v-if="false" class="shoppable-ingredient-reference-tags-image">
                      </div>
                      <div v-if="tag && tag.display" class="shoppable-ingredient-reference-tags-name text-title-2 font-normal">
                        {{ tag.display ? tag.display : "" }}
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="nutritionListTableColumnOne.length>0 || nutritionListTableColumnTwo.length>0" class="shoppable-ingredient-product-details-nutrition-container">
                  <div class="nutrition-section-heading text-title-2">Nutrition {{ showNutritionServing }}:</div>
                  <div class="nutrition-table-container">
                  <div class="nutrition-table-section">
                  <div class="nutrition-section-area">
                  <div v-for="(item, index) in nutritionListTableColumnOne" :key="index" class="nutrition-content-row">
                  <div :class="item.subNutrient ? 'nutrition-name-heading nutrition-sub-name-heading' : 'nutrition-name-heading'"
                  :id="item.bold ? 'bold-text' : ''"
                  >{{item.name.display}}</div>
                  <div class="nutrition-unit-heading">{{item && item.amount && item.amount.value?item.amount.value:'0'}} {{item && item.amount && item.amount.unit && item.amount.unit.abbreviation ? item.amount.unit.abbreviation :''}}</div>
                  <div class="nutrition-precentage-unit-heading">
                  <span v-if="item && item.dvp &&(item.dvp.value==0||item.dvp.value)">
                  {{item && item.dvp &&item.dvp.value && (item.dvp.value==0||item.dvp.value)?item.dvp.value:0}}%
                  </span>
                  </div>
                  <hr :class="!item.subNutrient ?'line-break':'line-break-sub'"/>
                  </div>
                  </div>
                  </div>
                  <div class="nutrition-table-section">
                  <div class="nutrition-section-area">
                  <div v-for="(item, index) in nutritionListTableColumnTwo" :key="index" class="nutrition-content-row">
                  <div :class="item.subNutrient ? 'nutrition-name-heading nutrition-sub-name-heading' : 'nutrition-name-heading'"
                  :id="item.bold ? 'bold-text' : ''"
                  >{{ item.name.display}}</div>
                  <div class="nutrition-unit-heading">{{item && item.amount && item.amount.value? item.amount.value:'0'}} {{item && item.amount && item.amount.unit && item.amount.unit.abbreviation ? item.amount.unit.abbreviation :''}}</div>
                  <div class="nutrition-precentage-unit-heading">
                    <span v-if="item && item.dvp &&(item.dvp.value==0||item.dvp.value)">
                    {{item && item.dvp &&item.dvp.value && (item.dvp.value==0||item.dvp.value)?item.dvp.value:0}}%
                    </span>
                  </div>
                  <hr :class="!item.subNutrient ?'line-break':'line-break-sub'"/>
                  </div>
                  </div>
                  <div class="nutrition-notes-section">
                  <span class="nutrition-notes-text text-light-h4">* Percent Daily Values based on a 2,000 calorie diet. Your daily value may be higher or lower depending on your calorie needs. </span>
                  </div>
                  </div>
                  </div>
                  </div>
              </div>
            </div>
            <div class="shoppable-product-reference-ingredient-cancel-button">
              <button type="button" class="btn-green-outline" @click="closeModal">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from "vue";
import cancelModal from "@/components/cancel-modal";
import Modal from "@/components/Modal";
import { debounce } from "lodash";

// utility
import { useRouter } from 'vue-router';
import { useNuxtApp } from '#app';
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";

// composables
import { useRefUtils } from '@/composables/useRefUtils';
import { useCommonUtils } from "@/composables/useCommonUtils";
import { useDelayTimer } from "@/composables/useDelayTimer";
import { useEventUtils } from "@/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";


const props = defineProps({
  ingredientsData: Object,
  recipeName: String,
  recipeSubtitle: String,
  recipeID: String,
  recipeImage: String,
  recipeProvider: String,
  imageUrlLinkUpdate: String,
  hostNameImageUrlLinkUpdate: String,
  isAdminCheck: Boolean,
});

// utiltiy declare
const store = useStore();
const { getRef } = useRefUtils();
const { delay } = useDelayTimer();
const {  triggerLoading, isEmptyOrWhitespace } = useCommonUtils();
const { preventEnterAndSpaceKeyPress, preventSpecialCharacters, restrictNumericInput, restrictToAlphabets, restrictSpecialCharacters } = useEventUtils()
const { t } = useI18n();
const router = useRouter();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus } = useNuxtApp();
const { readyProject } = useProjectLang();

const project = ref({});
const currentReferenceStoreCount = ref(0);
const currentRefrenceTags = ref("");
const matchedIngredientData = ref({});
const isIngredientPromoted = ref(true);
const buttonString = ref("");
const ADD = t("COMMON.ADD");
const OVERRIDE = $keys.KEY_NAMES.OVERRIDE;
const EDIT = t("BUTTONS.EDIT_BUTTON");
const isSaveClicked = ref(false);

const shoppableIngredientsData = ref({
  children: [],
});

const numProductMatches = ref(5);
const isAdmin = ref(false);
const isErrorOccuredModalVisible = ref(false);
const isCampaignModified = ref(false);
const isFilterBrandLoading = ref(false);
const scrollTimeout = ref(null);
const ingredientUomAutocompleteArrowCounter = ref(0);
const isReportModalVisible = ref(false);
const isFilterApplyButtonEnabled = ref(false);
const isProductConfirmationDeleted = ref(false);
const isSaveModalVisible = ref(false);
const isCancelModalVisible = ref(false);
const hasLastUOMClicked = ref(false);
const ingredientsUomList = ref([]);
const tempKeyword = ref("");
const tempGroupIndex = ref("");
const tempIndex = ref("");
const tempIngredientDataName = ref("");
const tempIngredientDataUom = ref("");
const tempIngredientDataQuantity = ref("");
const tempIngredientDataNotes = ref("");
const keywordData = ref([]);
const isIngredientKeywordsPopupModal = ref(false);
const tempingdata = ref({});
const recipeID = ref(null);
const showAddQuantity = ref(false);
const inputIngredientWeight = ref("");
const searchedUomList = ref([]);
const isNoteTooltipVisible = ref(false);
const searchListData = ref([
  { name: "Gram", isChecked: true },
  { name: "Milliliter", isChecked: false },
]);

const styleForTooltip = reactive({
  visibility: "hidden",
});

const shoppableListData = ref([
  { key: "shoppable", data: "Shoppable", subName: "(not pantry)" },
  { key: "shoppablePantry", data: "Shoppable", subName: "(pantry)" },
  { key: "nonShoppable", data: "Not Shoppable" },
]);

const currentProduct = ref(null);
const currentIngredient = ref(null);

const reportIssueTypes = ref([
  { key: "incorrectQuantity", display: "Wrong quantity" },
  { key: "other", display: "Other" },
]);

const selectedIssueType = ref("incorrectQuantity");
const issueDescription = ref("");
const recipePreviewImageUrlLinkUpdate = ref("");
const defaultImage = ref("");
const recievedKeywords = ref([]);
const isKeywordModified = ref(false);
const holdIngredient = ref([]);
const pageRange = ref(4);
const marginPages = ref(0);
const currentIngredientPromoted = ref({});
const currentIngredientUnPromoted = ref({});
const isProductIdCopiedToClipboard = ref(false);
const isPreviewReferenceProductDataVisible = ref(false);
const referenceIngredientProductData = ref([]);
const getIngredientReferenceProductListCode = ref([]);
const currentReferenceIngredientName = ref("");
const currentReferenceProductName = ref("");
const currentReferenceProductId = ref("");
const currentReferenceProductImage = ref("");
const currentReferenceProductQuantity = ref("");
const isProductTagOpen = ref(false);
const isPromotedProductTagIconVisible = ref(false);
const tagText = ref("");
const brandSearchQuery = ref("");
const isFilterDropdownVisible = ref(false);
const isErrorChecked = ref(false);
const currentProductTagStoreIngredientName = ref("");
const currentProductTagData = ref({});
const shoppableNutritionData = ref([]);
const nutritionListTableColumnOne = ref([]);
const nutritionListTableColumnTwo = ref([]);
const tagNutrition = ref([]);
const showNutritionServing = ref("");
const lang = ref("");
const displayInfoIcon = ref(false);
const productSortOptionsData = ref([]);
const isProductSortEnabled = ref(false);
const isAllIngredientVisible = ref(true);
const searchedUomText = ref("");
const statusesCSV = ref("");
const currentReferenceTags = ref();
const shoppableIngredientOverwritten = 'The shoppable experience for this ingredient has been overwritten for this recipe only.';
const copyProductId = 'Copy Product ID to the clipboard';
const isShoppableIngredientNameVisible = ref(false);
const isIngredientProductNameVisible = ref(false);
const isIngProductNameVisible = ref(false);
const isPromoteTooltipVisible = ref(false);
const clickToReportTooltip = 'Click to report a quantity issue.';
const isPopupProductNameVisible = ref(false);
const isIngredientNameTooltipVisible = ref(false);
const zeroMatchesFilterTooltip = 'Selected filter(s) have 0 results. Remove the filter(s) to view all product matches.';
const promoteProductTooltip = 'To promote products, click on “Promote” in Product Matches.';
onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    project.value = store.getters["userData/getProject"];
    if (!project.value?.id) {
      router.push({ path: "/create-project" });
      return;
    }

    lang.value = store.getters["userData/getDefaultLang"];
    setRecipePageSize();

    isAdmin.value = props.isAdminCheck;
    recipePreviewImageUrlLinkUpdate.value = props.imageUrlLinkUpdate;

    props.ingredientsData.children.forEach((groupData) => {
      const ingredients = groupData.children.map((ingredientsData) => ({
        ...getBasicIngredientData(ingredientsData),
        ...getCampaignData(ingredientsData),
        ...getShoppableData(),
        ...getFilterData(),
        size: {},
      }));

      const group = {
        displayGroup: groupData.displayGroup || "",
        setGroupName: groupData.setGroupName || false,
        name: groupData.name || "",
        children: ingredients,
      };

      shoppableIngredientsData.value.children.push(group);
    });

    // Collect product IDs for reference
    if (shoppableIngredientsData.value.children.length) {
      shoppableIngredientsData.value.children.forEach((ingredient) => {
        ingredient.children.forEach((data) => {
          if (data && data.productId) {
            getIngredientReferenceProductListCode.value.push(data.productId);
          }
        });
      });

      await getIngredientMultipleReferenceProductAsync(
        getIngredientReferenceProductListCode.value
      );
    }

    // Event listeners
    document.addEventListener("click", handleClickOutside);
    document.addEventListener("keyup", handleESCClickOutside);

    // Additional async calls
    await getRecipeUnitConfigAsync();
    await getShoppableReviewAsync();
    await getShoppableReviewConfigDataResultAsync();
    await setLabelsAsync();

    // Reset promoted message state
    shoppableIngredientsData.value.children.forEach((ingredient) => {
      ingredient.children.forEach((data) => {
        data.showPromotedMessage = false;
      });
    });
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keyup", handleESCClickOutside);
});

const getDefaultIngredientData = () => ({
  id: "",
  name: "",
  globalKeyword: [],
  volumeInMl: "",
  weightInGrams: "",
  quantity: 0,
  UOM: "",
  externalId: "",
  rawText: "",
  productId: "",
  foodItem: "",
  group: "",
  excludeFromNutrition: false,
  level: "main",
  modifier: "",
  note: "",
  remark: "",
  keywords: [],
  keywordInput: "",
});

// Function to map ingredient data
const mapIngredientData = (ingredientsData) => {
  if (!ingredientsData) return {};

  return {
    id: ingredientsData.id,
    name: ingredientsData.name,
    globalKeyword: ingredientsData.globalKeyword,
    volumeInMl: ingredientsData.volumeInMl,
    weightInGrams: ingredientsData.weightInGrams,
    quantity: ingredientsData.quantity,
    UOM: ingredientsData.UOM,
    externalId: ingredientsData.externalId,
    rawText: ingredientsData.rawText,
    productId: ingredientsData.productId,
    foodItem: ingredientsData.foodItem,
    group: ingredientsData.group,
    excludeFromNutrition: ingredientsData.excludeFromNutrition,
    level: ingredientsData.level,
    modifier: ingredientsData.modifier,
    note: ingredientsData.note,
    remark: ingredientsData.remark,
    keywords: ingredientsData.keywords,
    keywordInput: ingredientsData.keywordInput,
  };
};

const getBasicIngredientData = (ingredientsData) => {
  return {
    ...getDefaultIngredientData(),
    ...mapIngredientData(ingredientsData),
  };
};


const getCampaignData = (ingredientsData) => ({
  campaignData: ingredientsData?.campaignData
    ? copyCampaignData(ingredientsData.campaignData)
    : getDefaultCampaignData(),
  originalCampaignData: ingredientsData?.originalCampaignData
    ? copyCampaignData(ingredientsData.originalCampaignData)
    : getDefaultCampaignData(),
  isCampaignDataPassedIn: !!ingredientsData?.campaignData,
});

const getShoppableData = () => ({
  uomAutocomplete: false,
  isShoppableDropDown: false,
  shoppableSearchQuery: "",
  isSearchOn: false,
  showTableDetails: false,
  sortByDropdownOpen: false,
  sortByDataName: "",
  sortByDataKey: "",
  showView: true,
  copyReferenceProductID: false,
  loading: true,
  products: [],
  totalProducts: 0,
  promotedResult: [],
  promotedTotalProducts: 0,
  currentPagePromoted: 1,
  currentPageUnPromoted: 1,
  showUnpromotedLoader: false,
  showPromotedLoader: false,
  setPromotedCountingList: [],
  currentIndexOfPromotedIng: null,
  fromPromotedProductMatches: 0,
  fromProductMatches: 0,
});

// Function to get filter data
const getFilterData = () => ({
  isFilterBrandPopupVisible: false,
  filterList: [],
  displayBrand: "",
  checkedFilters: [],
  displaycount: 0,
  brandSearchQuery: "",
  filteredFilterList: [],
  selectedItem: "",
  isAllSelected: true,
  displayCrossIcon: false,
  displayAllOption: true,
  selectedItems: [],
  hasLabelsKeys: [],
});

// Function to check promoted label asynchronously
const label = () => {
  checkPromotedLabelAsync(); // Assuming this function is defined elsewhere
};

const checkPromotedLabelAsync = async () => {
  for (const item of shoppableIngredientsData.value.children) {
    for (const ingredient of item.children) {
      if (!ingredient.promotedResult?.length) continue;

      ingredient.showPromotedLoader = true;

      for (const product of ingredient.promotedResult) {
        const desirableLabels = product?.labels?.desirable?.map(label => label.key) ?? [];
        const selectedFilters = ingredient.selectedItems?.map(filter => filter.key) ?? [];

        product.isDisabled = selectedFilters.length
          ? !selectedFilters.every(value => desirableLabels.includes(value)) || !desirableLabels.length
          : false;
      }

      ingredient.showPromotedLoader = false;
    }
  }
};

const setLabelsAsync = async () => {
  shoppableIngredientsData?.value?.children.forEach((item) => {
    item.children.forEach((ingredient) => {
      if (ingredient?.campaignData?.hasLabels?.length) {
        ingredient.campaignData.hasLabels.forEach((label) => {
          ingredient.selectedItems.push(label);
        });
      }
    });
  });

  shoppableIngredientsData.value?.children.forEach((category) => {
    processCategoryIngredients(category);
  });

  shoppableIngredientsData.value?.children.forEach((item) => {
    item.children.forEach((ingredient) => {
      if (ingredient?.campaignData?.hasLabels?.length) {
        ingredient.isAllSelected = false;
        applyFilterAsync(ingredient);
      }
    });
  });
};

const processCategoryIngredients = (category) => {
  if (category?.children?.length) {
    category.children.forEach((ingredient) => {
      if (isCampaignDataValid(ingredient)) {
        updateIngredientFilters(ingredient);
      }
    });
  }
};

const isCampaignDataValid = (ingredient) => {
  return ingredient?.campaignData?.hasLabels?.length > 0;
};

const updateIngredientFilters = (ingredient) => {
  ingredient.isAllSelected = false;
  ingredient?.campaignData?.hasLabels?.forEach((label) => {
    setFilterChecked(ingredient, label);
  });
};

const setFilterChecked = (ingredient, label) => {
  ingredient?.filterList?.forEach((filter) => {
    if (label === filter?.key) {
      filter.isChecked = true;
    }
  });
};

const viewIngredientDropDown = () => {
  let data = [];
  if (isAllIngredientVisible.value) {
    shoppableIngredientsData.value.children.forEach((group) => {
      group.children.forEach((item) => {
        if (item) {
          item.showView = false;
        }
      });
    });
    isAllIngredientVisible.value = false;
  } else {
    shoppableIngredientsData.value.children.forEach((group) => {
      group.children.forEach((item) => {
        if (item) {
          item.showView = true;
        }
      });
    });
    isAllIngredientVisible.value = true;
  }

  data = shoppableIngredientsData.value.children;
  shoppableIngredientsData.value.children = [];
  shoppableIngredientsData.value.children = data;
};

const closeViewDetails = (ingredient) => {
  let counter = 0;
  let data = [];
  let flag = 0;

  ingredient.showView = !ingredient.showView;

  shoppableIngredientsData.value.children.forEach((group) => {
    group.children.forEach((item) => {
      if (item) {
        counter++;
      }
      if (item && item.showView) {
        flag++;
      }
    });
  });

  if (flag === counter) {
    isAllIngredientVisible.value = true;
  } else {
    isAllIngredientVisible.value = false;
  }
  data = shoppableIngredientsData.value.children;
  shoppableIngredientsData.value.children = [];
  shoppableIngredientsData.value.children = data;
};

const hidePromotedProducts = (index) => {
  const element = getRef("promoteTip" + index);
  if (element) {
    isPromoteTooltipVisible.value = false;
  }
};

const checkPromotedProducts = (ingredient, index) => {
  const { promotedProducts, onlyPromoted } = ingredient.campaignData;
  const element = getRef("promoteTip" + index);

  if (!promotedProducts.length && !onlyPromoted && element) {
    isPromoteTooltipVisible.value = true;
  }
};

const backToMaster = () => {
  cancelConfirmationPopup();
  closeModal();
};

const getShoppableReviewConfigDataResultAsync = async () => {
  const params = { lang: lang.value };

  try {
    await store.dispatch("shoppableReview/getShoppableReviewConfigDataAsync", {
      params,
    });

    const response = store.getters["shoppableReview/getShoppableReviewConfig"];
    if (Object.keys(response).length) {
      displayInfoIcon.value = response.productInfoEnabled || false;
      isProductSortEnabled.value = response.productSortEnabled || false;
      productSortOptionsData.value = response.productSortOptions || [];
      shoppableNutritionData.value = response.productNutrients || [];
      isFilterDropdownVisible.value =
        response.productLabelFilterEnabled || false;
    }

    if (response.productLabelFilterOptions) {
      const sortedFilterList = sortFilterOptions(
        response.productLabelFilterOptions
      );
      ingredientsFilterList(sortedFilterList);
    }
  } catch (error) {
    console.error(`Error in getShoppableReviewConfigDataResultAsync:`, error);
  }
};
const sortFilterOptions = (filterOptions) => {
  return (filterOptions || []).sort((optionA, optionB) =>
    (optionA.display || "").localeCompare(optionB.display || "")
  );
};

const ingredientsFilterList = (sortedFilterList) => {
  shoppableIngredientsData.value?.children?.forEach((group) => {
    groupIngredients(group, sortedFilterList);
  });
};

const groupIngredients = (group, sortedFilterList) => {
  group?.children?.forEach((ingredient) => {
    setFilterList(ingredient, sortedFilterList);
  });
};

const setFilterList = (ingredient, sortedFilterList) => {
  ingredient.filterList = generateFilterList(sortedFilterList);
};

const generateFilterList = (filterOptions) => {
  return (filterOptions || []).map((option) => ({
    ...option,
    isChecked: false,
  }));
};

const getQuantity = (ingredient) => {
  ingredient.quantity = parseFloat(ingredient.quantity);
};

const handlePaste = (event, value, index, groupIndex) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.clipboardData;
  const pastedText = clipboardData.getData("text");
  const isNegative = pastedText.includes("-");

  if (!isNegative && !isNaN(pastedText)) {
    if (value === "shoppableQuantity") {
      const item =
        shoppableIngredientsData.value.children[groupIndex].children[index];
      item.quantity = pastedText;
    }
  }
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
  event.preventDefault();
};

const selectAllFilter = (ingredient, event) => {
  if (!ingredient.isAllSelected) {
    ingredient.isAllSelected = true;
    ingredient.filterList.forEach((item) => {
      item.isChecked = false;
    });
    isFilterApplyButtonEnabled.value = true;
  }
  event.preventDefault();
};

const checkedBrandName = (info, ingredient) => {
  info.isChecked = !info.isChecked;
  isFilterApplyButtonEnabled.value = true;

  const checkedFilters = ingredient.filterList.filter((item) => item.isChecked);
  ingredient.isAllSelected = checkedFilters.length === 0;
  ingredient.selectedItem = info;
  ingredient.checkedFilters = checkedFilters;
};

const resetSearchQuery = (ingredient) => {
  ingredient.brandSearchQuery = "";
  ingredient.filteredFilterList = [];
};

const applyFilterAsync = async (ingredient, data) => {
  ingredient.displayCrossIcon = true;
  const count = ingredient.filterList.filter((item) => item.isChecked).length;

  if (count > 1) {
    ingredient.displaycount = count;
    ingredient.displayBrand = "";
  } else {
    ingredient.displayBrand =
      ingredient.filterList.find((item) => item.isChecked)?.display || "";
  }

  if (ingredient.isAllSelected) {
    resetIngredientFilter(ingredient);
  } else {
    ingredient.displayAllOption = false;
  }

  ingredient.selectedItems = ingredient.filterList.filter((item) => item.isChecked);
  ingredient.hasLabelsKeys = ingredient.selectedItems.map((item) => item.key);

  if (ingredient.isAllSelected) {
    ingredient.hasLabelsKeys = [];
  }

  ingredient.showUnpromotedLoader = true;
  ingredient.fromProductMatches = 0;
  ingredient.currentPageUnPromoted = 1;

  const result = await getShoppableProductsAsync(ingredient);
  ingredient.showUnpromotedLoader = false;
  ingredient.promotedResult = [...result.promotedResult];
  ingredient.promotedTotalProducts = result.promotedTotalProducts || 0;
  ingredient.products = [...result.products];
  ingredient.totalProducts = result.totalProducts;

  if (ingredient.selectedItems.length > 0) {
    ingredient.campaignData.hasLabels = ingredient.selectedItems.map((item) => item.key);
  }

  await checkPromotedLabelAsync();
  if (data) {
    isCampaignModified.value = true;
  }
};

const resetIngredientFilter = (ingredient) => {
  ingredient.displaycount = 0;
  ingredient.displayAllOption = true;
  ingredient.displayCrossIcon = false;
  ingredient.filterList.forEach((item) => {
    item.isChecked = false;
  });
  ingredient.displayBrand = "";
};

const resetSearchBrandAsync = async (ingredient) => {
  ingredient.hasLabelsKeys = [];
  ingredient.isAllSelected = true;
  ingredient.displayCrossIcon = false;
  ingredient.displayAllOption = true;
  ingredient.displaycount = 0;
  ingredient.displayBrand = "";
  ingredient.brandSearchQuery = "";
  ingredient.isFilterBrandPopupVisible = false;
  ingredient.selectedItems = [];
  ingredient.showUnpromotedLoader = true;
  ingredient.fromProductMatches = 0;
  ingredient.currentPageUnPromoted = 1;

  // Reset all filter items
  ingredient.filterList.forEach((item) => {
    item.isChecked = false;
  });

  // Fetch shoppable products
  const result = await getShoppableProductsAsync(ingredient);
  ingredient.showUnpromotedLoader = false;

  if (result) {
    ingredient.promotedResult = result.promotedResult || [];
    ingredient.promotedTotalProducts = result.promotedTotalProducts || 0;
    ingredient.products = result.products || [];
    ingredient.totalProducts = result.totalProducts || 0;
  }

  // Reset campaign data if no selected items
  if (ingredient.selectedItems.length === 0) {
    ingredient.campaignData.hasLabels = [];
  }

  // Mark the campaign as modified
  isCampaignModified.value = true;
};

const searchBrand = (ingredient) => {
  if (ingredient.brandSearchQuery.length) {
    isFilterBrandLoading.value = true;
  }

  const brandSearchQuery = ingredient.brandSearchQuery.toLowerCase();
  ingredient.filteredFilterList = ingredient.filterList.filter((filterItem) =>
    filterItem.display.toLowerCase().includes(brandSearchQuery)
  );

  isFilterBrandLoading.value = false;
};

const openSortByDropdown = (ingredient) => {
  if (ingredient.sortByDataName === "") {
    ingredient.sortByDataName = "Default";
  }
  ingredient.sortByDropdownOpen = !ingredient.sortByDropdownOpen;
  setCurrentIngredient(ingredient,true);
};

const selectSortByAsync = async (ingredient, data) => {
  ingredient.sortByDataName = data.display;
  ingredient.sortByDataKey = data.key;
  ingredient.showUnpromotedLoader = true;
  await getProductMatchesAsync(ingredient);
  ingredient.currentPageUnPromoted = 1;
  pageChangeUnPromotedAsync();
  onPaginationUnpromoted(ingredient);
  await checkPromotedLabelAsync();
};

const closeAllSortByDropdown = () => {
  shoppableIngredientsData?.value?.children?.forEach((group) => {
    group.children.forEach((ingredient) => {
      ingredient.sortByDropdownOpen = false;
    });
  });
};
const setFilterBrandPopupVisible = (ingredient, event) => {
  ingredient.filteredFilterList = [];
  ingredient.brandSearchQuery = "";
  const element = getRef(`scrollTag${ingredient.id}`);
  const headerOffset = 70;
  const offsetPosition = element.offsetTop - headerOffset;

  document.documentElement.scrollTop = offsetPosition;
  document.body.scrollTop = offsetPosition;
  isFilterApplyButtonEnabled.value = false;

  const lastGroup =
    shoppableIngredientsData.value.children[
      shoppableIngredientsData.value.children.length - 1
    ];
  const lastIngredient = lastGroup.children[lastGroup.children.length - 1];

  if (ingredient.id === lastIngredient.id) {
    clearTimeout(scrollTimeout.value);
    scrollTimeout.value = setTimeout(() => {
      const divElement = getRef(`filter-brand-popup-${ingredient.id}`);
      if (divElement && !ingredient?.promotedTotalProducts && !ingredient?.totalProducts) {
        const divHeight = divElement.clientHeight;
        const demo = getRef("shoppable-main-container");
        demo.style.marginBottom = `${divHeight}px`;
        divElement.scrollIntoView({ behavior: "smooth" });
      }
    }, 0);
  }

  if (!ingredient.selectedItems) {
    ingredient.selectedItems = [];
  }

  const selectedItemsArray = ingredient.selectedItems.map((item) => ({
    ...item,
  }));
  const checkedFiltersArray = ingredient.checkedFilters.map((item) => ({
    ...item,
  }));

  ingredient.filterList.forEach((filterItem) => {
    const isSelected = selectedItemsArray.some(
      (selectedItem) => selectedItem.key === filterItem.key
    );
    filterItem.isChecked = isSelected;
  });

  ingredient.checkedFilters = checkedFiltersArray;
  ingredient.isAllSelected = selectedItemsArray.length === 0;

  ingredient.isFilterBrandPopupVisible = !ingredient.isFilterBrandPopupVisible;
  setCurrentIngredient(ingredient, true);
};

const setCurrentIngredient = (ingredient, skipClose = false) => {
  if (!skipClose) {
    closeFilterTagByDropdown();
    closeAllSortByDropdown();
  }
  currentIngredient.value = ingredient;
  closeAllShoppableDropDown();
  closeAllDropdownUOM();
};

const closeFilterTagByDropdown = () => {
  shoppableIngredientsData.value.children.forEach((group) => {
    group?.children.forEach((ingredient) => {
      ingredient.isFilterBrandPopupVisible = false;
      ingredient.filterList.forEach((item) => {});
    });
  });
};


const getIngredientReferenceProductAsync = async (productID) => {
  const params = {
    codes: productID,
    lang: lang.value,
  };
  try {
    await store.dispatch(
      "shoppableReview/getIngredientReferenceMultipleProductDataAsync",
      { params }
    );
    const response =
      store.getters["shoppableReview/getIngredientReferenceProduct"];
    if (
      response?.results.length > 0 &&
      response.results[0]?.externalId === productID
    ) {
      matchedIngredientData.value = response.results[0];
    }
  } catch (e) {
    console.error("Failed to fetch ingredient reference product:", e);
  }
};

// Method to open the product tag
const openProductTag = (ingredient, data, isPromoted) => {
  isPromotedProductTagIconVisible.value = isPromoted;
  currentProductTagData.value = { ...data };
  currentProductTagStoreIngredientName.value = ingredient?.name ?? "";

  if (data.nutrients) {
    tagNutrition.value = data.nutrients.perServing.map((item) => {
      const matchingData = shoppableNutritionData.value.find(
        (d) => d.key === item.name.key
      );
      return { ...item, ...matchingData };
    });

    nutritionListTableColumnOne.value = tagNutrition.value.filter(
      (item) => item.column === "1"
    );
    nutritionListTableColumnTwo.value = tagNutrition.value.filter(
      (item) => item.column === "2"
    );
  }

  isProductTagOpen.value = true;
};

// Async method to preview reference product
const previewReferenceProductAsync = async (ingredient) => {
  resetCurrentReferenceData();
  if (!ingredient?.referenceProductData) {
    isPreviewReferenceProductDataVisible.value = true;
    return;
  }
  const { referenceProductData } = ingredient;
  currentReferenceProductName.value = getProductName(referenceProductData);
  currentReferenceProductId.value = referenceProductData.externalId ?? "";

  if (currentReferenceProductId.value) {
    await fetchReferenceProductData(currentReferenceProductId.value); // Implement this function as needed
  }
  currentReferenceProductImage.value = referenceProductData.image?.url ?? "";
  currentReferenceProductQuantity.value = formatProductQuantity(
    referenceProductData.size
  ); // Implement this function as needed
  currentReferenceIngredientName.value = ingredient.name ?? "";
  isPreviewReferenceProductDataVisible.value = true;
};

// Method to reset current reference data
const resetCurrentReferenceData = () => {
  currentReferenceStoreCount.value = 0; // Assuming it's defined
  currentReferenceTags.value = ""; // Assuming it's defined
  currentReferenceProductName.value = "";
  currentReferenceProductId.value = "";
  currentReferenceProductImage.value = "";
  currentReferenceProductQuantity.value = "";
  currentReferenceIngredientName.value = "";
};

// Helper method to get product name
const getProductName = (referenceProductData) => {
  return setIngredientName(
    referenceProductData.brand ?? "",
    referenceProductData.name ?? ""
  ).name; // Implement this function as needed
};
const fetchReferenceProductData = async (referenceProductId) => {
  await getIngredientReferenceProductAsync(referenceProductId);
  currentReferenceStoreCount.value = matchedIngredientData.value?.numberOfStores ?? 0;
  currentReferenceTags.value = matchedIngredientData.value?.labels?.desirable ?? "";
};

// Method to format product quantity
const formatProductQuantity = (size) => {
  if (!size?.value) return "";
  return size.unit?.abbreviation
    ? `${size.value} ${size.unit.abbreviation}`
    : size.value;
};

// Method to copy reference product to clipboard
const copyReferenceProduct = (ingredient, index) => {
  const copyElement = getRef("refrenceProduct" + index);
  navigator.clipboard.writeText(copyElement.innerHTML);
  isProductIdCopiedToClipboard.value = true;
  ingredient.copyReferenceProductID = true;

  setTimeout(() => {
    isProductIdCopiedToClipboard.value = false;
  }, 1000);
  setTimeout(() => {
    ingredient.copyReferenceProductID = false;
  }, 1000);
};

// Async method to fetch multiple ingredient reference products
const getIngredientMultipleReferenceProductAsync = async (productIDList) => {
  if (!productIDList?.length) return;

  try {
    const params = {
      lang: lang.value,
      codes: productIDList.join(","),
    };
    await store.dispatch(
      "shoppableReview/getIngredientReferenceMultipleProductDataAsync",
      { params }
    );
    const response =
      store.getters["shoppableReview/getIngredientReferenceProduct"];

    if (response?.results?.length) {
      referenceIngredientProductData.value = response.results;
      updateShoppableIngredientsData();
    }
  } catch (error) {
    console.error("Error fetching ingredient reference product data:", error);
  }
};

// Method to update shoppable ingredients data
const updateShoppableIngredientsData = () => {
  if (!referenceIngredientProductData.value || !shoppableIngredientsData.value)
    return;

  shoppableIngredientsData.value.children.forEach((category) => {
    category.children.forEach((ingredient) => {
      const data = findReferenceProductData(ingredient.productId);
      if (data) {
        ingredient.referenceProductData = data;
      }
    });
  });
};

// Method to find reference product data
const findReferenceProductData = (productId) => {
  const id = productId.toString();
  return referenceIngredientProductData.value.find((data) => data?.externalId?.toString() === id);
};

// Method to change reference ID to null
const changeReferenceIdToNull = (ing) => {
  ing.productId = null;
};

// Method to show info tip
const showInfoTip = (data, index, tag) => {
  let temp = [];
  tagText.value = "";

  if (tag && tag.length > 0) {
    tag.forEach((item) => {
      temp.push(item.display);
    });
  }

  if (temp.length > 0) {
    tagText.value = temp.join(", ");
  }

  const element = getRef("product" + data.id);
  if (element && (index + 1) % numProductMatches.value !== 0) {
    setTimeout(() => element.style.visibility = "visible");
  } else if (element && (index + 1) % numProductMatches.value === 0) {
    setTimeout(() => {
      element.classList.remove("info-tooltip");
      element.classList.add("info-tooltip-right");
      element.style.visibility = "visible";
    });
  }
};

// Method to hide info tip
const hideInfoTip = (data) => {
  const element = getRef("product" + data.id);
  if (element) {
    setTimeout(() => element.style.visibility = "hidden");
  }
};

// Method to disable tips
const disableTip = () => {
  const names = document.querySelectorAll(".info-tooltip");
  names.forEach((name) => {
    name.style.visibility = "hidden";
  });
};

// Method to set recipe page size
const setRecipePageSize = () => {
  numProductMatches.value = 5;
  const width = window.innerWidth;

  if (width > 1550 && width <= 1625) {
    numProductMatches.value = 6;
  } else if (width > 1625 && width <= 1800) {
    numProductMatches.value = 7;
  } else if (width > 1800 && width <= 2100) {
    numProductMatches.value = 8;
  } else if (width > 2100 && width <= 2400) {
    numProductMatches.value = 9;
  } else if (width > 2400) {
    numProductMatches.value = 10;
  }
};

// Method to count promoted ingredients
const countingOfPromotedIngrediet = (ingredient) => {
  return (ingredient.currentPagePromoted - 1) * numProductMatches.value;
};

// Methods to handle pagination for promoted and unpromoted ingredients
const onPaginationPromoted = (ingredient) => {
  currentIngredientPromoted.value = ingredient;
};

const onPaginationUnpromoted = (ingredient) => {
  currentIngredientUnPromoted.value = ingredient;
};

// Async method for page change for promoted ingredients
const pageChangePromotedAsync = async () => {
  await delay(0);
  await updateAllIngredientsAsync();
  label(); // Implement this function as needed
};

// Async method to update all ingredients
const updateAllIngredientsAsync = async () => {
  for (const groupData of shoppableIngredientsData.value.children) {
    for (const ingredientsData of groupData.children) {
      if (ingredientsData.id === currentIngredientPromoted.value.id) {
        await updatePromotedIngredientAsync(ingredientsData); // Implement this function as needed
      }
    }
  }
};
const updatePromotedIngredientAsync = async (ingredientsData) => {
  currentIngredientPromoted.value.fromPromotedProductMatches =
    (currentIngredientPromoted.value.currentPagePromoted - 1) * numProductMatches.value;

  ingredientsData.showPromotedLoader = true;

  const result = await getShoppableProductsAsync(ingredientsData);

  ingredientsData.promotedResult = [...result.promotedResult];
  ingredientsData.promotedTotalProducts =
    result.promotedTotalProducts || 0;
  ingredientsData.products = [...result.products];
  ingredientsData.totalProducts = result.totalProducts;

  ingredientsData.showPromotedLoader = false; // Set loader off here
};

// Async function to handle page change for unpromoted ingredients
const pageChangeUnPromotedAsync = async () => {
  await delay(0);
  await updateAllUnPromotedIngredientsAsync();
  label();
};

// Function to update all unpromoted ingredients
const updateAllUnPromotedIngredientsAsync = async () => {
  for (const groupData of shoppableIngredientsData.value.children) {
    for (const ingredient of groupData.children) {
      if (ingredient.id === currentIngredientUnPromoted.value.id) {
        await updateUnPromotedIngredientAsync(ingredient);
      }
    }
  }
};

// Async function to update a specific unpromoted ingredient
const updateUnPromotedIngredientAsync = async (ingredientData) => {
  currentIngredientUnPromoted.value.fromProductMatches =
    (currentIngredientUnPromoted.value.currentPageUnPromoted - 1) * numProductMatches.value;

  ingredientData.showUnpromotedLoader = true;

  const result = await getShoppableProductsAsync(ingredientData);

  ingredientData.showUnpromotedLoader = false; // Set loader off here
  ingredientData.promotedResult = [...result.promotedResult];
  ingredientData.promotedTotalProducts = result.promotedTotalProducts || 0;
  ingredientData.products = [...result.products];
  ingredientData.totalProducts = result.totalProducts;
};

// Focus on keyword input element
const focusKeywordInput = (event) => {
  const ele = getRef("keywordsName");
  if (event.target.id === "keyword" || ele === document.activeElement) return;
  ele.focus();
};

// Search for global keyword for a specific ingredient
const searchGlobalKeywordForIngredient = (ingredient, groupIndex, index) => {
  checkForIngredientsAsync(ingredient.name, groupIndex, index);
};

// Async function to check for ingredient keywords
const checkForIngredientsAsync = async (name, groupIndex, index) => {
  try {
    const params = {
      ingredients: [name],
    };
    await store.dispatch("ingredient/getIngredientKeywordsAsync", { params });

    const response = store.getters["ingredient/getIngredientKeywords"];
    if (!response?.results?.length) return;

    const newReceivedKeywords = Object.values(response.results).flatMap(
      (item) => item?.keywords || []
    );

    shoppableIngredientsData.value.children.forEach((item, realIndex) => {
      if (realIndex === groupIndex) {
        item.children.forEach((ing, insideIndex) => {
          if (insideIndex === index) {
            ing.globalKeyword = [...newReceivedKeywords] || [];
          }
        });
      }
    });
  } catch (e) {
    console.error("Error fetching ingredient keywords:", e);
  }
};

// Remove all keywords
const removeAllKeywords = () => {
  keywordData.value = [];
  isKeywordModified.value = false;
};


const saveButtonEnable = () => {
  isCampaignModified.value = true;
};

const openImageLinkPage = () => {
  window.open(recipePreviewImageUrlLinkUpdate.value, "_blank");
};

const ingredientKeywordsPopup = (
  groupIndex,
  index,
  name,
  uom,
  quantity,
  note,
  word,
  globalKeywords,
  ingredient,
  btn
) => {
  buttonString.value = btn;
  keywordData.value = [];
  recievedKeywords.value = [...globalKeywords];
  holdIngredient.value = ingredient;

  if (word?.length) {
    word.forEach((item) => {
      keywordData.value.push(item);
    });
  }

  tempIngredientDataName.value = name;
  tempIngredientDataUom.value = uom;
  tempIngredientDataQuantity.value = quantity;
  tempIngredientDataNotes.value = note;
  tempGroupIndex.value = groupIndex;
  tempIndex.value = index;
  isKeywordModified.value = false;
  isIngredientKeywordsPopupModal.value = true;
};

const removeIngredientKeyword = (ingredient, keyIndex) => {
  setCampaignModified();
  ingredient.keywords.splice(keyIndex, 1);
  resetShoppableProductsAsync(ingredient);
};

const checkIngredientPopupName = (index) => {
  const name = getRef("ingredientePopupProductName" + index);
  if (name.scrollWidth > name.clientWidth) {
    isIngredientNameTooltipVisible.value = true;
  }
};

const hideIngredientNamePopupTip = (index) => {
  const name = getRef("ingredientePopupProductName" + index);
  if (name.scrollWidth > name.clientWidth) {
    isIngredientNameTooltipVisible.value = false;
  }
};

const addKeywordBackword = () => {
  shoppableIngredientsData.value.children.forEach((data, tIndex) => {
    if (tIndex == tempGroupIndex.value) {
      data.children.forEach((ingredient, iIndex) => {
        if (tempIndex.value === iIndex) {
          ingredient.keywords = [...keywordData.value];
        }
      });
    }
    isCampaignModified.value = true;
  });
  resetShoppableProductsAsync(holdIngredient.value);
  isIngredientKeywordsPopupModal.value = false;
};

const removeKeyWord = (index) => {
  keywordData.value.forEach((item, position) => {
    if (position === index) {
      keywordData.value.splice(position, 1);
    }
  });
  isKeywordModified.value = keywordData.value.length > 0;
};

const inputDataKeyword = (value) => {
  if (value.trim() !== "" && !value.includes(", ")) {
    keywordData.value.push(value);
    tempKeyword.value = "";
  }

  if (value.trim().includes(", ")) {
    const inputDataKeywordSpace = value.split(", ");
    inputDataKeywordSpace.forEach((item) => {
      if (item !== "") {
        keywordData.value.push(item);
      }
    });
    tempKeyword.value = "";
  }

  if (value.trim() !== "") {
    isKeywordModified.value = true;
  }
};

const showAddQuantityMethod = (
  groupIndex,
  index,
  name,
  uom,
  quantity,
  note
) => {
  tempingdata.name = name;
  tempingdata.uom = uom;
  tempingdata.quantity = quantity;
  tempingdata.notes = note;
  tempGroupIndex.value = groupIndex;
  tempIndex.value = index;
  inputIngredientWeight.value = "";
  searchListData.value.forEach((item) => {
    item.isChecked = item.name === "Gram";
  });
  showAddQuantity.value = true;
};

const disableAddButton = (inputIngredientWeight) => {
  const numbers = /^\d+$/;
  return inputIngredientWeight.match(numbers);
};

const setIngredientWeightAsync = async () => {
  let tempunit = "";
  searchListData.value.forEach((item) => {
    if (item.isChecked) {
      tempunit = item.name;
    }
  });

  let ingredient = null;
  if (tempunit !== "") {
    shoppableIngredientsData.value.children.forEach((item, index) => {
      if (index === tempGroupIndex.value) {
        item.children.forEach((ing, insideIndex) => {
          if (insideIndex === tempIndex.value) {
            ingredient = ing;
            if (tempunit === "Gram") {
              ing.weightInGrams = inputIngredientWeight.value;
            } else if (tempunit === "Milliliter") {
              ing.volumeInMl = inputIngredientWeight.value;
            }
          }
        });
      }
    });
  }

  isCampaignModified.value = true;
  showAddQuantity.value = false;

  await delay(300); // Delay for ensuring UI updates and asynchronous task completion
  if (!isSaveClicked.value && ingredient) {
    resetShoppableProductsAsync(ingredient);
  }
};
const removeIngredientWeightAsync = async (groupIndex, index) => {
  tempGroupIndex.value = groupIndex;
  tempIndex.value = index;
  let ingredient = null;

  shoppableIngredientsData.value.children.forEach((item, idx) => {
    if (idx === tempGroupIndex.value) {
      item.children.forEach((ing, insideIndex) => {
        if (insideIndex === tempIndex.value) {
          ingredient = ing;
          if (ing.weightInGrams !== "") {
            ing.weightInGrams = "";
          }
          if (ing.volumeInMl !== "") {
            ing.volumeInMl = "";
          }
        }
      });
    }
  });

  isCampaignModified.value = true;
  await delay(300); // Adding a delay to prevent rapid consecutive executions of the resetShoppableProductsAsync method
  if (!isSaveClicked.value && ingredient) {
    resetShoppableProductsAsync(ingredient);
  }
};

const selectedRecipeStatuses = (info) => {
  searchListData.value.forEach((data) => {
    data.isChecked = info.name === data.name;
    if (data.isChecked) {
      statusesCSV.value = data.name;
    }
  });
};

const disableScroll = () => {
  document.addEventListener("wheel", () => {
    if (
      document.activeElement.type === "number" &&
      document.activeElement.classList.contains("no-scroll")
    ) {
      document.activeElement.blur();
    }
  });
};

const shoppableIngredientName = (index) => {
  const nameIng = getRef("foodItemName" + index);
  if (nameIng.scrollWidth > nameIng.clientWidth) {
    isShoppableIngredientNameVisible.value = true;
  }
};

const shoppableIngredientNamehide = (index) => {
  isShoppableIngredientNameVisible.value = false;
};

const shoppableIngredientNotes = (index) => {
  const ingNotes = getRef("ingredientNote" + index);
  if (ingNotes.scrollWidth > ingNotes.clientWidth) {
    isNoteTooltipVisible.value = true;
  }
};

const shopabbleIngredientNotesHide = (index) => {
  isNoteTooltipVisible.value = false;
};

const setIngredientName = (brand, name) => {
  const ingBrand = brand.toLowerCase();
  const ingName = name.toLowerCase();
  const fullName = ingName.startsWith(ingBrand) ? name : `${brand} ${name}`;
  return { name: fullName, nameLength: fullName.length };
};

const closeKeyword = (ingredient) => {
  if (ingredient.keywordInput !== "") {
    setCampaignModified();
    const split = ingredient.keywordInput.split(", ");
    split.forEach((textSplit) => {
      if (textSplit.trim() !== "") {
        ingredient.keywords.push(textSplit.trim());
      }
    });
    ingredient.keywordInput = "";
    resetShoppableProductsAsync(ingredient);
  }
};

const ingredientUomAutocompleteArrowDown = (ingredient) => {
  if (searchedUomText.value === "") {
    if (
      ingredientUomAutocompleteArrowCounter.value + 1 <
      ingredientsUomList.value.length
    ) {
      ingredientUomAutocompleteArrowCounter.value++;
      const scroll = getRef("ingredientsUomList");
      if (scroll) {
        scroll.scrollTop += 33;
      }
    }
    ingredient.UOM =
      ingredientsUomList.value[
        ingredientUomAutocompleteArrowCounter.value
      ]?.display;
  } else if (searchedUomList.value && searchedUomList.value.length > 0 && searchedUomText.value !== "") {
    if (
      ingredientUomAutocompleteArrowCounter.value + 1 <
      searchedUomList.value.length
    ) {
      ingredientUomAutocompleteArrowCounter.value++;
    }
    ingredient.UOM =
      searchedUomList.value[
        ingredientUomAutocompleteArrowCounter.value
      ]?.display;
  }
};
const setIngredientUomResult = (result, ingredient) => {
  if (result.display == ingredient.UOM) return;

  ingredient.UOM = result.display;
  setCampaignModified();
  searchedUomText.value = "";
  ingredient.uomAutocomplete = false;
  checkInputPresence(ingredient);
  resetShoppableProductsAsync(ingredient);
};

const checkInputPresence = (ingredient) => {
  if (ingredient && !ingredient.UOM) {
    ingredientUomAutocompleteArrowCounter.value = -1;
  }
};

const ingredientUomAutocompleteArrowUp = (ingredient) => {
  const arrowCounter = ingredientUomAutocompleteArrowCounter.value;

  if (!searchedUomText.value && arrowCounter > 0) {
    ingredientUomAutocompleteArrowCounter.value -= 1;
    const scroll = getRef("ingredientsUomList");
    if (scroll) {
      scroll.scrollTop -= 33;
    }
    ingredient.UOM = ingredientsUomList.value[arrowCounter].display;
  } else if (searchedUomText.value && searchedUomList.value.length > 0) {
    if (arrowCounter > 0) {
      ingredientUomAutocompleteArrowCounter.value -= 1;
    }
    ingredient.UOM = searchedUomList.value[arrowCounter].display;
  }
};

const ingredientUomAutocompleteEnter = (ingredient) => {
  const arrowCounter = ingredientUomAutocompleteArrowCounter.value;
  if (arrowCounter > 0) {
    if (!searchedUomText.value) {
      ingredient.UOM = ingredientsUomList.value[arrowCounter].display;
    } else {
      ingredient.UOM = searchedUomList.value[arrowCounter].display;
    }
  }
  ingredientUomAutocompleteArrowCounter.value = -1;
  ingredient.uomAutocomplete = false;
};

const searchUOMList = (ingredient) => {
  searchedUomText.value = ingredient.UOM;
  searchedUomList.value = [];

  if (searchedUomText.value) {
    const filter = searchedUomText.value.toUpperCase();
    ingredientsUomList.value.forEach((item) => {
      const text = item.display ? item.display.toUpperCase() : "";
      if (text.startsWith(filter)) {
        searchedUomList.value.push(item);
      }
    });
  }
};

const closeUomDropdownAsync = async (ingredient) => {
  await delay(500);
  hasLastUOMClicked.value = false;
  ingredient.uomAutocomplete = false;
  if (isEmptyOrWhitespace(searchedUomText.value)) {
    return;
  }

  if (!ingredientsUomList.value || !ingredientsUomList.value.length ||
      ingredientsUomList.value.every((e) => e.display !== ingredient.UOM)) {
    ingredient.UOM = "";
  }
};

const toggleDropdownAsync = async (ingredient, index) => {
  const ingredientFocus = getRef(`ingredientUom${ingredient.id}`);
  ingredientFocus.focus();
  closeAllShoppableDropDown();

  if (searchedUomText.value || ingredient.UOM) {
    ingredientsUomList.value.forEach((item, idx) => {
      if (item.display === ingredient.UOM) {
        ingredientUomAutocompleteArrowCounter.value = idx;
      }
    });

    if (!ingredientsUomList.value.some((e) => e.display === ingredient.UOM)) {
      ingredient.UOM = "";
    }
  }

  if (!ingredient.UOM) {
    ingredientUomAutocompleteArrowCounter.value = 0;
  }

  searchedUomText.value = "";
  ingredient.uomAutocomplete = !ingredient.uomAutocomplete;
  const isLastItem = index === shoppableIngredientsData?.value?.children?.[0]?.children?.length - 1;
  const isUOMClickable = ingredient.uomAutocomplete && !ingredient.promotedTotalProducts && !ingredient.totalProducts;

  if (isLastItem && isUOMClickable) {
    hasLastUOMClicked.value = true;
  }
};

const toggleDropdownUOM = (ingredient) => {
  searchedUomText.value = "";
  if (ingredientsUomList.value.length > 0) {
    setCurrentIngredient(ingredient);
    ingredient.uomAutocomplete = true;
    getRef(`ingredientUom${ingredient.id}`).focus();
  }
};

const closeAllDropdownUOM = () => {
  shoppableIngredientsData.value.children.forEach((group) => {
    group.children.forEach((ingredient) => {
      ingredient.uomAutocomplete = false;
    });
  });
};

const getShoppableReviewAsync = async () => {
  await executeOnIngredientsInBatchesAsync((ingredient) => {
    if (ingredient.isCampaignDataPassedIn) {
      return resetShoppableProductsAsync(ingredient);
    } else {
      return resetShoppableCampaignAndProductsAsync(ingredient);
    }
  });
};
const executeOnIngredientsInBatchesAsync = async (fn) => {
  let ingredients = shoppableIngredientsData.value.children.flatMap(
    (group) => group.children
  );
  const batchSize = 5;

  while (ingredients.length > 0) {
    const batch = ingredients.splice(0, batchSize);
    try {
      await Promise.all(batch.map(fn));
    } catch (e) {
      console.error(e);
    }
  }
};
const debounceIngredientName = debounce((groupIndex, index, ingredient) => {
  handleIngredientNameChange(groupIndex, index, ingredient);
  ingredient.sortByDataKey = "default";
  ingredient.sortByDataName = "Default";
  resetSearchBrandAsync(ingredient); // You should define this function elsewhere in setup
}, 1500);

async function resetShoppableCampaignAndProductsAsync(ingredient) {
  ingredient.fromPromotedProductMatches = 0;
  ingredient.currentPagePromoted = 1;
  ingredient.originalCampaignData = getDefaultCampaignData();
  ingredient.campaignData = getDefaultCampaignData();

  try {
    const params = { ingredients: ingredient.name };
    const ingredientsCampaignData = await store.dispatch("ingredient/getIngredientsCampaignDataAsync", { params });
    if (ingredientsCampaignData?.length) {
      const recipeCampaign = ingredientsCampaignData.find(
        (campaign) => campaign.data?.recipe === props.recipeID
      );

      if (recipeCampaign) {
        ingredient.originalCampaignData = {
          ...recipeCampaign.data,
          version: recipeCampaign.version,
          identifier: recipeCampaign.identifier,
        };
      } else {
        const ingredientCampaign = ingredientsCampaignData.find(
          (campaign) => campaign.data && !campaign.data.recipe
        );

        if (ingredientCampaign) {
          ingredient.originalCampaignData = {
            ...ingredientCampaign.data,
          };
        }
      }
    }
  } catch (e) {
    console.error(e);
  }

  copyIngredientCampaignData(ingredient);
  return resetShoppableProductsAsync(ingredient);
}

const handleIngredientNameChange = (groupIndex, index, ingredient) => {
  searchGlobalKeywordForIngredient(ingredient, groupIndex, index);
  if (ingredient.name === "") {
    ingredient.promotedTotalProducts = 0;
    ingredient.promotedResult = [];
    ingredient.totalProducts = 0;
    ingredient.products = [];
    isErrorChecked.value = true;
  } else if (!isSaveClicked.value && ingredient.name !== "") {
    isErrorChecked.value = false;
    resetShoppableCampaignAndProductsAsync(ingredient);
  }
};

const handleIngredientQuantityChange = (ingredient) => {
  if (!isSaveClicked.value) {
    resetShoppableProductsAsync(ingredient);
  }
};
const getDefaultCampaignData = () => ({
  version: null,
  identifier: null,
  recipe: null,
  promotedProducts: [],
  includedProducts: [],
  filteredProducts: [],
  onlyPromoted: false,
  onlyIncluded: false,
  shoppableFlag: "shoppable",
  hasLabels: [],
});

const copyIngredientCampaignData = (ingredient) => {
  ingredient.campaignData.version = ingredient.originalCampaignData.version;
  ingredient.campaignData.identifier =
    ingredient.originalCampaignData.identifier;
  ingredient.campaignData.recipe = ingredient.originalCampaignData.recipe;
  ingredient.campaignData.promotedProducts = [
    ...ingredient.originalCampaignData.promotedProducts,
  ];
  ingredient.campaignData.includedProducts = [
    ...ingredient.originalCampaignData.includedProducts,
  ];
  ingredient.campaignData.filteredProducts = [
    ...ingredient.originalCampaignData.filteredProducts,
  ];
  ingredient.campaignData.onlyPromoted =
    ingredient.originalCampaignData.onlyPromoted;
  ingredient.campaignData.onlyIncluded =
    ingredient.originalCampaignData.onlyIncluded;
  ingredient.campaignData.shoppableFlag =
    ingredient.originalCampaignData.shoppableFlag;
  ingredient.campaignData.hasLabels = ingredient.originalCampaignData.hasLabels;
};
const copyCampaignData = (campaignData) => ({
  version: campaignData.version,
  identifier: campaignData.identifier,
  recipe: campaignData.recipe,
  promotedProducts: [...campaignData.promotedProducts],
  includedProducts: [...campaignData.includedProducts],
  filteredProducts: [...campaignData.filteredProducts],
  onlyPromoted: campaignData.onlyPromoted,
  onlyIncluded: campaignData.onlyIncluded,
  shoppableFlag: campaignData.shoppableFlag,
  hasLabels: campaignData.hasLabels,
});

const resetShoppableProductsAsync = async (ingredient) => {
  ingredient.fromProductMatches = 0;
  ingredient.currentPageUnPromoted = 1;
  ingredient.loading = true;
  ingredient.shoppableSearchQuery = "";
  ingredient.isSearchOn = false;

  ingredient.products.splice(
    0,
    ingredient && ingredient.products && ingredient.products.length
      ? ingredient.products.length
      : 0
  );

  // Fetch products (from API or other logic)
  let result = await getShoppableProductsAsync(ingredient);

  // Update ingredient with the new product results
  ingredient.promotedResult = [...result.promotedResult];
  ingredient.promotedTotalProducts = result.promotedTotalProducts || 0;
  ingredient.products = [...result.products];
  ingredient.totalProducts = result.totalProducts;
  ingredient.loading = false;

  // Call the function to check promoted labels
  checkPromotedLabelAsync();
};

const formatResults = (result, promotedResult) => ({
  promotedResult: promotedResult?.products ? [...promotedResult.products] : [],
  promotedTotalProducts: promotedResult?.totalProducts || 0,
  products: result?.products ? [...result.products] : [],
  totalProducts: result?.totalProducts || 0,
});

const getEmptyResults = () => ({
  products: [],
  totalProducts: 0,
  promotedResult: [],
  promotedTotalProducts: 0,
});

const getUnitKey = (ingredient) => {
  let unitKey = null;
  if (ingredient && ingredient.UOM) {
    const ingredientUOM = ingredient.UOM.toLowerCase();
    let unit = ingredientsUomList.value.find((uom) => uom?.display?.toLowerCase() === ingredientUOM);
    if (!unit) {
      throw new Error(`Unit ${ingredient.UOM} not found`);
    } else {
      unitKey = unit?.key;
    }
  }
  return unitKey;
};

const getPromotedProductMatchesAsync = async (ingredient) => {
  let productList = [];
  let totalProducts = 0;
  const promotedProducts = ingredient.campaignData?.promotedProducts ?? [];

  if (promotedProducts.length) {
    const unitKey = getUnitKey(ingredient);
    const payload = {
      recipeIsin: props.recipeID,
      name: ingredient.name,
      quantity: Number(ingredient.quantity),
      unit: unitKey,
      keywords: ingredient.keywords?.length
        ? ingredient.keywords
        : ingredient.globalKeyword ?? [],
      promotedGtins: promotedProducts,
      weightInGrams: ingredient.weightInGrams
        ? Number(ingredient.weightInGrams)
        : null,
      volumeInMl: ingredient.volumeInMl ? Number(ingredient.volumeInMl) : null,
    };
    const params = {
      lang: lang.value,
      from: ingredient.fromPromotedProductMatches ?? 0,
      size: numProductMatches.value,
    };

    try {
      await store.dispatch("shoppableReview/getIngredientPromotedProductsAsync", { params, payload });
      const response = store.getters["shoppableReview/getIngredientPromotedProducts"];
      ingredient.errorLoading = false;

      if (
        response?.ingredient === ingredient.name &&
        response?.products?.results?.length
      ) {
        const products = response.products.results;
        products.forEach((product) => {
          if (promotedProducts.includes(product.gtin)) {
            product.id = `${ingredient.id}_${product.gtin}`;
            product.changePositionPopupOpened = false;
            product.menuDropDown = false;
            productList.push(product);
          }
        });
        totalProducts = response.products.total || 0;
      }
    } catch (error) {
      console.error(error);
      ingredient.errorLoading = true;
    }
  }

  return {
    products: productList,
    totalProducts: totalProducts,
  };
};

const getShoppableProductsAsync = async (ingredient) => {
  try {
    let result, promotedResult;
    if (ingredient?.isSearchOn) {
      result = await getProductMatchesAsync(
        ingredient,
        0,
        ingredient.shoppableSearchQuery
      );
    } else {
      result = await getProductMatchesAsync(ingredient, 0, null);
    }
    promotedResult = await getPromotedProductMatchesAsync(ingredient);
    return formatResults(result, promotedResult);
  } catch (e) {
    console.error(e);
    ingredient.errorLoading = true;
    return getEmptyResults();
  }
};

async function getProductMatchesAsync(ingredient, skip, searchQuery) {
      const unitKey = getUnitKey(ingredient);
      const payload = reactive({
        recipeIsin: props.recipeID,
        name: ingredient?.name,
        quantity: Number(ingredient?.quantity),
        unit: unitKey,
        keywords: ingredient?.keywords ?? [],
        promotedGtins: ingredient?.campaignData?.promotedProducts,
        includedGtins: ingredient?.campaignData?.includedProducts,
        filteredGtins: ingredient?.campaignData?.filteredProducts,
        onlyIncluded: ingredient?.campaignData?.onlyIncluded,
        filterTerm: searchQuery,
        weightInGrams: ingredient?.weightInGrams ? Number(ingredient.weightInGrams) : null,
        volumeInMl: ingredient?.volumeInMl ? Number(ingredient.volumeInMl) : null,
        hasLabels: ingredient?.hasLabelsKeys ?? [],
        sort: ingredient?.sortByDataKey ?? "default",
      });

      if (!payload.keywords.length) {
        payload.keywords = ingredient?.globalKeyword ?? [];
      }

      const params = {
        lang: lang.value,
        from: ingredient?.fromProductMatches ?? 0,
        size: numProductMatches.value,
      };

      let response;
      try {
        await store.dispatch("ingredient/getIngredientProductMatchesAsync", { params, payload });
        response = store.getters['ingredient/getIngredientsProduct'];
        ingredient.size = response?.size ?? {};
        ingredient.errorLoading = false;
      } catch (e) {
        console.error(e);
        ingredient.errorLoading = true;
      } finally {
        ingredient.showUnpromotedLoader = false;
      }

      const products = [];
      let totalProducts = 0;

      if (response?.ingredient === ingredient.name && response?.products?.results) {
        response.products.results.forEach((product) => {
          product.id = `${ingredient.id}_${product.gtin}`;
          product.changePositionPopupOpened = false;
          product.menuDropDown = false;
          products.push(product);
        });
        totalProducts = response.products.total;
      }

      return {
        products,
        totalProducts,
      };
    }
const getRecipeUnitConfigAsync = async () => {
  const params = {
    lang: lang.value,
  };
  try {
    await store.dispatch("ingredient/getRecipeUnitConfigAsync", { params });
    const response = store.getters["ingredient/getRecipeUnit"];
    if (response?.units?.length) {
      ingredientsUomList.value = response.units
        .filter((unit) => unit && unit.display)
        .sort((a, b) => a.display.localeCompare(b.display));
    }
  } catch (e) {
    console.error(e);
    throw e;
  }
};
const getIngredientShoppable = (ingredient) => {
  if (ingredient.campaignData && ingredient.campaignData.shoppableFlag) {
    for (const shoppableData of shoppableListData.value) {
      if (shoppableData.key === ingredient.campaignData.shoppableFlag) {
        return shoppableData.data;
      }
    }
  }
  return "";
};
const checkIngredientPopName = (ingredientID, groupIndex, index) => {
  const name = document.getElementById(
    `ingredientPopProductName${ingredientID}${groupIndex}${index}`
  );

  if (name.scrollWidth > name.clientWidth) {
    isIngProductNameVisible.value = true;
  }
};

const hideIngredientNamePopTip = (ingredientID, groupIndex, index) => {
  const name = document.getElementById(
    `ingredientPopProductName${ingredientID}${groupIndex}${index}`
  );

  if (name.scrollWidth > name.clientWidth) {
    isIngProductNameVisible.value = false;
  }
};

const checkIngredientPopNameSrt = (ingredientID, groupIndex, index) => {
  const name = document.getElementById(
    `ingredientPopProductNamesrt${ingredientID}${groupIndex}${index}`
  );

  if (name.scrollWidth > name.clientWidth) {
    isIngredientProductNameVisible.value = true;
  }
};

// Hide the tooltip when the name fits within the width
const hideIngredientNamePopTipSrt = (ingredientID, groupIndex, index) => {
  const name = document.getElementById(
    `ingredientPopProductNamesrt${ingredientID}${groupIndex}${index}`
  );

  if (name.scrollWidth > name.clientWidth) {
    isIngredientProductNameVisible.value = false;
  }
};

// Check if the ingredient name needs to show the tooltip (scrollWidth > clientWidth)
const checkIngredientNamePopup = (index) => {
  const name = document.getElementById(`ingredientePopProductName${index}`);

  if (name.scrollWidth > name.clientWidth) {
    isPopupProductNameVisible.value = true;
  }
};

// Hide the tooltip for ingredient name popup
const hideIngredientNamePopupTooltip = (index) => {
  const name = document.getElementById(`ingredientePopProductName${index}`);

  if (name.scrollWidth > name.clientWidth) {
    isPopupProductNameVisible.value = false;
  }
};

// Get the subName for the shoppable ingredient
const getIngredientShoppableSubName = (ingredient) => {
  if (ingredient.campaignData && ingredient.campaignData.shoppableFlag) {
    for (const shoppableData of shoppableListData.value) {
      if (shoppableData.key === ingredient.campaignData.shoppableFlag) {
        return shoppableData.subName;
      }
    }
  }
  return "";
};

// Toggle the shoppable dropdown for the ingredient
const openShoppableDropDown = (ingredient) => {
  const isShoppableDropDown = ingredient.isShoppableDropDown;
  setCurrentIngredient(ingredient); // Assuming setCurrentIngredient is defined in your setup
  ingredient.isShoppableDropDown = !isShoppableDropDown;
};
const selectedShoppable = (ingredient, shoppable) => {
  if (ingredient.campaignData.shoppableFlag === shoppable) {
    return;
  }
  setCampaignModified(); // Assuming this is defined elsewhere
  if (ingredient.campaignData) {
    ingredient.campaignData.shoppableFlag = shoppable;
  }
  ingredient.campaignData.isShoppableDropDown = false;
};

const closeAllShoppableDropDown = () => {
  shoppableIngredientsData.value.children.forEach((group) => {
    group.children.forEach((ingredient) => {
      ingredient.isShoppableDropDown = false;
    });
  });
};

const showMoreDetails = (ingredient) => {
  ingredient.showTableDetails = !ingredient.showTableDetails;
};

const openShoppableList = (ingredient) => {
  ingredient.showShoppableList = !ingredient.showShoppableList;
};

const savePopup = () => {
  isSaveModalVisible.value = true;
};

const closeModal = () => {
  isReportModalVisible.value = false;
  selectedIssueType.value = "incorrectQuantity";
  issueDescription.value = "";
  isSaveModalVisible.value = false;
  isCancelModalVisible.value = false;
  isProductConfirmationDeleted.value = false;
  showAddQuantity.value = false;
  isIngredientKeywordsPopupModal.value = false;
  isPreviewReferenceProductDataVisible.value = false;
  isProductTagOpen.value = false;
  isErrorOccuredModalVisible.value = false;
};

const cancelShoppableReview = () => {
  if (isCampaignModified.value) {
    isCancelModalVisible.value = true;
  } else {
    $eventBus.emit("cancelShoppableReview");
  }
};

const cancelConfirmationPopup = () => {
  window.scrollTo(0, 0);
  $eventBus.emit("cancelShoppableReview");
};
const saveShoppableReview = () => {
  if (isSaveClicked.value) {
    return;
  }
  isSaveClicked.value = true;

  let ingredientsData = shoppableIngredientsData.value.children.map((group) => {
    let ingredients = group.children.map((ingredient) => {
      return {
        name: ingredient.name,
        quantity: ingredient.quantity,
        UOM: ingredient.UOM,
        externalId: ingredient.externalId,
        rawText: ingredient.rawText,
        foodItem: ingredient.foodItem,
        group: ingredient.group,
        excludeFromNutrition: ingredient.excludeFromNutrition,
        level: ingredient.level,
        modifier: ingredient.modifier,
        note: ingredient.note,
        remark: ingredient.remark,
        keywords: ingredient.keywords,
        keywordInput: "",
        id: ingredient?.id || "",
        productId: ingredient?.productId || "",
        volumeInMl: ingredient?.volumeInMl?.toString() || "",
        weightInGrams: ingredient?.weightInGrams?.toString() || "",
        campaignData: ingredient.campaignData,
        originalCampaignData: ingredient.originalCampaignData,
        globalKeyword: ingredient?.globalKeyword || [],
        hasOverridingCampaign: campaignHasChanges(
          ingredient.originalCampaignData,
          ingredient.campaignData
        ),
        uomAutocomplete: false,
        isDropDown: false,
      };
    });

    return {
      children: ingredients,
      name: group.name,
      setGroupName: group.setGroupName,
      displayGroup: group.displayGroup,
    };
  });

  window.scrollTo(0, 0);
  $eventBus.emit(
    "saveShoppableReview",
    ingredientsData,
    isCampaignModified.value
  );
};

const checkIngredientName = () => {
  isSaveModalVisible.value = false;
  isErrorChecked.value = false;

  // Check for empty ingredient names
  isErrorChecked.value = shoppableIngredientsData.value.children.some((item) =>
    item?.children?.some((insideItem) => insideItem.name === "")
  );

  if (isErrorChecked.value) {
    isErrorOccuredModalVisible.value = true;
  } else {
    saveShoppableReview();
    closeModal();
  }
};

const getProductId = (product) => {
  if (
    project.value.id === "e_heb" ||
    project.value.id === "heb_sandbox" ||
    project.value.id === "central_market" ||
    project.value.id === "joev"
  ) {
    return product.externalId || product.gtin || "";
  }
  return product.gtin;
};

const unPromoteProductAsync = async (ingredient, product) => {
  setCampaignModified();
  scrollToElement("product-matches-title-search-id-" + ingredient.id, 0);

  if (
    ingredient.promotedResult.length <= 1 &&
    ingredient.currentPagePromoted > 1
  ) {
    ingredient.fromPromotedProductMatches -= numProductMatches.value;
    ingredient.currentPagePromoted -= 1;
  }

  const index = ingredient.campaignData.promotedProducts.indexOf(product.gtin);
  if (index > -1) {
    ingredient.campaignData.promotedProducts.splice(index, 1);
  }

  ingredient.showPromotedLoader = true;
  ingredient.showUnpromotedLoader = true;

  const result = await getShoppableProductsAsync(ingredient);
  ingredient.showPromotedLoader = false;
  ingredient.showUnpromotedLoader = false;

  ingredient.promotedResult = result?.promotedResult || [];
  ingredient.products = result?.products || [];
  ingredient.totalProducts = result?.totalProducts || 0;
  ingredient.promotedTotalProducts = result?.promotedTotalProducts || 0;

  if (
    ingredient.isSearchOn &&
    ingredient.shoppableSearchQuery !== "" &&
    ingredient.name !== ""
  ) {
    await resetShoppableProductsAsync(ingredient);
  }

  triggerLoading($keys.KEY_NAMES.PRODUCT_UNPROMOTED);

  if (
    ingredient.campaignData.promotedProducts.length === 0 &&
    ingredient.campaignData.onlyPromoted
  ) {
    ingredient.campaignData.onlyPromoted = false;
  }

  await checkPromotedLabelAsync();
};
const promoteProductAsync = async (ingredient, product) => {
  triggerLoading("productPromoted");
  setCampaignModified();
  scrollToElement("promoted-products-count-section-id-" + ingredient.id, 0)

  if (ingredient.products.length <= 1 && ingredient.currentPageUnPromoted > 1) {
    ingredient.fromProductMatches -= numProductMatches.value;
    ingredient.currentPageUnPromoted -= 1;
  }

  ingredient.campaignData.promotedProducts.unshift(product.gtin);
  ingredient.showUnpromotedLoader = true;
  ingredient.showPromotedLoader = true;

  const result = await getShoppableProductsAsync(ingredient);
  ingredient.showUnpromotedLoader = false;
  ingredient.showPromotedLoader = false;

  ingredient.promotedResult = result?.promotedResult || [];
  ingredient.products = result?.products || [];
  ingredient.totalProducts = result.totalProducts || 0;
  ingredient.promotedTotalProducts = result.promotedTotalProducts || 0;

  checkPromotedLabelAsync();
};

const scrollToElement = (refName, headerOffset) => {
  let element = getRef(refName);
  let elementPosition = element?.offsetTop;
  let offsetPosition = elementPosition - headerOffset;
  document.documentElement.scrollTop = offsetPosition;
  document.body.scrollTop = offsetPosition;
};
const removeProduct = (ingredient, product) => {
  setCampaignModified();

  const index = ingredient.campaignData.includedProducts.indexOf(product.gtin);
  if (index > -1) {
    ingredient.campaignData.includedProducts.splice(index, 1);
  } else {
    ingredient.campaignData.filteredProducts.push(product.gtin);
  }
};

const setProductForRemove = (ingredient, product) => {
  product.menuDropDown = false;
  setCurrentIngredient(ingredient);
  setCurrentProduct(product);
  isProductConfirmationDeleted.value = true;
};

const setProductForReport = (ingredient, product) => {
  product.menuDropDown = false;
  setCurrentIngredient(ingredient);
  setCurrentProduct(product);
  isReportModalVisible.value = true;
};

const checkSearchProductMatches = (ingredient) => {
  if (!ingredient.shoppableSearchQuery && ingredient.isSearchOn) {
    resetShoppableProductsAsync(ingredient);
  }
};

const searchProductMatchesAsync = async (ingredient) => {
  ingredient.fromProductMatches = 0;
  ingredient.currentPageUnPromoted = 1;

  if (!ingredient.shoppableSearchQuery) {
    resetShoppableProductsAsync(ingredient);
    return;
  }

  ingredient.loading = true;
  ingredient.isSearchOn = true;
  ingredient.products.length = 0;

  const result = await getShoppableProductsAsync(ingredient);
  ingredient.promotedResult = [...result.promotedResult];
  ingredient.promotedTotalProducts = result.promotedTotalProducts || 0;
  ingredient.products = [...result.products];
  ingredient.totalProducts = result.totalProducts;
  ingredient.loading = false;

  checkPromotedLabelAsync();
};
const displayOption = (ingredient, product) => {
  const menuDropDown = product.menuDropDown;
  setCurrentProduct(product);
  product.menuDropDown = !menuDropDown;
};

const closeProductsDropDown = () => {
  shoppableIngredientsData?.value?.children?.forEach((data) => {
    data.children.forEach((item) => {
      item.products.forEach((product) => {
        product.menuDropDown = false;
      });
    });
  });
};

const handleClickOutside = (event) => {
  const excludedClassNames = [
    "ingredient-brand-data",
    "ingredient-brand-data shoppable-brand-data-add-background",
    "shoppable-brand-data-add-background",
    "brand-dropdown-icon rotate",
    "checkmark",
    "brand-details",
    "brand-selected",
    "ingredient-brand-search-bar",
    "search-bar-content",
    "search-bar-text text-title-2 font-normal",
    "exit-search-icon",
    "filter-save-btn",
    "filter-btn text-h3 disabled",
    "ingredient-search-brand-main",
    "search-icon-grey-image"
  ];
  if (!excludedClassNames.includes(event?.target?.className)) {
    let containerElement = getRef("shoppable-main-container");
    containerElement.style.marginBottom = "23px";
    closeFilterTagByDropdown();
  }
  handleShoppableEvents(event);
};


const handleShoppableEvents = (event) => {
  if (
    currentIngredient.uomAutocomplete &&
    !document
      .querySelector(`#uomDropDown${currentIngredient.value.id}`)
      ?.contains(event.target)
  ) {
    currentIngredient.value.uomAutocomplete = false;
  }

  if (
    currentIngredient.value?.isShoppableDropDown &&
    !document
      .querySelector(`#shoppableDropDown${currentIngredient.value.id}`)
      ?.contains(event.target)
  ) {
    currentIngredient.value.isShoppableDropDown = false;
  }

  if (
    currentProduct.value?.menuDropDown &&
    !document.querySelector(".menu-selected")?.contains(event.target)
  ) {
    currentProduct.value.menuDropDown = false;
  }

  if (
    currentIngredient.value?.sortByDropdownOpen &&
    !document
      .querySelector(`#sortByDropDown${currentIngredient.value.id}`)
      ?.contains(event.target)
  ) {
    currentIngredient.value.sortByDropdownOpen = false;
  }

  if (
    currentProduct.value?.changePositionPopupOpened &&
    !document
      .querySelector(`#changePosition${currentProduct.value.id}`)
      ?.contains(event.target)
  ) {
    currentProduct.value.changePositionPopupOpened = false;
  }
};
const selectedPositionAsync = async (ingredient, product, position) => {
  closeAllPromotedCountingBox();
  setCampaignModified();

  // Update promoted products
  ingredient.campaignData.promotedProducts.splice(
    ingredient.currentIndexOfPromotedIng - 1,
    1
  );
  ingredient.campaignData.promotedProducts.splice(
    position - 1,
    0,
    product.gtin
  );

  ingredient.showPromotedLoader = true;
  const result = await getShoppableProductsAsync(ingredient);
  ingredient.showPromotedLoader = false;

  // Update results
  ingredient.promotedResult = [...result.promotedResult];
  ingredient.promotedTotalProducts = result?.promotedTotalProducts || 0;

  checkPromotedLabelAsync();
};

const closeAllPromotedCountingBox = () => {
  shoppableIngredientsData?.value?.children?.forEach((data) => {
    data.children.forEach((item) => {
      item.promotedResult.forEach((product) => {
        product.changePositionPopupOpened = false;
      });
    });
  });
};

const openPromotedCountingBox = (product, ingredient, index) => {
  ingredient.currentIndexOfPromotedIng = index;
  ingredient.setPromotedCountingList = Array.from(
    { length: ingredient.promotedTotalProducts },
    (_, i) => ({
      position: i + 1,
    })
  );

  setCurrentProduct(product);
  product.changePositionPopupOpened = true;
};

const toggleOnlyPromoted = (ingredient) => {
  isIngredientPromoted.value = !isIngredientPromoted.value;
  setCampaignModified();
  ingredient.campaignData.onlyPromoted = !ingredient.campaignData.onlyPromoted;
};

const setCurrentProduct = (product) => {
  currentProduct.value = product;
  closeAllPromotedCountingBox();
  closeProductsDropDown();
};

const reportProductAsync = async () => {
  const payload = {
    isin: props.recipeID,
    ingredient: currentIngredient.value.name,
    gtin: currentProduct.value.gtin,
    type: selectedIssueType.value,
    description: issueDescription.value,
  };

  try {
    await store.dispatch(
      "shoppableReview/postProductSuggestionIssueAsync",
      { payload }
    );
  } catch (e) {
    console.error(e);
    throw e;
  }

  // Reset form
  isReportModalVisible.value = false;
  selectedIssueType.value = "incorrectQuantity";
  issueDescription.value = "";
};
const removeProductConfirmedAsync = async () => {
  if (
    currentIngredient.value.products.length <= 1 &&
    currentIngredient.value.currentPageUnPromoted > 1
  ) {
    currentIngredient.value.fromProductMatches -= numProductMatches.value;
    currentIngredient.value.currentPageUnPromoted -= 1;
  }

  removeProduct(currentIngredient.value, currentProduct.value);
  isProductConfirmationDeleted.value = false;
  currentIngredient.value.showUnpromotedLoader = true;

  const result = await getShoppableProductsAsync(currentIngredient.value);

  currentIngredient.value.products = result?.products
    ? [...result.products]
    : [];
  currentIngredient.value.totalProducts = result?.totalProducts || 0;

  triggerLoading($keys.KEY_NAMES.DELETED);
};

const setProducts = (ingredient, products) => {
  products.forEach((product, index) => {
    if (index < ingredient.products.length) {
      if (product.gtin !== ingredient.products[index].gtin) {
        ingredient.products.splice(index, 1, product);
      }
    } else {
      ingredient.products.push(product);
    }
  });
};

const campaignHasChanges = (campaignData1, campaignData2) => {
  if (
    campaignData1.promotedProducts.length !==
    campaignData2.promotedProducts.length
  ) {
    return true;
  }

  // Check for differences in promoted products
  if (
    campaignData1.promotedProducts.some(
      (gtin, i) => gtin !== campaignData2.promotedProducts[i]
    )
  ) {
    return true;
  }

  // Check for differences in filtered products
  if (
    campaignData1.filteredProducts.length !==
      campaignData2.filteredProducts.length ||
    !campaignData1.filteredProducts.every((gtin) =>
      campaignData2.filteredProducts.includes(gtin)
    )
  ) {
    return true;
  }

  if (
    campaignData1.includedProducts.length !==
      campaignData2.includedProducts.length ||
    !campaignData1.includedProducts.every((gtin) =>
      campaignData2.includedProducts.includes(gtin)
    )
  ) {
    return true;
  }

  // Check other flags
  return (
    campaignData1.onlyPromoted !== campaignData2.onlyPromoted ||
    campaignData1.shoppableFlag !== campaignData2.shoppableFlag ||
    JSON.stringify(campaignData1.hasLabels) !==
      JSON.stringify(campaignData2.hasLabels)
  );
};

const setCampaignModified = () => {
  isCampaignModified.value = true;
};
const updateArrayAsync = async (ingredient) => {
  isCampaignModified.value = true;

  const preArray = ingredient.promotedResult.map((data) => data.gtin); // Collect GTINs into preArray
  const newArray = [...ingredient.campaignData.promotedProducts]; // Create a shallow copy to avoid mutating the original

  // Update newArray with the new GTINs
  newArray.splice(
    ingredient.fromPromotedProductMatches,
    preArray.length,
    ...preArray
  );
  ingredient.campaignData.promotedProducts = newArray;

  ingredient.showPromotedLoader = true;

  try {
    const result = await getShoppableProductsAsync(ingredient);
    ingredient.promotedResult = result?.promotedResult
      ? [...result.promotedResult]
      : [];
    ingredient.products = result?.products ? [...result.products] : [];
    ingredient.totalProducts = result?.totalProducts || 0;
    ingredient.promotedTotalProducts = result?.promotedTotalProducts || 0;
  } catch (error) {
    console.error("Error fetching shoppable products:", error);
  } finally {
    ingredient.showPromotedLoader = false;
  }
};
</script>
