<template>
  <div>
    <Modal @close="$emit('closeModal')">
      <template #editProductMatches>
        <div class="save-recipe-variant-modal">
          <div class="save-info-popup-container">
            <div class="save-image">
              <div class="save-image-container">
                <img alt="" class="save-icon" src="~/assets/images/Exit-recipe-variant.png" />
              </div>
            </div>
          </div>
          <div class="publish-content">
            <div class="publish-head">
              {{ $t('DESCRIPTION_POPUP.EXIT_PAGE_POPUP') }}
            </div>
            <div class="note-message" v-if="props.availableLang.length > 1">
              {{ $t('ALL_UPDATES_VARIANT_FORMS') }}
            </div>
            <div class="note-message" v-if="props.isCampaignModifiedFromShoppableReview">
             {{ $t('CHANGES_WILL_NOT_BE_SAVED') }}
            </div>
            <div class="button-container">
              <button type="button" class="btn-green-outline" @click="props.closeModal">{{ $t('BUTTONS.CANCEL_BUTTON')
              }}</button>
              <button type="button" class="btn-green" @click="props.callConfirm">{{ $t('BUTTONS.CONFIRM_BUTTON') }}</button>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import Modal from "@/components/Modal";

const props = defineProps({
  isCampaignModifiedFromShoppableReview: {
    type: Boolean,
  },
  availableLang: {
    type: Array,
  },
  callConfirm: {
    type: Function,
  },
  closeModal: {
    type: Function,
  },
});
</script>
