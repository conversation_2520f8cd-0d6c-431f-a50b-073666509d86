<template>
  <div
    :class="[
      'recipe-info-overview-section',
      { content: isRecipeImportedSectionVisible && getFeatureConfig[$keys.KEY_NAMES.GENERATOR] },
    ]"
  >
    <div class="recipe-head-count-section border-box">
      <div class="image">
        <img data-test-id="recipe-count-image-icon" src="@/assets/images/Recipes.png" alt="recipes count"/>
      </div>
      <div class="recipe-count">
        <span data-test-id="recipe-total-count" class="head-count">{{ recipeTotalCount }}</span>
        <span class="text">{{ $t('COMMON.RECIPES') }}</span>
      </div>
    </div>
    <div class="count-section border-box">
      <div class="recipe-count">
        <span data-test-id="recipe-manual-count"  class="count">{{ recipeManualCount }}</span>
        <span data-test-id="recipe-manual-count-label" class="text">{{ $t('OVERVIEW.MANUAL') }}</span>
      </div>
    </div>
    <div v-if="isRecipeImportedSectionVisible" class="count-section border-box">
      <div class="recipe-count">
        <span data-test-id="recipe-imported-count" class="count">{{ recipeImportedCount }}</span>
        <span data-test-id="recipe-imported-count-label" class="text">{{ $t('OVERVIEW.IMPORTED') }}</span>
      </div>
    </div>
    <div v-if="getFeatureConfig[$keys.KEY_NAMES.GENERATOR]" class="count-section border-box last-box">
      <div class="recipe-count">
        <span data-test-id="recipe-generated-count" class="count">{{ recipeGeneratedCount }}</span>
        <span data-test-id="recipe-generated-count-label" class="text">{{ $t('OVERVIEW.GENERATED') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, getCurrentInstance } from 'vue';
import { useNuxtApp } from '#app'
import { useStore } from 'vuex';
import { useProjectLang } from "@/composables/useProjectLang";
const { readyProject } = useProjectLang();  

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $eventBus } = useNuxtApp();

const props = defineProps({
  project: Object,
});

const recipeGeneratedCount = ref(0);
const recipeTotalCount = ref(0);
const recipeManualCount = ref(0);
const recipeImportedCount = ref(0);

const store = useStore();

const isRecipeImportedSectionVisible = computed(() => {
  if (store) {
    return !!store.getters['config/getFeatures']?.importer;
  }
  return false;
});
const getFeatureConfig = computed(() => store.getters["config/getFeatures"]);

const getRecipeMatchesAsync = async() => {
  const lang = store.getters['userData/getDefaultLang'];
  await store
    .dispatch('recipe/getOverviewRecipeStatisticsAsync', { lang })
    .then((response) => {
      const recipes = response?.data?.recipes || {};
      recipeGeneratedCount.value = recipes?.generated || 0;
      recipeManualCount.value = recipes?.manual || 0;
      recipeImportedCount.value = recipes?.imported || 0;
      recipeTotalCount.value =
        recipeGeneratedCount.value +
        recipeManualCount.value +
        (isRecipeImportedSectionVisible.value ? recipeImportedCount.value : 0);
    })
    .catch(console.error);
}

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await getRecipeMatchesAsync();
    }
  });
  $eventBus.on('recipeCountUpdate', getRecipeMatchesAsync);
  $eventBus.on('fetchRecipeDetails', getRecipeMatchesAsync);
});

onBeforeUnmount(() => {
  $eventBus.off('recipeCountUpdate');
  $eventBus.off('fetchRecipeDetails');
});
</script>
