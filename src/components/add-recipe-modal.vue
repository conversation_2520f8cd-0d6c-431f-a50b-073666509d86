<template>
    <div>
        <Modal>
            <template #addRecipeMatches>
                <div class="add-category-recipe-matches-modal">
                    <div class="header-section">
                        <div class="title-section">
                            <div v-if="isAddTag || isEditTag" class="title">{{ $t('TAG.ADD_RECIPE_TO_TAG') }}</div>
                            <div v-if="isAddCategory || isEditCategories" class="title">{{
                                $t('CATEGORY.ADD_RECIPE_TO_CATEGORY') }}</div>
                            <div class="search-box-pop">
                                <input
                                  type="text"
                                  class="search-input-box"
                                  placeholder="Find a recipe"
                                  @keypress.enter="searchPopUpAsync()"
                                  v-model.trim="queryPopUp"
                                  :class="{ 'align-search-input-box': queryPopUp }"
                                />
                                <button
                                  type="button"
                                  class="btn-reset search-icon-green-image"
                                  @click="searchPopUpAsync()"
                                >
                                  <img
                                    alt="search"
                                    src="@/assets/images/search-grey.png"
                                  />
                                </button>
                                <button
                                  type="button"
                                  class="btn-reset exit-search-icon"
                                  @click="resetPopupQuery()"
                                >
                                  <img
                                    alt="exit"
                                    v-if="isExitSearchPopupEnabled"
                                    src="@/assets/images/exit-search.svg?skipsvgo=true"
                                  />
                                </button>
                            </div>
                        </div>
                        <div class="close-icon-section">
                            <div class="close-icon">
                                <img src="@/assets/images/exit-gray.png" alt="" @click="closeModal()">
                            </div>
                        </div>
                    </div>
                    <div class="add-table-content" ref="addTable">
                        <div v-if="isTableDataLoading" class="table-image-loader">
                            <div class="loader"></div>
                        </div>
                        <table class="add-table" v-if="!isTableDataLoading">
                            <caption></caption>
                            <tbody>
                                <div v-if="addRecipeList == ''" class="no-recipe-result">
                                    {{ $t('COMMON.NO_RESULTS') }}
                                </div>
                                <tr></tr>
                                <tr class="add-recipe-body" v-for="(add, index) in addRecipeList"
                                    :class="{ 'change-background-color-for-selected': add.isAdded }" :key="index">
                                    <td class="table-image-recipe">
                                        <div class="recipe-image-wrapper">
                                            <img alt=""
                                                class="recipe-image"
                                                :src="add.media?.[lang]?.image || add.media?.[lang]?.externalImageUrl || defaultImage"
                                                @error="$event.target.src = defaultImage" />
                                        </div>
                                    </td>
                                    <td class="table-recipe-code">
                                        <div class="recipe-code">
                                            {{ add.isin ? add.isin : "" }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="recipe-name-tooltip">
                                            <span>
                                                {{ add.title[lang] ? add.title[lang] : "" }}
                                            </span>
                                        </div>
                                        <div class="recipe-subtitle" v-if="add && add.subtitle && add.subtitle[lang]">
                                            <span>
                                                {{
                                                    add && add.subtitle && add.subtitle[lang]
                                                    ? add.subtitle[lang]
                                                    : " "
                                                }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="recipe-details">
                                            <div class="details" v-if="add.time && parseDurationString(add.time.total)">
                                                <img alt="" class="image" src="@/assets/images/Time-meal.png" />
                                                <span>{{
                                                    parseDurationString(add.time.total ? add.time.total : "")
                                                    ?? ""
                                                }}</span>
                                            </div>
                                            <div class="details" v-if="add.ingredients &&
                                                    add.ingredients[lang] &&
                                                    add.ingredients[lang].length
                                                    ">
                                                <img alt="" class="image"
                                                    src="@/assets/images/shopping_cart_black_24dp.png" />
                                                <span v-if="add.ingredients &&
                                                    add.ingredients[lang] &&
                                                    add.ingredients[lang].length == 1
                                                    ">{{
        add.ingredients && add.ingredients[lang]
        ? add.ingredients[lang].length
        : ""
    }}
                                                    ingredient</span>
                                                <span v-if="add.ingredients &&
                                                        add.ingredients[lang] &&
                                                        add.ingredients[lang].length > 1
                                                        ">{{
            add.ingredients && add.ingredients[lang]
            ? add.ingredients[lang].length
            : ""
        }}
                                                    ingredients</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button
                                            type="button"
                                            class="btn-green-outline"
                                            :class="{
                                                disabled: add.isAlreadyAdded
                                            }"
                                            @click="addCategoryRecipe(add, index)"
                                            @keydown="preventEnterAndSpaceKeyPress($event)">
                                            {{ add.isAdded ? $t('COMMON.ADDED') : $t('COMMON.ADD') }}
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="load-button" v-if="fromPopUp + sizePopUp < addRecipeListTotal">
                            <button type="button" class="btn-green" @click="loadMoreRecipesAsync()"
                                @keydown="preventEnterAndSpaceKeyPress($event)">
                                {{ $t('COMMON.LOAD_MORE') }}
                            </button>
                        </div>
                    </div>
                    <div class="done-section">
                        <button type="button" class="btn-green" @click="recipeAdded()"
                            @keydown="preventEnterAndSpaceKeyPress($event)">{{ $t('BUTTONS.DONE_BUTTON') }}</button>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script setup>
import Modal from "@/components/Modal";
import RecipeService from "@/services/RecipeService";
import { ref, onMounted ,watch } from 'vue';
import { useStore } from 'vuex';
import { useProjectLang } from "@/composables/useProjectLang";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { useRouter } from 'vue-router';
import { useRefUtils } from '@/composables/useRefUtils'; // Adjust if this is a Nuxt 3 composable
import { useEventUtils } from '@/composables/useEventUtils';
import defaultImage from "~/assets/images/default_recipe_image.png";
import { useNuxtApp } from "#app";

// Reactive variables
const project = ref({});
const { $tracker, $auth ,$eventBus } = useNuxtApp();
const store = useStore();
const { parseDurationString } = useTimeUtils();

const { preventEnterAndSpaceKeyPress } = useEventUtils()
const { readyProject, getProject } = useProjectLang();
const addRecipeListTotal = ref(0);
const router = useRouter();
const addRecipeList = ref([]);
const queryPopUp = ref("");
const { getRef } = useRefUtils();
const fromPopUp = ref(0);
const sizePopUp = ref(10); // Change to ref for reactivity
const isTableDataLoading = ref(true);
const isExitSearchPopupEnabled = ref(false);
const localSelectedRecipe = ref([]);
const lang = ref("");
const localRemoveRecipeList = ref([]);
const localRemoveRecipeTagList = ref([]);
const localRecipesAfterPageChange = ref([]);
const localSelectedCategoryRecipe = ref([]);

const props = defineProps({
    recipeDataForCategories: {
        type: Array,
    },
    categoryPromotedRecipes: {
        type: Array,
    },
    selectedCategoryRecipe: {
        type: Array,
    },
    removeRecipeList: {
        type: Array,
    },
    recipesAfterPageChange: {
        type: Array,
    },
    recipeMatchesIsinsRemove: {
        type: Array,
    },
    isEditCategories: {
        type: Boolean,
        default: false // Optional: set a default value
    },
    isAddCategory: {
        type: Boolean,
        default: false // Optional: set a default value
    },
    isPageLoading: {
        type: Boolean,
        default: false // Optional: set a default value
    },
    isAddTag: {
        type: Boolean,
        default: false // Optional: set a default value
    },
    isEditTag: {
        type: Boolean,
        default: false // Optional: set a default value
    },
    recipeMatchesIsinsTagRemove: {
        type: Array,
    },
    removeRecipeTagList: {
        type: Array,
    },
    closeModal: {
        type: Function,
        required: true
    },
    onRecipeAdded: {
        type: Function,
        default: () => {}
    },
    campaignModifiedAddRecipe: {
        type: Function,
        default: () => {}
    }
});

onMounted(async () => {
    // Resetting variables
    fromPopUp.value = 0;
    addRecipeList.value = [];
    addRecipeListTotal.value = 0;
    isTableDataLoading.value = true;
    queryPopUp.value = "";
    isExitSearchPopupEnabled.value = false;
    lang.value = store.getters["userData/getDefaultLang"];
    project.value = await getProject();
    await getRecipeForCategories(); // Assuming getRecipeForCategories is defined
});

const getRecipeForCategories = async () => {
    const response = await RecipeService.getRecipeForCategoriesPopUp(
        project.value,
        queryPopUp.value,
        router.currentRoute.value.query.isin || [],
        fromPopUp.value,
        sizePopUp.value, // Assuming sizePopUp is defined elsewhere
        lang.value,
        store,
        $auth
    );

    response.results.forEach((data) => {
        data.isAlreadyAdded = false;
        if (props.isAddCategory) {
            data.alreadyInPromotedRecipes = false;
        }
        data.dropDown = false;
        data.isAdded = false;
        data.isSearched = false;
        data.isSelected = true;

        // Check for edits
        if (props.isEditCategories || props.isEditTag) {
            props.recipesAfterPageChange.forEach((item) => {
                if (data.isin === item.isin) {
                    data.isAdded = true;
                    data.isAlreadyAdded = true;
                }
            });
        }

        // Check against existing data
        props.recipeDataForCategories.forEach((item) => {
            if (data.isin === item.isin) {
                if (props.isAddCategory) {
                    data.alreadyInPromotedRecipes = true;
                }
                data.isAdded = true;
            }
        });

        if (props.isAddCategory || props.isEditCategories) {
            props.categoryPromotedRecipes.forEach((item) => {
                if (data.isin === item.isin) {
                    data.isAdded = true;
                }
            });
        }
    });

    addRecipeListTotal.value = response.total;

    if (fromPopUp.value >= 10) {
        response.results.forEach((data) => {
            props.recipeDataForCategories.forEach((item) => {
                if (data.isin === item.isin) {
                    data.isAlreadyAdded = true;
                }
            });
        });
        return response.results;
    } else {
        addRecipeList.value = response.results;
        addRecipeList.value.forEach((data) => {
            props.recipeDataForCategories.forEach((item) => {
                if (data.isin === item.isin) {
                    data.isAlreadyAdded = true;
                }
            });
        });
    }

    isTableDataLoading.value = false;
};


const loadMoreRecipesAsync = async () => {
    let from = parseInt(fromPopUp.value) + sizePopUp.value;
    if (from < addRecipeListTotal.value) {
        fromPopUp.value = from;
        const loadMoreRecipeList = await getRecipeForCategories();
        if (loadMoreRecipeList && addRecipeList.value) {
            addRecipeList.value = [...addRecipeList.value, ...loadMoreRecipeList];
        }
    }
};

const removeFromArray = (array, recipe) => {
    const index = array.findIndex(data => data.isin === recipe.isin);
    if (index !== -1) {
        array.splice(index, 1);
    }
};

const addCategoryRecipe = (recipe) => {
    const shouldAddCategoryOrTag = props.isAddCategory || props.isAddTag; // Assuming these are defined
    const shouldEditCategoriesOrTag = props.isEditCategories || props.isEditTag; // Assuming these are defined
    if (shouldAddCategoryOrTag) {
        handleAddCategoryOrTag(recipe);
    } else if (shouldEditCategoriesOrTag) {
        handleEditCategoriesOrTag(recipe);
    }
};

const handleAddCategoryOrTag = (recipe) => {

    if (recipe.isAdded) {
        removeFromArray(localSelectedRecipe.value, recipe);
        recipe.isAdded = false;
    } else {
        const exists = localSelectedRecipe.value.some(item => item.isin === recipe.isin);
        if (!exists) {
            recipe.isAdded = true;
            localSelectedRecipe.value.push(recipe);
        }
    }
};

const handleData = () => {
    addRecipeList.value.forEach((recipe) => {
        localSelectedRecipe.value.forEach((data) => {
            if (data.isin === recipe.isin) {
                recipe.isAdded = true;
            }
        });
    });
};

const handleEditCategoriesOrTag = (recipe) => {
    // Call campaign modified callback
    props.campaignModifiedAddRecipe();

    if (recipe.isAdded) {
        handleRemoveEditCategoriesOrTag(recipe);
    } else {
        handleAddEditCategoriesOrTag(recipe);
    }
};

const handleRemoveEditCategoriesOrTag = (recipe) => {
    if (props.isEditCategories) {
        removeFromArray(localSelectedRecipe.value, recipe);
        localRemoveRecipeList.value = localRemoveRecipeList.value.filter(data => data.isin !== recipe.isin);
    } else if (props.isEditTag) {
        removeFromArray(localSelectedRecipe.value, recipe);
        localRemoveRecipeTagList.value = localRemoveRecipeTagList.value.filter(data => data.isin !== recipe.isin);
    }
    recipe.isAdded = false;
};

const handleAddEditCategoriesOrTag = (recipe) => {
    recipe.isAdded = true;
    localSelectedRecipe.value.push(recipe);
    if (!localRecipesAfterPageChange.value.some(data => data.isin === recipe.isin)) {
        localRecipesAfterPageChange.value.push(recipe);
    }
    if (!localSelectedCategoryRecipe.value.includes(recipe.isin)) {
        localSelectedCategoryRecipe.value.push(recipe.isin);
    }
};

const resetPopupQuery = () => {
    queryPopUp.value = "";
    isExitSearchPopupEnabled.value = false;
    searchPopUpAsync(); // Assuming this is defined
};

const addToRecipeLists = (sourceArray, targetArray, categoryList) => {
    sourceArray.forEach((data) => {
        if (data.isAdded) {
            targetArray.unshift(data);
            categoryList.push(data.isin);
        }
    });
};

const recipeAdded = () => {
    // Get the selected recipes to pass to parent
    const selectedRecipes = localSelectedRecipe.value.filter(recipe => recipe.isAdded);

    // Call the parent's onRecipeAdded callback with selected recipes
    if (selectedRecipes.length > 0) {
        props.onRecipeAdded(selectedRecipes);
    }

    // Call campaign modified callback if needed
    if (selectedRecipes.length > 0) {
        props.campaignModifiedAddRecipe();
    }

    // Close the modal
    closeModal();
};

const searchPopUpAsync = async () => {
    isExitSearchPopupEnabled.value = !!queryPopUp.value;
    fromPopUp.value = 0;
    await getRecipeForCategories();
    if (addRecipeList.value.length) {
        handleData();
    }
};

const resetLocalData = () => {
    localSelectedRecipe.value = [];
    localRemoveRecipeList.value = [];
    localRemoveRecipeTagList.value = [];
    localSelectedCategoryRecipe.value = [];
    localRecipesAfterPageChange.value = [];
};

const closeModal = () => {
    // Reset local state
    queryPopUp.value = "";
    isExitSearchPopupEnabled.value = false;
    resetLocalData();

    // Call parent's close modal function
    props.closeModal();
};

watch(
    () => props.removeRecipeList,
    (newValue) => {
        localRemoveRecipeList.value = [...newValue];
    }
);

watch(
    () => props.removeRecipeTagList,
    (newValue) => {
        localRemoveRecipeTagList.value = [...newValue];
    }
);

</script>
