<template>
  <content-wrapper>
    <section class="iq-ui">
      <div class="iq-ui-container">
        <div class="iq-ui-container-head">
          <h2 id="iq-ui-checkbox" class="display-2 iq-ui-container-title">
            Button <code>src/assets/scss/ui/_btn.scss</code>
          </h2>
        </div>
        <div class="iq-ui-container-body">
          <p><b>Default</b></p>
          <p><button type="button" class="btn">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Disabled</b></p>
          <p>
            <button type="button" class="btn" disabled>Create</button>
            <button type="button" class="btn-green" disabled>Create</button>
            <button type="button" class="btn-green-outline" disabled>Create</button>
            <button type="button" class="btn-green-text" disabled>Create</button>
            <button type="button" class="btn-red" disabled>Create</button>
            <button type="button" class="btn-red-outline" disabled>Create</button>
            <button type="button" class="btn-red-text" disabled>Create</button>
            <button type="button" class="btn-white" disabled>Create</button>
          </p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Example: <code>&lt;button type="button" class="btn-green" disabled&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Green</b></p>
          <p><button type="button" class="btn-green">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-green</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-green"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Green Outline</b></p>
          <p><button type="button" class="btn-green-outline">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-green-outline</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-green-outline"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Green Text</b></p>
          <p><button type="button" class="btn-green-text">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-green-text</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-green-text"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Red</b></p>
          <p><button type="button" class="btn-red">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-red</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-red"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Red Outline</b></p>
          <p><button type="button" class="btn-red-outline">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-red-outline</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-red-outline"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Red Text</b></p>
          <p><button type="button" class="btn-red-text">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-red-text</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-red-text"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>White</b></p>
          <p><button type="button" class="btn-white">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-white</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-white"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>With icon</b></p>
          <p>
            <button type="button" class="btn-green">
              <img src="@/assets/images/generator-white-icon.png" alt="generator white icon" />
              <span>Create</span>
            </button>
            <button type="button" class="btn-green-outline">
              <span>Create</span>
              <img alt="arrow" src="@/assets/images/arrow-down-green.png" />
            </button>
            <button type="button" class="btn-green-text">
              <img alt="article" src="@/assets/images/article_green_image.svg?skipsvgo=true" />
              <span>Create</span>
            </button>
          </p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-green</code> and <code>btn-green-outline</code>. The icon can be from any side.</span>
              <span>Example:
              <br><br><code>&lt;button type="button" class="btn-green"&gt;&lt;img src="@/assets/images/generator-white-icon.png" alt="generator white icon" /&gt;&lt;span&gt;Create&lt;/span&gt;&lt;/button&gt;</code>
              <br><br><code>&lt;button type="button" class="btn-green-outline"&gt;&lt;span&gt;Create&lt;/span&gt;&lt;img src="@/assets/images/arrow-down-green.png" alt="arrow" /&gt;&lt;/button&gt;</code>
              <br><br><code>&lt;button type="button" class="btn-green-text"&gt;&lt;img src="@/assets/images/article_green_image.svg" alt="article" /&gt;&lt;span&gt;Create&lt;/span&gt;&lt;/button&gt;</code>
            </span>
            </p>
          </div>

          <p><b>Small</b></p>
          <p><button type="button" class="btn-small btn-green">Create</button></p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-green</code>. The icon can be added.</span>
              <span>Example: <code>&lt;button type="button" class="btn-small btn-green"&gt;Create&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Icon Button</b></p>
          <p>
            <button type="button" class="btn-icon">
              <img src="@/assets/images/edit-icon.png" alt="edit icon">
            </button>
          </p>
          <div class="iq-ui-container-body-wrapper">
            <p>
              <span>Class: <code>btn-icon</code>.</span>
              <span>Example: <code>&lt;button type="button" class="btn-icon"&gt;&lt;img src="@/assets/images/edit-icon.png" alt="edit icon"&gt;&lt;/button&gt;</code></span>
            </p>
          </div>

          <p><b>Utility classes</b></p>
          <p>
            <span><code>btn-text-14</code></span>
          </p>
        </div>
      </div>


      <div class="iq-ui-container">
        <div class="iq-ui-container-head">
          <h2 id="iq-ui-checkbox" class="display-2 iq-ui-container-title">
            Checkbox <code>src/assets/scss/_checkbox.scss</code>
          </h2>
        </div>
        <div class="iq-ui-container-body">
          <p>Class: <code>checkbox</code></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="checkbox checkbox-silver">
              Default
              <input type="checkbox">
              <span class="checkmark"></span>
            </label>
            <label class="checkbox checkbox-20">
              Size 20
              <input type="checkbox">
              <span class="checkmark"></span>
            </label>
            <label class="checkbox checkbox-18">
              Size 18
              <input type="checkbox">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Palette</b></p>
          <p>Class: <code>checkbox-silver</code></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="checkbox">
              Checkbox default
              <input type="checkbox">
              <span class="checkmark"></span>
            </label>
            <label class="checkbox checkbox-silver">
              Checkbox silver
              <input type="checkbox">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Without text</b></p>
          <div class="iq-ui-container-body-wrapper">
            <label for="iq-ui-container-body-wrapper1" aria-label="bodywrapper" class="checkbox checkbox-without-text">
              <input type="checkbox" id="iq-ui-container-body-wrapper1">
              <span class="checkmark"></span>
            </label>
            <label for="iq-ui-container-body-wrapper2" aria-label="bodywrapper" class="checkbox checkbox-20 checkbox-without-text">
              <input type="checkbox" id="iq-ui-container-body-wrapper2">
              <span class="checkmark"></span>
            </label>
            <label for="iq-ui-container-body-wrapper3" aria-label="bodywrapper" class="checkbox checkbox-18 checkbox-without-text">
              <input type="checkbox" id="iq-ui-container-body-wrapper3">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Disabled</b></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="checkbox">
              Default
              <input type="checkbox" disabled>
              <span class="checkmark"></span>
            </label>
            <label class="checkbox checkbox-silver">
              Silver
              <input type="checkbox" disabled>
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
      </div>


      <div class="iq-ui-container">
        <div class="iq-ui-container-head">
          <h2 id="iq-ui-radio" class="display-2 iq-ui-container-title">
            Radio <code>src/assets/scss/_radio.scss</code>
          </h2>
        </div>
        <div class="iq-ui-container-body">
          <p>Class: <code>control-radio</code></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="control-radio">
              Default
              <input type="radio" name="radio-example">
              <span class="checkmark"></span>
            </label>
            <label class="control-radio control-radio-20">
              Size 20
              <input type="radio" name="radio-example">
              <span class="checkmark"></span>
            </label>
            <label class="control-radio control-radio-18">
              Size 18
              <input type="radio" name="radio-example">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Palette</b></p>
          <p>Class: <code>control-radio-silver</code></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="control-radio">
              Default
              <input type="radio" name="radio-example">
              <span class="checkmark"></span>
            </label>
            <label class="control-radio control-radio-silver">
              Radio silver
              <input type="radio" name="radio-example">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Without text</b></p>
          <div class="iq-ui-container-body-wrapper">
            <label for="iq-ui-container-body-wrapperRadio1" aria-label="bodywrapper" class="control-radio control-radio-without-text">
              <input type="radio" name="radio-example-2" id="iq-ui-container-body-wrapperRadio1">
              <span class="checkmark"></span>
            </label>
            <label for="iq-ui-container-body-wrapperRadio2" aria-label="bodywrapper" class="control-radio control-radio-20 control-radio-without-text">
              <input type="radio" name="radio-example-2" id="iq-ui-container-body-wrapperRadio2">
              <span class="checkmark"></span>
            </label>
            <label for="iq-ui-container-body-wrapperRadio3" aria-label="bodywrapper" class="control-radio control-radio-18 control-radio-without-text">
              <input type="radio" name="radio-example-2" id="iq-ui-container-body-wrapperRadio3">
              <span class="checkmark"></span>
            </label>
          </div>
          <p><b>Disabled</b></p>
          <div class="iq-ui-container-body-wrapper">
            <label class="control-radio">
              Default
              <input type="radio" name="radio-example" disabled>
              <span class="checkmark"></span>
            </label>
            <label class="control-radio control-radio-silver">
              Silver
              <input type="radio" name="radio-example" disabled>
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
      </div>


      <div class="iq-ui-container">
        <div class="iq-ui-container-head">
          <h2 id="iq-ui-radio" class="display-2 iq-ui-container-title">
            Badge <code>src/components/badge/badge.vue; src/assets/scss/components/_badge.scss</code>
          </h2>
        </div>
        <div class="iq-ui-container-body">
          <p>Component name: <code>badge</code></p>
          <p><b>Props</b></p>
          <p>:label <i>- required.</i></p>
          <p>:imgSrc</p>
          <p>:badgeClass</p>
          <p>:badgeType <i>- types can be found in const BADGE_TYPE.</i></p>
          <p><b>Badge types</b></p>
          <div class="iq-ui-container-body-wrapper">
            <badge label="light green" :badge-type="BADGE_TYPE.LIGHT_GREEN"></badge>
            <badge label="blue" :badge-type="BADGE_TYPE.BLUE"></badge>
            <badge label="yellow" :badge-type="BADGE_TYPE.YELLOW"></badge>
            <badge label="silver" :badge-type="BADGE_TYPE.SILVER"></badge>
            <badge label="orange" :badge-type="BADGE_TYPE.ORANGE"></badge>
            <badge label="red" :badge-type="BADGE_TYPE.RED"></badge>
          </div>
        </div>
      </div>


      <div class="iq-ui-container">
        <div class="iq-ui-container-head">
          <h2 id="iq-ui-radio" class="display-2 iq-ui-container-title">
            Simple Table <code>src/components/simple-table/simple-table.vue; src/assets/scss/components/_simple-table.scss</code>
          </h2>
        </div>
        <div class="iq-ui-container-body">
          <p>Component name: <code>simple-table</code></p>
          <p><b>Props</b></p>
          <p>:columnNames <i>- An array of names for the table header columns, specified in the display order. Required props.</i></p>
          <p>:columnKeys <i>- An array of unique keys for slots, corresponding to the display order of the columns. Required props.</i></p>
          <p>:dataSource <i>- An array containing the data to be displayed in the table. Required props.</i></p>
          <p>:tableClass <i>- A class that is applied to the table element.</i></p>
          <p>:tableHeadColumnClass <i>- A class that is applied to the header columns of the table.</i></p>
          <p>:tableColumnClass <i>- A class that is applied to the body columns of the table.</i></p>
          <p><b>Details</b></p>
          <p>Each column template is passed through a slot, which receives props containing the corresponding data.</p>
          <p><b>Example</b></p>
          <div class="iq-ui-container-body-wrapper">
          <pre>
            <code>
              &lt;simple-table
                :column-names="['Isin', 'Title', 'Status', '']"
                :column-keys="['isin', 'title', 'status', 'actions']"
                :data-source="data"
              &gt;
                &lt;template v-slot:isin="props"&gt;
                  { { props.data.isin } }
                &lt;/template&gt;
                &lt;template v-slot:title="props"&gt;
                  { { props.data.name } }
                &lt;/template&gt;
                &lt;template v-slot:status="props"&gt;
                  { { props.data.state } } - { { props.data.type } }
                &lt;/template&gt;
                &lt;template v-slot:actions="props"&gt;
                  &lt;some-component :data="props.data" &gt;&lt;/some-component&gt;
                &lt;/template&gt;
              &lt;/simple-table&gt;
            </code>
          </pre>
          </div>
        </div>
      </div>
    </section>
  </content-wrapper>
</template>

<script setup>
import Badge from "@/components/badge/badge.vue";
import { BADGE_TYPE } from "@/components/badge/badge-type";

const { isAdmin, readyProject } = useProjectLang();

onMounted(() => {
  readyProject(({ isProjectReady }) => {
    if (isProjectReady && !isAdmin) {
      navigateTo("/overview")
    }
  });

  const body = document?.body;
  body?.classList?.add("bg-gray-concrete");
});

onBeforeUnmount(() => {
  const body = document?.body;
  body?.classList?.remove("bg-gray-concrete");
})
</script>

<style scoped lang="scss">
.iq-ui {
  width: 100%;
  min-height: 100vh;
  padding: 16px 0;

  code {
    font-size: 12px;
    color: #7c7c7c;
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
  }

  &-container {
    position: relative;
    margin-bottom: 20px;
    padding: 0;
    background-color: #ffffff;
    border-radius: 8px;

    &-head {
      padding: 16px 16px 0;
      background: linear-gradient(90deg, #4db935, #b6f0a9);
      border-radius: 8px 8px 0 0;
    }

    &-title {
      margin-bottom: 16px;
      padding-bottom: 8px;

      code {
        color: #ffffff;
      }
    }

    &-body {
      padding: 0 16px 16px;

      p {
        color: #000000;
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 16px;

        code {
          color: #1f2328;
          margin: 0;
          padding: .2em .4em;
          font-size: 85%;
          white-space: break-spaces;
          background-color: #afb8c133;
          border-radius: 6px;
        }
      }

      &-wrapper {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
