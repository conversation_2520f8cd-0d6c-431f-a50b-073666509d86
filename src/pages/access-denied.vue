<template>
  <div class="acess-denied-container">
    <access-denied-popup></access-denied-popup>
  </div>
</template>
<script setup>
import { onMounted, getCurrentInstance } from 'vue';
import AccessDeniedPopup from "@/components/access-denied-popup.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useNuxtApp } from '#app';

const { $tracker, $nuxt } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { projectPermissions } = useProjectLang();
const { readyProject } = useProjectLang();

onMounted(() => {
  readyProject(({ displayName, isProjectReady }) => {
    if (isProjectReady) {
      $tracker.sendEvent(
        $keys.EVENT_KEY_NAMES.VIEW_ACCESS_DENIED,
        {
          "user permissions": projectPermissions.value,
          "project name": displayName,
          "previous url": $nuxt?.context?.from?.path,
        },
        LOCAL_TRACKER_CONFIG
      );
    }
  });
});
</script>
