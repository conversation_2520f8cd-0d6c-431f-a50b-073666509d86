<template>
  <content-wrapper :is-body-loading="isPageLoading">
    <div
      v-if="!isPageLoading"
      class="dynamic-hero-quiz-sub-container main-inner-section"
    >
      <div class="dynamic-hero-quiz-top-container">
        <div
          @click="backToDynamicHeroList()"
          class="back-to-dynamic-hero-list-section"
        >
          <div class="back-arrow-image">
            <img
              alt=""
              class="back-arrow-image"
              src="~/assets/images/back-arrow.png"
            />
          </div>
          <div class="back-text" v-if="isPageOverviewVisible">
            {{ $t('OVERVIEW.BACK_TO_OVERVIEW') }}
          </div>
          <div class="back-text" v-if="!isPageOverviewVisible">
            {{ $t('SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST') }}
          </div>
        </div>
        <div class="dynamic-hero-quiz-continue-and-cancel-section">
          <div class="cancel-button">
            <button type="button" @click="backToDynamicHeroList()">
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
          <div
            v-if="!isLiveHeroReplaced"
            :class="
              quizName.trim() !== '' &&
              quizQuestionText.trim() !== '' &&
              quizResultText.trim() !== '' &&
              isCampaignModified &&
              (isCtaLinkValid || quizCTALinkText.trim() == '')
                ? 'continue-button'
                : 'disable-button continue-button'
            "
            @click="saveQuizForm()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            <button type="button" v-if="isHeroLive">{{ $t('BUTTONS.UPDATE') }}</button>
            <button type="button" v-else>{{ $t('BUTTONS.CONTINUE') }}</button>
          </div>
          <div
            v-if="isLiveHeroReplaced"
            :class="
              quizName.trim() !== '' &&
              quizQuestionText.trim() !== '' &&
              quizResultText.trim() !== '' &&
              isCampaignModified
                ? 'continue-button'
                : 'disable-button continue-button'
            "
            @click="saveReplaceForm()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            <button type="button">{{ $t('REPLACE') }}</button>
          </div>
        </div>
      </div>
      <div class="dynamic-hero-form-edit-id">ID: {{ quizUuid }}</div>
      <div class="dynamic-hero-quiz-middle-container">
        <div class="dynamic-hero-quiz-middle-section">
          <div class="dynamic-hero-quiz-name-and-calender-section">
            <div class="dynamic-hero-quiz-form-name-section">
              <div class="dynamic-hero-quiz-form-info-question-image-section">
                <img
                  alt="quiz icon"
                  class="hero-quiz-image"
                  src="~/assets/images/hero-quiz-image.png"
                />
              </div>
              <div class="dynamic-hero-quiz-form-text">{{ $t('DYNAMIC_HERO.QUIZ_FORM') }}</div>
            </div>
            <div class="dynamic-hero-quiz-form-calender-section">
              <div
                class="news-date-picker-container"
              >
                <div class="start-date-text">Start date:</div>
                  <CalendarPicker
                    v-model="scheduleDate"
                    :isRange="false"
                    :markers="markers"
                    :disabled-dates="disabledDates"
                    :isLiveHeroReplaced="isLiveHeroReplaced"
                    :isHeroLive="isHeroLive"
                    @update:model-value="handleDateChange"
                  />
              </div>
            </div>
          </div>
          <div class="dynamic-hero-quiz-name-and-schedule-section">
            <div class="dynamic-hero-quiz-name-section"
              :class="{
                'simple-data-tooltip': isQuizNameInFocus,
              }"
              :data-tooltip-text="isQuizNameInFocus && quizName"
            >
              <div v-if="quizName == ''" class="compulsory-quiz-field">*</div>
              <input
                class="quiz-name-field"
                type="text"
                ref="quizInputFieldRef"
                autocomplete="off"
                v-model.trim="quizName"
                placeholder="Quiz name in CMS"
                @mouseover="checkQuizName()"
                @keydown="hideQuizTip()"
                @mouseleave="hideQuizTip()"
                @input="hideQuizTip()"
              />
            </div>
            <div
              v-if="!isLiveHeroReplaced && !isHeroLive"
              class="dynamic-hero-quiz-schedule-section"
            >
              <div
                :class="
                  scheduleDate == ''
                    ? 'schedule-text disabled-text'
                    : 'schedule-text'
                "
              >
                Schedule
              </div>
              <div class="schedule-toggle-button">
                <label class="switch"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-edge': scheduleDate == 0,
                  }"
                  :data-tooltip-text="scheduleDate == 0 && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
                >
                  <input
                    type="checkbox"
                    :disabled="scheduleDate == '' ? true : false"
                    :checked="isQuizStatusDisplayed"
                    @click="
                      (isQuizStatusDisplayed = !isQuizStatusDisplayed), (isCampaignModified = true)
                    "
                  />
                  <span
                    :class="
                      scheduleDate == ''
                        ? 'slider-round disabled-slider'
                        : 'slider-round'
                    "
                  ></span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!isLiveHeroReplaced && !isHeroLive" class="delete-quiz-section">
          <button class="delete" type="button" @click="deletemodalpopup()">
            <img
              alt="delete icon"
              src="@/assets/images/delete-icon.png"
              width="15"
              height="16"
            />
            <span>{{ $t('DYNAMIC_HERO.DELETE_QUIZ') }}</span>
          </button>
        </div>
      </div>
      <div class="dynamic-hero-quiz-bottom-container">
        <div class="dynamic-hero-quiz-question-section">
          <div class="dynamic-hero-quiz-question-container">
            <div class="dynamic-hero-quiz-question-heading">
              <span class="heading-text">Question</span>
              <span class="compulsory-field">*</span>
            </div>
            <div v-if="!isHeroLive" class="preview-section-dynamic">
              <div class="text-section-prev">{{ $t('DYNAMIC_HERO.HERO_PREVIEW') }}</div>
              <label class="switch"
                :class="{
                  'simple-data-tooltip simple-data-tooltip-edge': !isHeroPreview
                }"
                :data-tooltip-text="!isHeroPreview && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
              >
                <input
                  @change="
                    [(isHeroPreview = !isHeroPreview), (isCampaignModified = true)]
                  "
                  :checked="isHeroPreview"
                  type="checkbox"
                />
                <span class="slider-round"></span>
              </label>
            </div>
          </div>
          <div class="dynamic-hero-quiz-question-input-section">
            <textarea
              @input="checkQuestion"
              maxlength="244"
              class="input-text-area"
              ref="quizQuestionFieldRef"
              v-model="quizQuestionText"
            ></textarea>
            <div
              v-if="quizQuestionText !== ''"
              class="question-section-word-count-section"
            >
              {{ quizQuestionText.length }}/244
            </div>
          </div>
        </div>
        <div class="dynamic-hero-quiz-result-section">
          <div class="dynamic-hero-quiz-result-head-section">
            <div class="dynamic-hero-quiz-result-heading">
              <span class="heading-text">Result:</span>
              <span class="compulsory-field">*</span>
            </div>
            <div class="dynamic-hero-quiz-result-radio-button-section">
              <div
                class="radio-button-section"
                v-for="(data, index) in quizResultList"
                :key="index"
                @click="!data.isChecked && selectResult(data, data.name)"
              >
                <div class="round">
                  <input v-if="data.isChecked" type="radio" />
                  <label for="round" aria-label="round"></label>
                </div>
                <div class="result-text">{{ data.name }}</div>
              </div>
            </div>
          </div>
          <div class="dynamic-hero-quiz-result-input-section">
            <textarea
              @input="checkResult"
              maxlength="244"
              class="input-text-area"
              ref="quizResultFieldRef"
              v-model="quizResultText"
            ></textarea>
            <div
              v-if="quizResultText !== ''"
              class="result-section-word-count-section"
            >
              {{ quizResultText.length }}/244
            </div>
          </div>
        </div>
        <div class="dynamic-hero-quiz-cta-link-section">
          <div class="dynamic-hero-quiz-cta-link-heading">
            <span class="heading-text">CTA link:</span>
          </div>
          <div class="dynamic-hero-quiz-cta-link-input-section">
            <div class="input-section">
              <input
                class="input-text-area"
                ref="quizCTALinkRef"
                v-model="quizCTALinkText"
                autocomplete="off"
                @input="validateisCTALinkInprogress"
                placeholder="Enter link"
              />
            </div>
            <div class="cta-link-input-verify-section">
              <div v-if="isCtaLinkInProgress" class="cta-link-progress-check">
                <div class="loader-image"></div>
              </div>
              <div class="cta-link-correct-check link-image-check">
                <img
                  v-if="!isCtaLinkInProgress && isCtaLinkValid"
                  class="correct-icon"
                  alt=""
                  src="@/assets/images/tick-icon.png"
                />
              </div>
              <div class="cta-link-wrong-check link-image-check">
                <img
                  v-if="!isCtaLinkInProgress && !isCtaLinkValid && isCtaLinkBroken"
                  class="wrong-icon"
                  alt="wrong"
                  src="@/assets/images/red-info.svg?skipsvgo=true"
                />
              </div>
            </div>
          </div>
          <div
            v-if="!isCtaLinkValid && isCtaLinkBroken"
            class="cta-broken-link-validation-section"
          >
            <span class="cta-link-broken-message">This link is broken.</span>
          </div>
        </div>
      </div>
    </div>
    <saveModal
      v-if="isQuizSavedAsADraft"
      :closeModal="closeModal"
      :saveAndPublishFunction="patchEditDynamicHeroDataAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your Quiz form?'"
      imageName="@/assets/images/quiz-form.png"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduleQuizPopupVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="handleHeroSchedule"
      @schedule="handleHeroSchedule"
      :PatchScheduledHero="patchEditDynamicHeroDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      :markers="markers"
      @date-click="handleDateClickPopup"
    />
    <savingModal v-show="isQuizSaving" :status="'saving'" />
    <deleteModal
      v-if="isHeroDeletePopupVisible"
      :closeModal="closeModal"
      :productInfoTitle="'Delete the Quiz?'"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="'quiz?'"
      :deleteItem="deleteEditDynamicHeroDataAsync"
      :availableLanguage="0"
    />
    <updatingLiveHero
      v-if="isLiveHeroSaveDynamicHero"
      :closeModal="closeModal"
      :callConfirm="patchEditDynamicHeroDataAsync"
      :updatingText="'Please, confirm the updating live Hero'"
    />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="closeModal"
      :closeReplacement="patchEditDynamicHeroDataAsync"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import deleteModal from "@/components/delete-modal";
import savingModal from "@/components/saving-modal";
import updatingLiveHero from "@/components/updating-live";
import deletingModal from "@/components/deleting-modal";
import saveModal from "@/components/save-modal.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useStore } from "vuex";
import { useProjectLang } from "@/composables/useProjectLang";
import { useRoute } from "vue-router";
import cancelModal from "@/components/cancel-modal";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";


const route = useRoute();
const store = useStore();
const heroData = ref([
  {template: 'quiz'}
]);
const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  deleteDynamicHeroDataAsync,
  patchDynamicHeroDataAsync
} = useDynamicHeroStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const quizInputFieldRef = ref(null);
const quizQuestionFieldRef = ref("");
const quizResultFieldRef = ref("");
const quizCTALinkRef = ref("");
const quizUuid = ref("");
const scheduleDateConfirm = ref("");
const disableList = ref([]);
const disabledDates = ref([]);
const isQuizNameInFocus = ref(false);
const quizState = ref("");
const isPageOverviewVisible = ref(false);
const isHeroPreview = ref(false);
const includeId = ref(0);
const todos = reactive([]);
const isPageLoading = ref(false);
const isQuizStatusDisplayed = ref(false);
const isConfirmModalVisible = ref(false);
const isCampaignModified = ref(false);
const isQuizSavedAsADraft = ref(false);
const quizName = ref("");
const isDeletingModalVisible = ref(false);
const scheduleDate = ref("");
const isHeroDeletePopupVisible = ref(false);
const isScheduleQuizPopupVisible = ref(false);
const quizQuestionText = ref("");
const quizResultText = ref("");
const quizCTALinkText = ref("");
const quizResultList = reactive([
  {
    value: "1",
    name: "True",
    key: "vrai",
    isChecked: false,
  },
  {
    value: "2",
    name: "False",
    key: "faux",
    isChecked: false,
  },
]);
const isQuizSaving = ref(false);
const isCtaLinkInProgress = ref(false);
const isCtaLinkValid = ref(false);
const isCtaLinkBroken = ref(false);
const isHeroLive = ref(false);
const isLiveHeroSaveDynamicHero = ref(false);
const isLiveHeroReplaced = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const defaultQuizImage = ref(
  "https://innit-web-widget.s3.us-west-2.amazonaws.com/img/quiz_hero.svg?skipsvgo=true"
);
const lang = ref("");
const isAdminCheck = ref(false);

// Composables
const { triggerLoading, routeToPage, checkDuplicate, processScheduledElement, isScheduledWithPublishDate, getDisableList, getDisabledDates, useCalendarMarkers } = useCommonUtils();
const { readyProject, isAdmin } = useProjectLang();
const { watchReactiveValue } = useWatcherUtils();
const { preventEnterAndSpaceKeyPress } = useEventUtils();

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});
const { markers } = useCalendarMarkers(disableList);
const initializeDataAsync = async () => {
  isAdminCheck.value = isAdmin.value;
  lang.value = store.getters["userData/getDefaultLang"];

  await getEditDynamicHeroDataAsync();
  await getDynamicHeroDataAsync();

  let sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isLiveHeroReplaced.value = true;
  }
  if (sourceUrl.includes("overviewQuiz")) {
    isPageOverviewVisible.value = true;
  }
  disabledDates.value = await getDisabledDates().value;
  disableList.value = await getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.push(newTodo);
  }
  includeId.value = todos.length;
};
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
};
const checkQuizName = () => {
  const nameElement = quizInputFieldRef.value;
  if (
    nameElement &&
    nameElement.scrollWidth > nameElement.clientWidth &&
    nameElement !== document.activeElement &&
    quizName.value.trim().length > 0
  ) {
    isQuizNameInFocus.value = true;
  }
};
const handleHeroSchedule = () => {
  isScheduleQuizPopupVisible.value = false;
};
const hideQuizTip = () => {
  isQuizNameInFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event && event.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getDynamicHeroDataAsync:", error);
  }
};

const getEditDynamicHeroDataAsync = async () => {
  if (!route.query[QUERY_PARAM_KEY.UUID]) {
    return;
  }

  try {
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

    await fetchDynamicHeroDataAsync();
    const response = await editDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      processHeroData(response);
      if (response.data) {
        processHeroDataDetails(response.data);
      }
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getEditDynamicHeroDataAsync:`, error);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const fetchDynamicHeroDataAsync = async () => {
  await getEditPageDynamicHeroDataAsync({
    lang: lang.value,
    uuid: route.query[QUERY_PARAM_KEY.UUID],
  });
};
const processHeroData = (response) => {
  isHeroLive.value = response?.state === 'live';
  isHeroPreview.value = response?.preview ?? false;
  quizState.value = response?.state ?? '';
  quizUuid.value = response?.uuid ?? '';
  quizName.value = response?.title ?? '';
  isQuizStatusDisplayed.value = !!response.publishDate;
  scheduleDate.value = response?.publishDate ? convertTimeStamp(response.publishDate) : '';

  if (quizState.value === 'draft' && scheduleDate.value !== '') {
    isQuizStatusDisplayed.value = false;
    scheduleDate.value = '';
  }
};
const processHeroDataDetails = (data) => {
  quizCTALinkText.value = data.ctaLink || '';
  if (quizCTALinkText.value.trim() !== '') {
    validateisCTALinkInprogress();
  }
  quizQuestionText.value = data.body || '';
  quizResultText.value = data.commentary || '';

  if (data.answer) {
    processAnswerData(data.answer);
  }
};
const processAnswerData = (answer) => {
  let normalizedAnswer = answer.toLowerCase();
  quizResultList[0].isChecked = normalizedAnswer === 'vrai';
  quizResultList[1].isChecked = normalizedAnswer !== 'vrai';
};
const deletemodalpopup = () => {
  isHeroDeletePopupVisible.value = true;
};
const handleDateChange = () => {
  isCampaignModified.value = true;
  isQuizStatusDisplayed.value = true;
};
const handleDateClickPopup = (newValue) => {
  scheduleDate.value = newValue;
};
const deleteEditDynamicHeroDataAsync = async () => {
  isDeletingModalVisible.value = true;
  try {
    await deleteDynamicHeroDataAsync({ uuid: route.query[QUERY_PARAM_KEY.UUID] });
    routeToPage('dynamic-hero');
    triggerLoading('newDeletedSuccess');
    closeModal();
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}deleteDynamicHeroDataAsync:`, error);
    closeModal();
  } finally {
    isDeletingModalVisible.value = false;
  }
};

const saveReplaceForm = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const patchEditDynamicHeroDataAsync = async () => {
  const calculatedScheduleDate = calculateScheduleDate();
  const answerData = getAnswerData();
  prepareFlagsForSaving();
  const payload = constructPayload(calculatedScheduleDate, answerData);
  cleanPayload(payload);

  try {
    await dispatchPatchDynamicHeroDataAsync(payload);
    handleAfterPatch();
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}patchDynamicHeroDataAsync:`, error);
  }
};
const calculateScheduleDate = () => {
  let scheduleDateValue = '';

  if (isLiveHeroReplaced.value || isHeroLive.value) {
    isReplacementConfirmPopupVisible.value = false;
    isQuizStatusDisplayed.value = true;
    scheduleDate.value = new Date();
    scheduleDate.value.setHours(0, 0, 0, 0);
    scheduleDateValue = Math.floor(scheduleDate.value.getTime() / 1000);
  } else {
    const date = new Date(scheduleDate.value);
    scheduleDateValue = Math.floor(date.getTime() / 1000);
  }

  return scheduleDateValue;
};
const getAnswerData = () => {
  let answerData = '';
  (quizResultList ?? []).forEach((data) => {
    if (data.isChecked) {
      switch (data.name) {
        case 'True':
          answerData = 'vrai';
          break;
        case 'False':
          answerData = 'faux';
          break;
      }
    }
  });
  return answerData;
};
const prepareFlagsForSaving = () => {
  isScheduleQuizPopupVisible.value = false;
  isQuizSavedAsADraft.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isQuizSaving.value = true;
};
const constructPayload = (scheduleDateVal, answerData) => {
  return {
    uuid: route.query[QUERY_PARAM_KEY.UUID],
    title: quizName.value ? quizName.value.trim() : '',
    template: 'quiz',
    publishDate: !scheduleDate.value || !isQuizStatusDisplayed.value ? '0' : scheduleDateVal,
    image: defaultQuizImage.value,
    data: {
      answer: answerData,
      body: quizQuestionText.value,
      ctaLink: quizCTALinkText.value.trim(),
      commentary: quizResultText.value,
    },
    state: isQuizStatusDisplayed.value && scheduleDate ? 'scheduled' : 'draft',
    preview: isHeroPreview.value,
  };
};
const cleanPayload = (payload) => {
  if (!quizCTALinkText.value.trim()) {
    delete payload.data.ctaLink;
  }

  if (!isQuizStatusDisplayed.value && !scheduleDate.value) {
    delete payload.publishDate;
  }
};
const dispatchPatchDynamicHeroDataAsync = async (payload) => {
  await patchDynamicHeroDataAsync({
    payload,
    uuid: route.query[QUERY_PARAM_KEY.UUID],
  });
};

const handleAfterPatch = () => {
  isQuizSaving.value = false;

  if (!isQuizStatusDisplayed.value || !scheduleDate.value) {
    if (!isHeroLive.value && !isLiveHeroReplaced.value) {
      triggerLoading('quizSaved');
    }
  } else if (isQuizStatusDisplayed.value && scheduleDate.value) {
    if (!isHeroLive.value && !isLiveHeroReplaced.value) {
      triggerLoading('quizScheduled');
    }
  } else if (isHeroLive.value && !isLiveHeroReplaced.value) {
    triggerLoading('contentLive');
  } else if (isLiveHeroReplaced.value) {
    triggerLoading('heroReplaced');
  }

  routeToPage('dynamic-hero');
};
const saveQuizForm = () => {
  if (isHeroLive.value) {
    isLiveHeroSaveDynamicHero.value = true;
    return;
  }
  if (scheduleDate.value && isQuizStatusDisplayed.value) {
    isScheduleQuizPopupVisible.value = true;
    scheduleDateConfirm.value = scheduleDate.value;
  } else {
    isQuizSavedAsADraft.value = true;
  }
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);

  if (isPageOverviewVisible.value) {
    routeToPage('overview');
  } else {
    routeToPage('dynamic-hero');
  }
};

const selectResult = (data, name) => {
  quizResultList.forEach((element) => {
    element.isChecked = false;
  });
  data.isChecked = true;
  isCampaignModified.value = true;
};
const validateisCTALinkInprogress = () => {
  if (quizCTALinkText.value.trim() === '') {
    isCtaLinkInProgress.value = false;
    isCtaLinkValid.value = false;
    isCtaLinkBroken.value = false;
  } else {
    isCtaLinkInProgress.value = true;
    checkCTALink();
  }
};
const checkCTALink = () => {
  const urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/;
  const linkInput = quizCTALinkText.value.trim();

  if (urlPattern.test(linkInput) || (rocheAppPattern.test(linkInput) && linkInput !== '')) {
    isCtaLinkValid.value = true;
  } else {
    isCtaLinkBroken.value = true;
    isCtaLinkValid.value = false;
  }

  setTimeout(() => {
    isCtaLinkInProgress.value = false;
  }, 1000);
};
const checkQuestion = () => {
  if (quizQuestionText.value.length === 1 && quizQuestionText.value[0] === ' ') {
    quizQuestionText.value = '';
  } else {
    quizQuestionText.value = quizQuestionText.value.replace(/\s+/g, ' ');
  }
};
const checkResult = () => {
  if (quizResultText.value.length === 1 && quizResultText.value[0] === ' ') {
    quizResultText.value = '';
  } else {
    quizResultText.value = quizResultText.value.replace(/\s+/g, ' ');
  }
};
const handleTypeInput = (event) => {
  if (quizInputFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizQuestionFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizResultFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizCTALinkRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
};
const closeModal = () => {
  scheduleDateConfirm.value = scheduleDate.value;
  if (!isLiveHeroReplaced.value) {
    isLiveHeroReplaced.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isHeroDeletePopupVisible.value = false;
  isQuizSavedAsADraft.value = false;
  isConfirmModalVisible.value = false;
  isScheduleQuizPopupVisible.value = false;
  isQuizSaving.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isDeletingModalVisible.value = false;
};
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
</script>
