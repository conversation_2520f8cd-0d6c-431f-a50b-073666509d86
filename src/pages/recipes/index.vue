<template>
  <client-only>
    <content-wrapper :is-body-loading="initialLoading" wrapperClasses="recipes-page font-family-averta">
      <template v-slot:title>
        <span>{{ $t('COMMON.RECIPES') }}</span>
      </template>

      <block-wrapper is-transparent>
        <recipesCount />
      </block-wrapper>

      <block-wrapper is-transparent :class="{ 'recipes-block-wrapper-with-select-all': showSelectAllBlock }">
        <div class="recipes-filter-wrapper">
          <h4 class="recipes-sub-title display-4 color-gray-tundora">
            {{ isSearchEnabled ? $t('COMMON.SEARCH_RESULT') : $t('MASTER_LIST') }}
          </h4>
          <div v-if="!showSelectAllBlock" class="recipes-filter-wrapper-body">
            <div class="recipes-filter-wrapper-body-filters">
              <div class="font-size-base font-bold color-slate-gray" data-test-id="recipe-filter">{{ $t('COMMON.FILTER') }}</div>
              <filter-select
                :query-param="FILTER_STATUS_QUERY_PARAM"
                :label="$t('COMMON.FILTER_STATUS')"
                :options="FILTER_STATUS_OPTIONS"
                :default-option="FILTER_STATUS_OPTIONS[0]"
              />

              <filter-select
                :query-param="FILTER_CREATED_BY_QUERY_PARAM"
                :label="$t('CREATED_BY')"
                :options="FILTER_CREATED_BY_OPTIONS"
                :default-option="FILTER_CREATED_BY_OPTIONS[0]"
              />
            </div>

            <NuxtLink
              to="/recipe-detail"
              class="btn-green"
              data-test-id="add-recipe-button"
            >+ &nbsp;{{ $t('BUTTONS.ADD_RECIPE') }}</NuxtLink>
          </div>
        </div>
      </block-wrapper>

      <block-wrapper is-transparent :is-loading="tm_isLoading">

        <div v-if="showSelectButton && !showSelectAllBlock" class="recipes-block-select w-100">
          <button
            type="button"
            class="btn-green-text"
            @click="toggleSelectAllBlock"
          >
            {{ $t('COMMON.SELECT') }}
          </button>
        </div>

        <simple-sticky-wrapper v-if="showSelectAllBlock" :top="60" :distance="150">
          <div class="recipes-select-all-block">
            <div>
              <label for="checkbox-select-all" class="checkbox">
                <b class="color-black">{{ $t("PAGE.RECIPES.SELECT_ALL") }}</b>
                <input
                  ref="checkboxSelectAllRef"
                  id="checkbox-select-all"
                  type="checkbox"
                  @change="selectAllRecipes"
                >
                <span class="checkmark"></span>
              </label>

              <div class="recipes-select-count-block">
                <span class="font-size-base font-normal color-black">{{ checkedRecipesModel.length }} {{ $t("PAGE.RECIPES.SELECTED") }}</span>
                <button
                  v-if="checkedRecipesModel.length"
                  type="button"
                  class="btn-reset"
                  @click="resetSelectedAllRecipes"
                >
                  <img alt="close green" src="@/assets/images/close.svg?skipsvgo=true" width="12" height="12"/>
                </button>
              </div>
            </div>

            <div>
              <button
                type="button"
                class="btn-green-outline"
                :disabled="!checkedRecipesModel.length"
                @click="openRecipesScheduleModal"
              >
                {{ $t('BUTTONS.SCHEDULE') }}
              </button>

              <button
                type="button"
                class="btn-green"
                :disabled="!checkedRecipesModel.length"
                @click="openRecipesPublishModal"
              >
                {{ $t('BUTTONS.PUBLISH_BUTTON') }}
              </button>

              <button
                type="button"
                class="btn-green-text"
                @click="toggleSelectAllBlock"
              >
                {{ $t('BUTTONS.CANCEL_BUTTON') }}
              </button>
            </div>
          </div>
        </simple-sticky-wrapper>

        <simple-table
          v-if="rows.length"
          :column-names="columnNames"
          :column-keys="columnKeys"
          :data-source="rows"
          table-class="recipes-table"
          @row-click="tableRowClickAction"
        >
          <template v-if="showSelectColumn" v-slot:select="props">
            <div
              :class="{
                  'simple-data-tooltip': !props.data?.image || props.data.schedule
                }"
              :data-tooltip-text="getCheckboxTooltipText(props.data)"
            >
              <label :for="`recipe-checkbox-${props.data?.isin}`" class="checkbox checkbox-without-text" @click.stop>
                <input
                  :id="`recipe-checkbox-${props.data?.isin}`"
                  type="checkbox"
                  :disabled="!props.data?.image || props.data.schedule"
                  :value="props.data?.isin"
                  v-model="checkedRecipesModel"
                  @click.stop
                >
                <span class="checkmark" @click.stop></span>
              </label>
            </div>
          </template>

          <template v-slot:image="props">
            <img
              :src="props.data?.image || defaultImage"
              :src-placeholder="defaultImage"
              :alt="props.data?.title"
              class="simple-table-img"
              @error="$event.target.src = defaultImage"
            />
          </template>

          <template v-slot:isin="props">
            <span class="font-size-14 font-normal color-black">{{ props.data?.isin }}</span>
          </template>

          <template v-slot:title="props">
            <div class="recipes-title-wrapper">
              <recipeTypeBadge
                :is-new-format="true"
                :generatorPromptData="$keys.KEY_NAMES.RECIPE_LAYOUT"
                :recipeData="{}"
                :recipe-provider="props.data?.provider"
              />
              <div
                class="recipes-title font-size-14 font-bold color-black"
                :class="{
                'simple-data-tooltip': props.data?.hasTitleTooltip
              }"
                :data-tooltip-text="props.data?.title"
              >
                <p>{{ props.data?.title }}</p>
              </div>
            </div>
          </template>

          <template v-if="finalAvailableLangs?.length > 1" v-slot:variants="props">
            <span v-if="props.data?.langs.length > 1" class="font-size-14 font-normal color-stone-gray">{{ props.data?.langs.length - 1 }}</span>
          </template>

          <template v-slot:modified="props">
            <span class="font-size-14 font-normal color-stone-gray">
              {{ props.data?.lastMod ? formatJsonTimestamp(props.data?.lastMod) : "" }}
            </span>
          </template>

          <template v-slot:externalId="props">
            <span class="font-size-14 font-normal color-stone-gray">
              {{ props.data?.externalId }}
            </span>
          </template>

          <template v-slot:status="props">
            <badge
              :label="$t(props.data?.badge.tKey)"
              :badge-type="props.data?.badge.badgeType"
              :img-src="props.data?.badge.icon"
            />
            <badge
              v-if="props.data?.schedule"
              class="simple-data-tooltip"
              :class="{
                'cursor-pointer': props.data?.schedule?.errorCode
              }"
              label=""
              :badge-type="BADGE_TYPE.RED"
              :img-src="!props.data?.schedule?.errorCode ? redClockIcon : redInfoIcon"
              :only-icon="true"
              :data-tooltip-text="
                !props.data?.schedule?.errorCode
                ? `${$t('PAGE.RECIPES.TOOLTIP.TEXT_SCHEDULED_PUBLISHED_DATES')} \n ${formatScheduleDateRange(props.data?.schedule?.publishDate, props.data?.schedule?.endDate)}`
                : `${getErrorMessage(props.data?.schedule?.errorCode)}. \n ${$t('PAGE.RECIPES.TOOLTIP.TEXT_MORE_DETAILS')}`
              "
              @click="openTechnicalIssueModal(props.data?.schedule?.errorCode)"
            />
          </template>

          <template v-slot:actions="props">
            <body-menu :actions="getBodyMenuAction(props.data)" @call-actions="bodyMenuAction" />
          </template>
        </simple-table>

        <simple-paginate
          :is-pagination-enabled="isPaginationShowing"
          :pagination-total="tm_pagination.total"
          :pagination-size="tm_pagination.size"
        />

        <noResultFound
          v-if="!rows.length && tm_isFirstLoadCompleted"
          :isReloadRequired="false"
          :isContentSearched="false"
          :noResultText="$t('NO_RESULT.RECIPE')">
        </noResultFound>
      </block-wrapper>

    </content-wrapper>
  </client-only>
</template>

<script setup>
import ContentWrapper from "../../components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "../../components/block-wrapper/block-wrapper.vue";
import RecipesCount from "../../components/recipes-count.vue";
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import SimpleTable from "../../components/simple-table/simple-table.vue";
import { useTableManager } from "../../composables/useTableManager.js";
import defaultImage from "~/assets/images/default_recipe_image.png";
import { getTableColumnKeys, getTableColumnNames, RecipesModel } from "../../models/recipes.model.js";
import SimplePaginate from "../../components/simple-paginate.vue";
import Badge from "../../components/badge/badge.vue";
import redClockIcon from "@/assets/images/red-clock.png";
import redInfoIcon from "@/assets/images/red-info.svg?skipsvgo=true";
import { BADGE_TYPE } from "../../components/badge/badge-type.js";
import RecipeTypeBadge from "../../components/pages/recipe-detail/recipe-type-badge.vue";
import { useRecipesStore } from "../../stores/recipes.js";
import RecipeTechnicalIssueModal from "../../components/pages/recipes/recipe-technical-issue-modal.vue";
import BodyMenu from "../../components/body-menu.vue";
import { useDynamicHeroStore } from "../../stores/dynamic-hero.js";
import { useStore } from "vuex";
import NoResultFound from "../../components/no-result-found.vue";
import { LOCAL_TRACKER_CONFIG } from "../../сonstants/trackerConfig.js";
import { useSearchStore } from "../../stores/search.js";
import SimpleStickyWrapper from "../../components/simple-sticky-wrapper.vue";
import ProcessModal from "../../components/modals/process-modal.vue";
import ConfirmModal from "../../components/modals/confirm-modal.vue";
import FilterSelect from "../../components/filters/filter-select.vue";
import { useRoute, useRouter } from "vue-router";
import { QUERY_PARAM_KEY } from "../../сonstants/query-param-key.js";
import RecipePreviewDetailModal from "../../components/pages/recipes/recipe-preview-detail-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../../models/confirm-modal.model.js";
import { PROCESS_MODAL_TYPE } from "../../models/process-modal.model.js";
import { useSimpleCustomFetch } from "../../composables/useCustomFetch.js";
import RecipesSchedulePublishModal from "../../components/pages/recipes/recipes-schedule-publish-modal.vue";

const BODY_MENU_ACTION_CASE = {
  PREVIEW: "preview",
  EDIT: "edit",
  DELETE: "delete",
};

const {
  $keys,
  $tracker,
  $eventBus,
} = useNuxtApp();
const router = useRouter();
const route = useRoute();
const {
  readyProject,
  getProject,
} = useProjectLang();
const { t } = useI18n();
const store = useStore();
const {

  formatJsonTimestamp,
  convertToTimestamp,
  formatDateToReadableString,
  formatScheduleDateRange,
} = useTimeUtils();
const { delay } = useDelayTimer();
const {
  getDynamicHeroListDataAsync,
  disabledArticleDataListGetter,
} = useDynamicHeroStore();
const { getSchedulesRecipesAsync } = useRecipesStore();
const { isSearchEnabled } = useSearchStore();
const { triggerLoading } = useCommonUtils();

const checkboxSelectAllRef = ref(null);
const initialLoading = ref(true);
const recipeScheduleList = ref([]);
const showSelectButton = ref(false);
const showSelectAllBlock = ref(false);
const showSelectColumn = ref(false);
const checkedRecipesModel = ref([]);
const recipesSelectedMaxLength = ref(0);

const FILTER_STATUS_QUERY_PARAM = QUERY_PARAM_KEY.STATUS;
const FILTER_STATUS_OPTIONS = reactive([
  {
    label: 'All',
    value: 'all',
    hiddenValue: { excludingState: 'pending' },
  },
  {
    label: 'Published',
    value: 'published',
    hiddenValue: {
      states: 'published,readyToPublish',
      statuses: 'active',
    },
  },
  {
    label: 'Unpublished',
    value: 'unpublished',
    hiddenValue: {
      states: 'published,readyToPublish',
      statuses: 'hidden',
    },
  },
  {
    label: 'Updating',
    value: 'updating',
    hiddenValue: { states: 'publishing' },
  },
]);

const FILTER_CREATED_BY_QUERY_PARAM = QUERY_PARAM_KEY.CREATED_BY;
const FILTER_CREATED_BY_OPTIONS = reactive([
  {
    label: t("ALL"),
    value: 'all',
    hiddenValue: '',
  },
  {
    label: t("GENERATOR.GENERATOR_TEXT"),
    value: 'generator_text',
    hiddenValue: 'cmsAI,cmsAiBatch',
  },
  {
    label: t("IMPORT_RECIPE.IMPORT_RECIPE_HEADER"),
    value: 'import_recipe_header',
    hiddenValue: 'cmsImported',
  },
  {
    label: t("USER"),
    value: 'user',
    hiddenValue: 'cmsManual,fimsLite,heb,nestle',
  },
  {
    label: t("INNIT"),
    value: 'innit',
    hiddenValue: 'fims,fims3P,lpn,jdf,you,mar,floco'
  },
]);

const {
  tm_rows,
  tm_pagination,
  tm_isLoading,
  tm_isFirstLoadCompleted,
  tm_lang,
  tm_fetch,
} = useTableManager({
  storeId: "recipes",
  clientKey: "icl",
  endpointKey: "getRecipeList",
  defaultParams: {
    lang: undefined,
  },
  afterMapParamsFn: (params) => {
    const statusQueryParam = route.query?.[FILTER_STATUS_QUERY_PARAM];
    const createdByQueryParam = route.query?.[FILTER_CREATED_BY_QUERY_PARAM];
    const country = tm_lang.value.split('-')[1];
    const status = FILTER_STATUS_OPTIONS?.find((item) => item.value === statusQueryParam)?.hiddenValue;
    const providers = FILTER_CREATED_BY_OPTIONS.find((item) => item.value === createdByQueryParam)?.hiddenValue;

    return {
      ...params,
      ...status,
      country,
      providers,
    };
  },
  smoothUpdate: true,
  resultModel: (data) => RecipesModel(data, tm_lang.value),
});
const { openModal, closeModal } = useBaseModal({
  "RecipeTechnicalIssueModal": {
    component: RecipeTechnicalIssueModal,
    hideCloseBtn: false,
  },
  "RecipeProcessModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
  "RecipeConfirmModal": ConfirmModal,
  "RecipePreviewDetailModal": {
    component: RecipePreviewDetailModal,
    hideCloseBtn: false,
    modalWrapperClass: "recipe-preview-detail-modal-wrapper",
    skipClickOutside: true,
  },
  "RecipesSchedulePublishModal": {
    component: RecipesSchedulePublishModal,
    hideCloseBtn: false,
    skipClickOutside: true,
  }
});

const finalAvailableLangs = computed(() => store.getters["userData/getAvailableLangs"]);

const columnNames = computed(() => getTableColumnNames({
  showSelectColumn: showSelectColumn.value,
  finalAvailableLangs: finalAvailableLangs.value,
  t,
}));

const columnKeys = computed(() => getTableColumnKeys({
  showSelectColumn: showSelectColumn.value,
  finalAvailableLangs: finalAvailableLangs.value
}));

const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));
const rows = computed(() => {
  const baseRows = tm_rows.value ?? [];
  const scheduleList = recipeScheduleList.value ?? [];
  const disabledIsins = disabledArticleDataListGetter.value ?? [];

  if (!baseRows.length) {
    return [];
  }

  return baseRows.map((row) => {
    const matchedSchedule = scheduleList.find(item => item.isin === row.isin);
    const usedInHero = disabledIsins.includes(row.isin);
    return {
      ...row,
      schedule: matchedSchedule,
      usedInHero,
    };
  });
});

const tableRowClickAction = (data) => {
  if (showSelectAllBlock.value && data?.isin) {
    if (checkedRecipesModel.value.includes(data.isin)) {
      checkedRecipesModel.value = checkedRecipesModel.value?.filter((item) => item !== data.isin);
    } else {
      checkedRecipesModel.value = [data.isin, ...checkedRecipesModel.value];
    }
  }
};

const toggleSelectAllBlock = () => {
  showSelectAllBlock.value = !showSelectAllBlock.value;
  showSelectColumn.value = !showSelectColumn.value;
  checkedRecipesModel.value = [];
};

const getCheckboxTooltipText = (item) => {
  if (item?.schedule) {
    return t('PAGE.RECIPES.TOOLTIP.TEXT_RECIPE_SCHEDULED');
  } else if (!item?.image) {
    return t('PAGE.RECIPES.TOOLTIP.TEXT_UNABLE_PUBLISH');
  }

  return "";
};

const getErrorMessage = (errorCode) => {
  if (errorCode === $keys.KEY_NAMES.PUBLISH_FAILED) {
    return t("TECHNICAL_ISSUES.PUBLISH_TIMEOUT");
  } else if (errorCode === $keys.KEY_NAMES.UNPUBLISH_FAILED) {
    return t("TECHNICAL_ISSUES.UNPUBLISH_TIMEOUT");
  }
};

const toggleSelectAllCheckbox = (val) => {
  if (checkboxSelectAllRef.value) {
    checkboxSelectAllRef.value.checked = val;
  }
};

const resetSelectedAllRecipes = () => {
  toggleSelectAllCheckbox(false);
  selectAllRecipes(undefined);
};

const selectAllRecipes = (event) => {
  if (!event?.target?.checked) {
    checkedRecipesModel.value = [];
    recipesSelectedMaxLength.value = 0;
    return;
  }

  const selectedIsinArr = rows?.value.filter((item) => !item?.schedule && item?.image).map((item) => item?.isin);
  checkedRecipesModel.value = selectedIsinArr;
  recipesSelectedMaxLength.value = selectedIsinArr.length;
};

const resetFiltersAndQueries = () => {
  router.push({ path: "recipes" })?.catch();
};

const openTechnicalIssueModal = (errorCode) => {
  if (errorCode) {
    openModal({
      name: "RecipeTechnicalIssueModal",
      props: {
        hasUnpulishedDateTimeout: errorCode === $keys.KEY_NAMES.UNPUBLISH_FAILED,
        hasPulishedDateTimeout: errorCode === $keys.KEY_NAMES.PUBLISH_FAILED
      },
    });
  }
};

const getBodyMenuAction = (item) => {
  const { isin, state, usedInHero } = item;
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [BODY_MENU_ACTION_CASE.PREVIEW, isin],
      label: t('BUTTONS.PREVIEW_BUTTON'),
    },
    {
      isDisable: false,
      isInactive: state === $keys.KEY_NAMES.PUBLISHING,
      key: [BODY_MENU_ACTION_CASE.EDIT, isin],
      label: t('BUTTONS.EDIT_BUTTON'),
      tooltip: state === $keys.KEY_NAMES.PUBLISHING ? t('COMMON.CANNOT_EDIT_WHILE_UPDATING') : "",
    },
    {
      isDisable: false,
      isInactive: state === $keys.KEY_NAMES.PUBLISHING || usedInHero,
      key: [BODY_MENU_ACTION_CASE.DELETE, isin],
      label: t('BUTTONS.DELETE_BUTTON'),
      tooltip: usedInHero && t('COMMON.RECIPE_IN_HERO'),
      tooltipPosition: "edge",
    },
  ];
};

const deleteRecipeAsync = async (isin) => {
  const runTriggerAsync = async (key) => {
    await tm_fetch({});
    closeModal("RecipeProcessModal");
    triggerLoading(key);
  };

  try {
    openModal({ name: "RecipeProcessModal", props: { modalType: PROCESS_MODAL_TYPE.DELETING } });

    await useSimpleCustomFetch("", { method: "DELETE" }, "flite", "getRecipeSchedule", isin);
    await useSimpleCustomFetch("", { method: "DELETE" }, "flite", "deleteRecipe", isin, "{isin}");

    await runTriggerAsync($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (e) {
    console.error(`[IQ][Recipe page] Cannot delete recipe, isin: ${isin}.`, e);
    await runTriggerAsync($keys.KEY_NAMES.SOMETHING_WENT_WRONG);
  } finally {
    triggerLoading($keys.KEY_NAMES.RECIPE_COUNT_UPDATE);
  }
};

const bodyMenuAction = ([key, isin]) => {
  switch (key) {
    case BODY_MENU_ACTION_CASE.PREVIEW: {
      openModal({
        name: "RecipePreviewDetailModal",
        props: {
          recipeIsin: isin,
          checkRecipePreviewVideo: () => {},
        },
      });
      return;
    }
    case BODY_MENU_ACTION_CASE.EDIT: {
      const row = rows.value.find((item) => item.isin === isin);
      const pageQueryParam = route.query?.[QUERY_PARAM_KEY.PAGE];
      const searchQueryParam = route.query?.[QUERY_PARAM_KEY.SEARCH];
      const createdByQueryParam = route.query?.[FILTER_CREATED_BY_QUERY_PARAM];
      const statusQueryParam = route.query?.[FILTER_STATUS_QUERY_PARAM];
      router.push({
        path: "recipe-detail",
        query: {
          ...(pageQueryParam && { [QUERY_PARAM_KEY.FROM]: pageQueryParam }),
          ...(searchQueryParam && { editRecipeQuery: searchQueryParam }),
          ...(createdByQueryParam && { [FILTER_CREATED_BY_QUERY_PARAM]: createdByQueryParam }),
          ...(statusQueryParam && { [FILTER_STATUS_QUERY_PARAM]: statusQueryParam }),
          [QUERY_PARAM_KEY.ISIN]: isin,
          [QUERY_PARAM_KEY.IS_IN_HERO]: row?.usedInHero || false,
        }
      })?.catch();
      return;
    }
    case BODY_MENU_ACTION_CASE.DELETE: {
      openModal({
        name: "RecipeConfirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.DELETE,
          title: "Delete Recipe?",
          description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} recipe?`,
        },
        onClose: (response) => response && deleteRecipeAsync(isin),
      });
    }
  }
};

const scheduleSelectedRecipesAsync = async ({ publishDate, endDate }) => {
  try {
    openModal({ name: "RecipeProcessModal", props: { modalType: PROCESS_MODAL_TYPE.SCHEDULING } });
    const body = {
      isins: checkedRecipesModel.value,
      publishDate,
      endDate,
    };
    await useSimpleCustomFetch("", { method: "POST", body }, "flite", "getRecipeSchedule");
    triggerLoading("scheduledSucess", checkedRecipesModel.value.length);
  } catch (e) {
    console.error(`[IQ][Recipe page] Cannot schedule selected recipes. Isins: ${checkedRecipesModel.value}.`, e);
    triggerLoading("somethingWentWrong");
  } finally {
    toggleSelectAllBlock();
    resetFiltersAndQueries();
    closeModal("RecipeProcessModal");
  }
};

const openRecipesScheduleModal = () => {
  openModal({
    name: "RecipesSchedulePublishModal",
    props: {
      isSchedule: true,
      rows: rows.value?.filter((item) => checkedRecipesModel.value?.includes(item.isin)) ?? [],
    },
    onClose: (data) => data && scheduleSelectedRecipesAsync(data),
  });
};

const publishedRecipeAsync = async () => {
  try {
    openModal({ name: "RecipeProcessModal", props: { modalType: PROCESS_MODAL_TYPE.PUBLISHING } });
    const selectedRows = rows.value?.filter((item) => checkedRecipesModel.value?.includes(item.isin) && item.status === 'hidden') ?? [];
    for (const item of selectedRows) {
      await useSimpleCustomFetch("", { method: "POST", body: {} }, "icl", "publishRecipe", item.isin, "{isin}");
    }
    await delay(1000);
    triggerLoading($keys.KEY_NAMES.RECIPE_PUBLISHED, checkedRecipesModel.value.length);
  } catch (e) {
    console.error(`[IQ][Recipe page] Cannot publish selected recipes. Isins: ${checkedRecipesModel.value}.`, e);
    triggerLoading("somethingWentWrong");
  } finally {
    toggleSelectAllBlock();
    resetFiltersAndQueries();
    closeModal("RecipeProcessModal");
  }
};

const openRecipesPublishModal = () => {
  openModal({
    name: "RecipesSchedulePublishModal",
    props: {
      isSchedule: false,
      rows: rows.value?.filter((item) => checkedRecipesModel.value?.includes(item.isin)) ?? [],
    },
    onClose: (data) => data && publishedRecipeAsync(),
  });
};

watch(() => route.query[FILTER_STATUS_QUERY_PARAM], (val) => showSelectButton.value = val === 'unpublished', { immediate: true });
watch(checkedRecipesModel, (val) => {
  const length = val?.length;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, !!length);
  toggleSelectAllCheckbox(length !== 0 && length === recipesSelectedMaxLength.value);
});

onMounted(() => {
  readyProject(async ({ isProjectReady, id }) => {
    if (isProjectReady) {
      recipeScheduleList.value = await getSchedulesRecipesAsync();

      if (id === 'roche_main') {
        await getDynamicHeroListDataAsync({ lang: tm_lang.value });
      }

      initialLoading.value = false;

      $tracker.sendEvent(
        $keys.EVENT_KEY_NAMES.VIEW_RECIPES,
        {},
        { ...LOCAL_TRACKER_CONFIG }
      );

      $eventBus.on($keys.KEY_NAMES.UPDATE_RECIPE_LIST, () => {
        resetFiltersAndQueries();
      });

      $eventBus.on($keys.KEY_NAMES.FETCH_RECIPE_DETAILS, async () => {
        await tm_fetch({});
      });
    }
  });
});

onBeforeUnmount(() => {
  $eventBus.off($keys.KEY_NAMES.UPDATE_RECIPE_LIST);
  $eventBus.off($keys.KEY_NAMES.FETCH_RECIPE_DETAILS);
});
</script>
