<template>
  <client-only>
    <content-wrapper class="main-section" :is-body-loading="isloading">
      <div class="main-inner-section">
        <DynamicHeroHead
          :isCreateNewVisible="isCreateNewVisible"
          :isloading="isloading"
          :isSelected="isSelected"
          :hasReloadTextCheck="hasReloadTextCheck"
          :openSavePopup="openSavePopup"
          :backBtn="backBtn"
          :openNewHeroPopup="openNewHeroPopup"
        />
        <div
          v-if="!isloading && dynamicHeroList?.length"
          class="dynamic-hero-table-main-container"
        >
          <DynamicHeroTableList
            :isCreateNewVisible="isCreateNewVisible"
            :dynamicHeroList="dynamicHeroList"
            :getTime="getTime"
            :defaultImage="defaultImage"
            :selectLiveHeroOnSelect="selectLiveHeroOnSelect"
            :selectLiveHeroOnSelected="selectLiveHeroOnSelected"
            :openNewHeroPopup="openNewHeroPopup"
            :displayOption="displayOption"
            :navigateToEditPage="navigateToEditPage"
            :navigateToSelectLiveHero="navigateToSelectLiveHero"
            :navigateToCreateDuplicate="navigateToCreateDuplicate"
            :unScheduleHeroes="unScheduleHeroes"
            :deletePopUpOpen="deletePopUpOpen"
            :scheduleHeroes="scheduleHeroes"
            :expiredHeroCount="expiredHeroCount"
            :toggleText="toggleText"
            :isShowList="isShowList"
            :isSelected="isSelected"
          />
        </div>
        <DynamicHeroSelectType
          :isVisible="isDynamicHeroModalVisible"
          :leftColumnTypes="leftColumnTypes"
          :rightColumnTypes="rightColumnTypes"
          v-model="selectedDynamicType"
          @close="closeModal"
          @closeSelectDynamicHero="closeSelectDynamicHero"
          :selectedDynamicOption="selectedDynamicOption"
        />
        <replacementModal
          v-if="isReplacementConfirmPopupVisible"
          :closeModal="closeModal"
          :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
          :saveReplacement="saveSelectedLiveHero"
          :closeReplacement="closeReplacementConfirmPopup"
        />
        <DynamicHeroUnscheduleModal
          :isVisible="isUnscheduleDynamicHeroVisible"
          :dataForSelectedHero="dataForSelectedHero"
          @close="closeModal"
          :unpatchScheduledHero="unpatchScheduledHero"
        />
        <DynamicHeroScheduleModal
          :isVisible="isScheduleDynamicHeroVisible"
          :heroData="dataForSelectedHero"
          :isDynamicMasterPage="true"
          :disabledDates="disabledDates"
          @close="closeModal"
          @schedule="closeModal"
          @date-click="handleDateClick"
          :markers="markers"
          :PatchScheduledHero="PatchScheduledHero"
          :selectedDateValue="scheduleDateConfirm"
          :isScheduleDateSelected="isScheduleDateSelected"
        />
        <unableToContentModal
          v-if="isUnableToReplace"
          :closeModal="closeModal"
          :text="'Unable to replace live hero.'"
        />
        <savingModal
          v-if="isSchedulingUnschedulingPopup"
          :status="actionStatus"
        ></savingModal>
        <deleteModal
          v-if="isHeroDeletePopupVisible"
          :closeModal="closeModal"
          :productInfoTitle="'Delete the hero?'"
          :productDescriptionOne="'Are you sure you want to delete '"
          :productDescriptionTwo="'this hero?'"
          :deleteItem="deleteHeroAsync"
          :availableLanguage="0"
          :buttonText="'Delete'"
        />
        <deletingModal v-show="isDeletingModalVisible" />
        <cancelModal
          v-if="isConfirmModalVisible"
          :availableLang="[]"
          :isCampaignModifiedFromShoppableReview="false"
          :callConfirm="closeModal"
          :closeModal="closeConfirmModal"
        />
      </div>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance, watch } from 'vue';
import { useNuxtApp } from "#app";
import { useRouter } from "vue-router";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import replacementModal from "@/components/confirm-replacement-modal";
import deleteModal from "@/components/delete-modal";
import cancelModal from "@/components/cancel-modal";
import deletingModal from "@/components/deleting-modal";
import unableToContentModal from "@/components/unable-to-content-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import defaultImage from "~/assets/images/default_recipe_image.png";
import { useCommonUtils } from '~/composables/useCommonUtils';
import { useProjectLang } from "@/composables/useProjectLang";
import { useStore } from 'vuex';
import { useTimeUtils } from '../composables/useTimeUtils';
import DynamicHeroHead from '../components/pages/dynamic-hero/dynamic-hero-header.vue'
import DynamicHeroTableList from '../components/pages/dynamic-hero/dynamic-hero-table-list.vue';
import DynamicHeroSelectType from '../components/pages/dynamic-hero/dynamic-hero-select-type.vue';
import DynamicHeroUnscheduleModal from '../components/pages/dynamic-hero/dynamic-hero-unschedule-modal.vue';
import DynamicHeroScheduleModal from '../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue';
import { useI18n } from 'vue-i18n';

const instance = getCurrentInstance();
const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  deleteDynamicHeroDataAsync,
  patchDynamicHeroDataAsync,
} = useDynamicHeroStore();
const actionStatus = ref('');
const { t } = useI18n();
const $keys = instance.appContext.config.globalProperties.$keys;
const router = useRouter();
const { triggerLoading, useCalendarMarkers } = useCommonUtils();
const { readyProject } = useProjectLang();
const { formatDate } = useTimeUtils();
const store = useStore();
const { $eventBus } = useNuxtApp();
const disableList = ref([]);
const isloading = ref(false);
const incId = ref(0);
const todos = ref([]);
const disabledDates = ref([]);
const isDynamicHeroModalVisible = ref(false);
const uuidForSelectedHero = ref("");
const selectDynamicTypes = ref([
  { key: "quiz", options: "Quiz" },
  { key: "news", options: "News" },
  { key: "event", options: "Event" },
  { key: "content", options: "Content" },
  { key: "advice", options: "Advice" }
]);
const selectedDynamicType = ref("quiz");
const scheduleDateConfirm = ref('');
const hasReloadTextCheck = ref(false);
const isShowList = ref(false);
const isCreateNewVisible = ref(false);
const isSelectBtnVisible = ref(false);
const isCreateNewBtnVisible = ref(false);
const isSelected = ref(false);
const isSelectLiveHero = ref(false);
const isDeletingModalVisible = ref(false);
const targetElement = ref(null);
const dynamicHeroList = ref([]);
const isHeroDeletePopupVisible = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const dropdownItem = reactive([]);
const dataForSelectedHero = ref({});
const selectedDate = ref(0);
const isScheduleDynamicHeroVisible = ref(false);
const isUnscheduleDynamicHeroVisible = ref(false);
const stateType = ref("");
const isUnableToReplace = ref(false);
const isConfirmModalVisible = ref(false);
const expiredHeroCount = ref(0);
const isSchedulingUnschedulingPopup = ref(false);
const lang = ref("");
const isAdminCheck = ref(false);
const isScheduleDateSelected = ref(false);
const leftColumnTypes = computed(() =>
  selectDynamicTypes.value.slice(0, Math.ceil(selectDynamicTypes.value.length / 2))
);
const rightColumnTypes = computed(() =>
  selectDynamicTypes.value.slice(Math.ceil(selectDynamicTypes.value.length / 2))
);

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
      registerRootListeners();
    }
  });
});

const getActionStatusText = (state) => {
  return state === 'scheduled' ? 'scheduling' : 'unscheduling';
};

const initializeDataAsync = async () => {
  isAdminCheck.value = store.state.userData.isAdmin;
  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value],
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  document.addEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
};
const registerRootListeners = () => {
  $eventBus.on("current-link", (link) => {
    if (link.includes("/dynamic-hero")) {
      getDynamicHeroDataAsync();
    }
  });
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    if (isCreateNewVisible.value) {
      isDynamicHeroModalVisible.value = false;
    } else {
      closeModal();
    }
  }
};
const scheduleHeroes = (uuid) => {
  todos.value = [];
  incId.value = 0;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value],
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;
  selectedDate.value = "";
  scheduleDateConfirm.value = "";
  dataForSelectedHero.uuid = uuid;
  isScheduleDynamicHeroVisible.value = true;
  uuidForSelectedHero.value = uuid;
  dynamicHeroList.value.forEach((data) => {
    if (data.uuid === uuid) {
      dataForSelectedHero.value = data;
      data.selectedText = "SCHEDULE";
    }
  });
};
const unScheduleHeroes = (uuid) => {
  selectedDate.value = "";
  isUnscheduleDynamicHeroVisible.value = true;
  uuidForSelectedHero.value = uuid;
  dynamicHeroList.value.forEach((data) => {
    if (data.uuid === uuid) {
      dataForSelectedHero.value = data;
      data.selectedText = "UNSCHEDULE";
    }
  });
};
const navigateToCreateDuplicate = (template, uuid) => {
  const pathMap = {
    news: "add-dynamic-news",
    quiz: "add-dynamic-quiz",
    event: "add-dynamic-event",
    content: "add-dynamic-content",
    advice: "add-dynamic-advice",
  };

  const path = pathMap[template];
  if (path) {
    router.push({
      path,
      query: { "create-duplicate": template, [QUERY_PARAM_KEY.UUID]: uuid },
    });
  }
};
const PatchScheduledHero = () => {
  stateType.value = "scheduled";
  isScheduleDynamicHeroVisible.value = false;
  actionStatus.value = `${getActionStatusText(stateType.value)}`;
  selectedDate.value = scheduleDateConfirm.value;

  const patchMap = {
    event: patchDynamicHeroEventDataAsync,
    content: patchDynamicHeroContentDataAsync,
    quiz: patchDynamicHeroQuizDataAsync,
    news: patchDynamicHeroNewsDataAsync,
    advice: patchDynamicHeroAdviceDataAsync,
  };

  const patchFunction = patchMap[dataForSelectedHero.value.template];
  if (patchFunction) {
    patchFunction();
  }
};
const unpatchScheduledHero = () => {
  stateType.value = "draft";
  isUnscheduleDynamicHeroVisible.value = false;
  actionStatus.value = `${getActionStatusText(stateType.value)}`;

  const patchMap = {
    event: patchDynamicHeroEventDataAsync,
    content: patchDynamicHeroContentDataAsync,
    quiz: patchDynamicHeroQuizDataAsync,
    news: patchDynamicHeroNewsDataAsync,
    advice: patchDynamicHeroAdviceDataAsync,
  };

  const patchFunction = patchMap[dataForSelectedHero.value.template];
  if (patchFunction) {
    patchFunction();
  }
};
const saveSelectedLiveHero = () => {
  const date = new Date();
  date.setHours(0, 0, 0, 0);
  selectedDate.value = date;
  stateType.value = "live";
  isReplacementConfirmPopupVisible.value = false;
  actionStatus.value = `${getActionStatusText(stateType.value)}`;


  const patchMap = {
    event: patchDynamicHeroEventDataAsync,
    content: patchDynamicHeroContentDataAsync,
    quiz: patchDynamicHeroQuizDataAsync,
    news: patchDynamicHeroNewsDataAsync,
    advice: patchDynamicHeroAdviceDataAsync,
  };

  const patchFunction = patchMap[dataForSelectedHero.value.template];
  if (patchFunction) {
    patchFunction();
  }
};
const patchDynamicHeroNewsDataAsync = async () => {
  isSchedulingUnschedulingPopup.value = true;
  let parsedDate = selectedDate.value;

  if (selectedDate.value) {
    parsedDate = Date.parse(selectedDate.value) / 1000;
  }

  const payload = {
    uuid: dataForSelectedHero.value?.uuid,
    title: dataForSelectedHero.value?.title,
    template: dataForSelectedHero.value?.template,
    publishDate: parsedDate,
    image: dataForSelectedHero.value?.image,
    data: {
      body: dataForSelectedHero.value?.data?.body,
      image: dataForSelectedHero.value?.data?.image,
      ctaText: dataForSelectedHero.value?.data?.ctaText,
      ctaLink: dataForSelectedHero.value?.data?.ctaLink,
    },
    state: stateType.value,
    preview: dataForSelectedHero.value?.preview,
  };

  if (stateType.value === "draft") {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: dataForSelectedHero.value?.uuid,
    });
    isSchedulingUnschedulingPopup.value = false;
    await getDynamicHeroDataAsync();

    if (stateType.value === "live") {
      triggerLoading("heroChange");
    } else if (!selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroScheduled");
    }

    closeModal();
  } catch (error) {
    isloading.value = false;
    isSchedulingUnschedulingPopup.value = false;
    console.error("Error in patchDynamicHeroNewsDataAsync:", error);
  }
};
const patchDynamicHeroAdviceDataAsync = async () => {
  isSchedulingUnschedulingPopup.value = true;
  let parsedDate = selectedDate.value;

  if (selectedDate.value) {
    parsedDate = Date.parse(selectedDate.value) / 1000;
  }

  const payload = {
    uuid: dataForSelectedHero.value?.uuid,
    title: dataForSelectedHero.value?.title,
    template: dataForSelectedHero.value?.template,
    publishDate: parsedDate,
    image: dataForSelectedHero.value?.image,
    data: {
      body: dataForSelectedHero.value?.data?.body,
      image: dataForSelectedHero.value?.data?.image,
    },
    state: stateType.value,
    preview: dataForSelectedHero.value?.preview,
  };

  if (stateType.value === "draft") {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: dataForSelectedHero.value?.uuid,
    });

    isSchedulingUnschedulingPopup.value = false;
    await getDynamicHeroDataAsync();

    if (stateType.value === "live") {
      triggerLoading("heroChange");
    } else if (!selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroScheduled");
    }

    closeModal();
  } catch (error) {
    isloading.value = false;
    isSchedulingUnschedulingPopup.value = false;
    console.error("Error in patchDynamicHeroAdviceDataAsync:", error);
  }
};
const patchDynamicHeroQuizDataAsync = async () => {
  isSchedulingUnschedulingPopup.value = true;
  let parsedDate = selectedDate.value;

  if (selectedDate.value !== 0) {
    parsedDate = Date.parse(selectedDate.value) / 1000;
  }

  const payload = {
    uuid: dataForSelectedHero.value?.uuid,
    title: dataForSelectedHero.value?.title,
    template: dataForSelectedHero.value?.template,
    publishDate: parsedDate,
    image: dataForSelectedHero.value?.image,
    data: {
      answer: dataForSelectedHero.value?.data.answer,
      body: dataForSelectedHero.value?.data?.body,
      ctaLink: dataForSelectedHero.value?.data?.ctaLink,
      commentary: dataForSelectedHero.value?.data?.commentary,
    },
    state: stateType.value,
    preview: dataForSelectedHero.value?.preview,
  };

  if (stateType.value === "draft") {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: dataForSelectedHero.value?.uuid,
    });

    isSchedulingUnschedulingPopup.value = false;
    await getDynamicHeroDataAsync();

    if (stateType.value === "live") {
      triggerLoading("heroChange");
    } else if (!selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroScheduled");
    }

    closeModal();
  } catch (error) {
    isloading.value = false;
    isSchedulingUnschedulingPopup.value = false;
    console.error("Error in patchDynamicHeroQuizDataAsync:", error);
  }
};
const patchDynamicHeroContentDataAsync = async () => {
  isSchedulingUnschedulingPopup.value = true;
  let parsedDate = selectedDate.value;

  if (selectedDate.value) {
    parsedDate = Date.parse(selectedDate.value) / 1000;
  }

  const payload = {
    uuid: dataForSelectedHero.value?.uuid,
    title: dataForSelectedHero.value?.title,
    template: dataForSelectedHero.value?.template,
    publishDate: parsedDate,
    image: dataForSelectedHero.value?.image,
    data: {
      title: dataForSelectedHero.value?.data?.title,
      image: dataForSelectedHero.value?.data?.image,
      backgroundColor: dataForSelectedHero.value?.data?.backgroundColor,
      titleColor: dataForSelectedHero.value?.data?.titleColor,
      ctaLink: dataForSelectedHero.value?.data?.ctaLink,
      ctaText: dataForSelectedHero.value?.data?.ctaText,
      body: dataForSelectedHero.value?.data?.body,
    },
    state: stateType.value,
    preview: dataForSelectedHero.value?.preview,
  };

  if (stateType.value === "draft") {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: dataForSelectedHero.value?.uuid,
    });

    isSchedulingUnschedulingPopup.value = false;
    await getDynamicHeroDataAsync();

    if (stateType.value === "live") {
      triggerLoading("heroChange");
    } else if (!selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroScheduled");
    }

    closeModal();
  } catch (error) {
    isloading.value = false;
    isSchedulingUnschedulingPopup.value = false;
    console.error("Error in patchDynamicHeroContentDataAsync:", error);
  }
};

const patchDynamicHeroEventDataAsync = async () => {
  isSchedulingUnschedulingPopup.value = true;
  let parsedDate = selectedDate.value;

  if (selectedDate.value) {
    parsedDate = Date.parse(selectedDate.value) / 1000;
  }

  const payload = {
    uuid: dataForSelectedHero.value?.uuid,
    title: dataForSelectedHero.value?.title,
    template: dataForSelectedHero.value?.template,
    publishDate: parsedDate,
    image: dataForSelectedHero.value?.image,
    data: {
      body: dataForSelectedHero.value?.data?.body,
      image: dataForSelectedHero.value?.data?.image,
      subtext: dataForSelectedHero.value?.data?.subtext,
      dates: dataForSelectedHero.value?.data?.dates,
      ctaLink: dataForSelectedHero.value?.data?.ctaLink,
      ctaText: dataForSelectedHero.value?.data?.ctaText,
    },
    state: stateType.value,
    preview: dataForSelectedHero.value?.preview,
  };

  if (stateType.value === "draft") {
    delete payload.publishDate;
  }

  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: dataForSelectedHero.value?.uuid,
    });

    isSchedulingUnschedulingPopup.value = false;
    await getDynamicHeroDataAsync();

    if (stateType.value === "live") {
      triggerLoading("heroChange");
    } else if (!selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroUnscheduled");
    } else if (selectedDate.value && stateType.value !== "live") {
      triggerLoading("heroScheduled");
    }

    closeModal();
  } catch (error) {
    isloading.value = false;
    isSchedulingUnschedulingPopup.value = false;
    console.error("Error in patchDynamicHeroEventDataAsync:", error);
  }
};
const deletePopUpOpen = (uuid) => {
  isHeroDeletePopupVisible.value = true;
  uuidForSelectedHero.value = uuid;

  dynamicHeroList.value.forEach((data) => {
    data.dropDown = false;
    if (uuid === data.uuid) {
      data.selectedText = "DELETE";
    }
  });

  dynamicHeroList.value = [...dynamicHeroList.value];
};
const deleteHeroAsync = async () => {
  isDeletingModalVisible.value = true;
  try {
    await deleteDynamicHeroDataAsync({ uuid: uuidForSelectedHero.value, });
    triggerLoading("newDeletedSuccess");
    await getDynamicHeroDataAsync();
  } catch (error) {
    console.error("Error in deleteHeroAsync:", error);
  } finally {
    closeModal();
    isDeletingModalVisible.value = false;
  }
};
const getDynamicHeroDataAsync = async () => {
  isCreateNewVisible.value = false;
  isloading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isloading.value);

  dynamicHeroList.value = [];
  expiredHeroCount.value = 0;

  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;

    if (Object.keys(response).length) {
      disableList.value = [];
      disabledDates.value = [];

      response.forEach((element) => {
        if (element.state === 'scheduled' && element.publishDate) {
          const currentDate = new Date();
          currentDate.setHours(0, 0, 0, 0);
          const timestampInSeconds = Math.floor(currentDate.getTime() / 1000);
          const timestamp = element.publishDate;

          if (timestampInSeconds < timestamp) {
            const date = new Date(timestamp * 1000);
            disabledDates.value.push(date);
          }
          disableList.value.push({
            date: element.publishDate || '',
            description: element.title || '',
            template: element.template || '',
          });
        }
      });

      dynamicHeroList.value = response ?? [];

      dynamicHeroList.value.forEach((data) => {
        data.currentDateExpired = false;

        if (data.state === 'expired') {
          expiredHeroCount.value++;

          const currentDate = new Date();
          currentDate.setHours(0, 0, 0, 0);
          const unixTimestampSec = Math.floor(currentDate.getTime() / 1000);
          let elementDate = getTime(data.publishDate);
          const currentExpiredDate = getTime(unixTimestampSec);

          if (elementDate === currentExpiredDate) {
            data.currentDateExpired = true;
          }
        }
        if (data.state === 'draft') {
          data.publishDate = 0;
        }
        data.dropDown = false;
        data.selectedText = '';
        data.isSelected = false;
        data.isDisable = false;
      });

      dynamicHeroList.value.sort(customSort);
    }

    isloading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isloading.value);
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, false);
  } catch (error) {
    isloading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isloading.value);
    hasReloadTextCheck.value = true;
    console.error('Error in getDynamicHeroDataAsync:', error);
  }
};
const customSort = (a, b) => {
  if (a.publishDate === 0 && b.publishDate !== 0) {
    return 1;
  } else if (a.publishDate !== 0 && b.publishDate === 0) {
    return -1;
  } else {
    return a.publishDate - b.publishDate;
  }
};
const navigateToEditPage = (template, uuid, replace) => {
  const QUERY_PARAM_KEY = { UUID: 'uuid' };

  const basePaths = {
    news: 'dynamic-news',
    quiz: 'dynamic-quiz',
    event: 'dynamic-event',
    content: 'dynamic-content',
    advice: 'dynamic-advice',
  };

  const path = basePaths[template];

  const pathPrefix = replace === "replace" ? "add-" : "edit-";
  const queryParams =
    replace === "replace"
      ? { replace: "replace-live-hero" }
      : { [QUERY_PARAM_KEY.UUID]: uuid };

  if (path) {
    router.push({
      path: `${pathPrefix}${path}`,
      query: queryParams,
    });
  }
};
const handleDateClick = (newValue) => {
  selectedDate.value = newValue;
  scheduleDateConfirm.value = newValue;
  isScheduleDateSelected.value = true;
};
const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "long", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};
const displayOption = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;

  dynamicHeroList.value.forEach((data) => {
    if (item.uuid !== data.uuid) {
      data.dropDown = false;
    }
  });

  dynamicHeroList.value = [...dynamicHeroList.value];
};
const handleClickOutside = (event) => {
  if (!dropdownItem.value || !dropdownItem.value.dropDown) return;

  const isEditBtn = event.target?.classList.contains("dynamic-hero-table-edit-btn");
  const menuClass = dropdownItem.value.state === "expired"
    ? ".dynamic-expired-hero-menu-selected"
    : ".dynamic-hero-menu-selected";

  const menuElement = document.querySelector(menuClass);

  if (!isEditBtn && (!menuElement || !menuElement.contains(event.target))) {
    dropdownItem.value.dropDown = false;
    dynamicHeroList.value = [...dynamicHeroList.value];
    dropdownItem.value = null;
  }
};
const openNewHeroPopup = (item) => {
  isDynamicHeroModalVisible.value = true;
  selectedDynamicType.value = "quiz";
  if (item) {
    dataForSelectedHero.value = item;
  }
};
const backBtn = () => {
  if (isSelected.value) {
    isConfirmModalVisible.value = true;
  } else {
    isCreateNewVisible.value = false;
    isSelected.value = false;
    dynamicHeroList.value.forEach((data) => {
      data.selectedText = "";
    });
  }
};
const closeConfirmModal = () => {
  isConfirmModalVisible.value = false;
};
const closeReplacementConfirmPopup = () => {
  isReplacementConfirmPopupVisible.value = false;
};
const closeSelectDynamicHero = () => {
  isDynamicHeroModalVisible.value = false;
};
const closeModal = () => {
  if (dynamicHeroList.value.length > 0) {
    dynamicHeroList.value.forEach((data) => {
      data.selectedText = "";
    });
  }
  isScheduleDateSelected.value = false;
  selectedDate.value = 0;
  isUnableToReplace.value = false;
  isConfirmModalVisible.value = false;
  isReplacementConfirmPopupVisible.value = false;
  isUnscheduleDynamicHeroVisible.value = false;
  dataForSelectedHero.value = {};
  isScheduleDynamicHeroVisible.value = false;
  isDynamicHeroModalVisible.value = false;
  isHeroDeletePopupVisible.value = false;
  uuidForSelectedHero.value = "";
  isDeletingModalVisible.value = false;
  isCreateNewVisible.value = false;
  isSelected.value = false;
  isSchedulingUnschedulingPopup.value = false;
  stateType.value = "";
};
const selectedDynamicOption = () => {
  const { template, uuid } = dataForSelectedHero.value || {};
  const pathMap = {
    news: "add-dynamic-news",
    quiz: "add-dynamic-quiz",
    event: "add-dynamic-event",
    content: "add-dynamic-content",
    advice: "add-dynamic-advice",
    banner: "/banner/create",
  };

  if (template && selectedDynamicType.value === template) {
    navigateToEditPage(template, uuid, "replace");
  } else {
    const path = pathMap[selectedDynamicType.value];
    if (dataForSelectedHero.value.state === "live") {
      if (path) {
        router.push({
          path,
          query: { replace: "replace-live-hero" },
        });
      }
    } else if (path) {
      router.push({ path });
    }
  }
  isDynamicHeroModalVisible.value = false;
  if (dynamicHeroList.value.length > 0) {
    dynamicHeroList.value.forEach((data) => {
      data.selectedText = "";
    });
  }
};
const toggleText = () => {
  isShowList.value = !isShowList.value;
  if (isShowList.value) {
    if (targetElement.value) {
      const offsetTop = targetElement.value.offsetTop;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
  }
};
const openSavePopup = () => {
  if (dataForSelectedHero?.value?.state === "draft") {
    isUnableToReplace.value = true;
  } else {
    isReplacementConfirmPopupVisible.value = true;
  }
};
const navigateToSelectLiveHero = (item) => {
  isShowList.value = false;
  uuidForSelectedHero.value = item.uuid;
  isCreateNewVisible.value = !isCreateNewVisible.value;
  item.dropDown = false;
  dynamicHeroList.value.forEach((data) => {
    if (item.uuid !== data.uuid) {
      data.selectedText = t("COMMON.SELECT");
      isSelectBtnVisible.value = true;
    } else if (item.uuid === data.uuid) {
      isCreateNewBtnVisible.value = true;
      isSelectLiveHero.value = true;
      data.selectedText = t('DYNAMIC_HERO.CREATE_NEW');
    }
  });
};
const selectLiveHeroOnSelect = (item) => {
  dataForSelectedHero.value = {};
  dynamicHeroList.value.forEach((data) => {
    if (item.uuid === data.uuid) {
      data.selectedText = t("COMMON.SELECTED");
      isSelected.value = true;
      dataForSelectedHero.value = item;
    }
  });
};
const selectLiveHeroOnSelected = (item) => {
  dynamicHeroList.value.forEach((data) => {
    if (item.uuid === data.uuid) {
      data.selectedText = t("COMMON.SELECT");
      isSelected.value = !isSelected.value;
    }
  });
};
watch(isSelected, (newValue) => {
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, newValue);
});
watch(stateType, (newState) => {
  actionStatus.value = `${getActionStatusText(newState)}`;
});
onBeforeUnmount(() => {
  document.removeEventListener($keys.KEY_NAMES.CLICK, handleClickOutside);
  $eventBus.off($keys.KEY_NAMES.CURRENT_LINK);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
</script>
