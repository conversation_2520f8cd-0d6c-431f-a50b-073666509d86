<template>
  <client-only>
    <content-wrapper
      wrapper-classes="padding-zero main-section"
      :is-body-loading="isPageLoading"
    >
      <div v-if="!isPageLoading" class="add-content-form-hero">
        <div class="main-content">
          <dynamicHeroContentHeader
            :isReplaceLiveHero="isReplaceLiveHero"
            :contentName="contentName"
            :isCampaignModified="isCampaignModified"
            :contentDescriptionText="description"
            :checkForTextPresence="checkForTextPresence"
            @back="backToDynamicHeroList"
            @openReplace="openReplacePopup"
            @openSave="openSavePopup"
          />
          <div class="add-content-intro-section">
            <dynamicHeroContentIntro
              baseClass="add"
              :heading="$t('DYNAMIC_HERO.CONTENT_FORM')"
              :startDateText="$t('DYNAMIC_HERO.START_DATE')"
              v-model:selectedDate="selectedDate"
              :isLiveHeroReplaced="isReplaceLiveHero"
              :isHeroLive="false"
              :disabledDates="disabledDates"
              @date-click="handleDateClick"
              :isRange="false"
              :markers="markers"
            />
            <dynamicHeroContentForm
              baseClass="add"
              :contentName="contentName"
              @update:contentName="contentName = $event"
              :contentList="contentList"
              :selectedDate="selectedDate"
              :isReplaceLiveHero="isReplaceLiveHero"
              :isEventStatusDisplayed="isEventStatus"
              @selectContent="selectContent"
              @scheduleToggle="scheduleToggle"
              :hasContentNameFocus="hasContentNameFocus"
              :checkContentName="checkContentName"
              :hideContentTip="hideContentTip"
            />
          </div>
          <dynamicHeroContentBody
            containerClass="add-form-container-dynamic"
            :contentList="contentList"
            :selectedArticle="selectedArticle"
            :selectedRecipe="selectedRecipe"
            :selectedCategoryData="selectedCategoryData"
            :description="description"
            :ctaInput="ctaInput"
            :showPreview="true"
            :isPreviewEnabled="isHeroPreview"
            @update:description="description = $event"
            @update:ctaInput="ctaInput = $event"
            @deleteRecipe="deleteRecipePopUp"
            @deleteCategory="deleteCategoriePopup"
            :selectRecipe="selectRecipe"
            :isRecipesButtonVisible="isDisplayRecipesButton"
            :isArticlesButtonVisible="isDisplayArticlesButton"
            :addCategoryButton="addCategoryButton"
            @update:isPreviewEnabled="[(isHeroPreview = $event)]"
            :selectArticle="selectArticle"
            :deleteArticlePopUp="deleteArticlePopUp"
            :replaceArticle="selectArticle"
            :defaultImage="defaultImage"
            :updateShowAsterisk="updateShowAsterisk"
          />
        </div>
      </div>
      <saveModal
        v-if="isContentDraftPopupVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="postDynamicFormAsync"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DYNAMIC_HERO.EVENT_DRAFT_FORM')"
        :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
      />
      <DynamicHeroScheduleModal
        :isVisible="isScheduleContentPopupVisible"
        :heroData="heroData"
        :disabledDates="disabledDates"
        @close="closeModal"
        @schedule="closeModal"
        :markers="markers"
        :PatchScheduledHero="postDynamicFormAsync"
        :selectedDateValue="scheduleDateConfirm"
        @date-click="handleDateClickPopup"
      />
      <RecipeModal
        v-if="hasRecipeContentForm"
        :recipeList="addRecipeList"
        :lang="lang"
        :query="queryPopUp"
        @update:query="queryPopUp = $event"
        :isTableLoading="isTableDataLoading"
        :defaultImage="defaultImage"
        :canLoadMore="fromPopUp + sizePopUp < addRecipeListTotal"
        :isSearchPopupExitEnable="isSearchPopupExitEnable"
        :selectedItem="selectedItem"
        @close="closeModal"
        @search="searchPopUp"
        @resetQuery="resetPopupQuery"
        @selectRecipe="selectHeroRecipe"
        @confirmSelection="selectedRecipeForContent"
        @loadMore="loadMoreRecipesAsync"
        @done="addRecipeToContent"
      />
      <dynamicHeroArticleModel
        v-if="isDisplayArticlesPopup"
        :isAddArticlesMatches="isAddArticlesMatches"
        :searchQuery="searchQuery"
        @update:searchQuery="searchQuery = $event"
        :formattedArticles="formattedArticles"
        :isTableDataLoading="isTableDataLoading"
        :isSearchExitEnable="isSearchPopupExitEnable"
        :defaultImage="defaultImage"
        @close="closeModal"
        @clear-search="clearSearchList"
        @add-article="addHeroArticle"
        @selected-article="SelectedData"
        @done="addArticleToContent"
        :searchArticles="searchArticles"
      />
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backToDynamicHeroListConfirm"
        :closeModal="closeModal"
      />
      <dynamicHeroAddCategoryModel
        v-if="isAddCategoryModal"
        :queryCategory="queryCategory"
        @update:queryCategory="queryCategory = $event"
        :isSearchExitEnable="isSearchExitEnable"
        :displayNoRecipeSection="displayNoRecipeSection"
        :isTableDataLoading="isCategorytableDataLoading"
        :categoryUpdateList="categoryUpdateList"
        :formattedCategoryPopUp="formattedCategoryPopUp"
        :canLoadMore="fromPopUp + sizePopUp < addCategoriesTotal"
        :defaultImage="defaultImage"
        :lang="lang"
        :saveButtonMessage="'Done'"
        :categoriesTitle="'Add Categories to Content form'"
        :categoriesSubtitle="'Select categories to add to your group.'"
        @close="closeModal"
        @getCategorySearch="getCategorySearch"
        @resetQuery="resetQuery"
        @isCategoryChecked="isCategoryChecked"
        @loadMore="loadUpdatePopUpAsync"
        @done="addCategories"
      />
      <replacementModal
        v-if="isReplacementConfirmPopupVisible"
        :closeModal="closeModal"
        :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
        :saveReplacement="postDynamicFormAsync"
        :closeReplacement="closeModal"
      />
      <deleteModal
        v-if="isDeleteArticlePopupVisible"
        :closeModal="closeModal"
        :deleteItem="deleteArticle"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_ARTICLE')"
        :productDescriptionOne="'Are you sure you want to remove this article from the'"
        :productDescriptionTwo="'content hero?'"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <deleteModal
        v-if="isDeleteRecipePopupVisible"
        :closeModal="closeModal"
        :deleteItem="deleteRecipe"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
        :productDescriptionTwo="'content hero?'"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <deleteModal
        v-if="isDeleteCategoryPopupVisible"
        :closeModal="closeModal"
        :deleteItem="deleteCategory"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY_POPUP')"
        :productDescriptionTwo="'content hero?'"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <savingModal
        v-if="isloading"
        :status="
          selectedDate ? $keys.KEY_NAMES.SCHEDULING : $keys.KEY_NAMES.SAVING
        "
      />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useNuxtApp } from "#app";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import cancelModal from "@/components/cancel-modal";
import replacementModal from "@/components/confirm-replacement-modal";
import deleteModal from "@/components/delete-modal";
import RecipeModal from "../components/recipe-modal.vue";
import RecipeService from "@/services/RecipeService";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import savingModal from "@/components/saving-modal";
import saveModal from "@/components/save-modal.vue";
import dynamicHeroContentBody from "../components/pages/dynamic-hero/dynamic-hero-content-body.vue";
import dynamicHeroContentForm from "../components/pages/dynamic-hero/dynamic-hero-content-form.vue";
import dynamicHeroContentIntro from "../components/pages/dynamic-hero/dynamic-hero-content-intro.vue";
import dynamicHeroContentHeader from "../components/pages/dynamic-hero/dynamic-hero-content-header.vue";
import dynamicHeroArticleModel from "../components/pages/dynamic-hero/dynamic-hero-article-model.vue";
import dynamicHeroAddCategoryModel from "../components/pages/dynamic-hero/dynamic-hero-add-category-model.vue";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useProjectLang } from "@/composables/useProjectLang";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import defaultImage from "~/assets/images/default_recipe_image.png";

const {
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  postDynamicHeroDataAsync,
  postDynamicHeroDataList,
} = useDynamicHeroStore();
const heroData = ref([
  {template: 'Content'}
]);
const selectedArticle = ref([]);
const selectedCategoryData = ref([]);
const selectedRecipe = ref([]);
const ctaInput = ref("");
const store = useStore();
const route = useRoute();
const { getRef } = useRefUtils();
const { formatDate, isDateDisabled, checkDate } = useTimeUtils();
const {
  triggerLoading,
  routeToPage,
  processScheduledElement,
  isScheduledWithPublishDate,
  checkDuplicate,
  getDisabledDates,
  getDisableList,
  useCalendarMarkers,
  updateSelectedItems,
  updateShowAsterisk,
  checkForTextPresence,
  splitLangAndCountry,
} = useCommonUtils(
  selectedArticle,
  selectedCategoryData,
  selectedRecipe,
  ctaInput
);
const { watchReactiveValue } = useWatcherUtils();
const { $keys, $auth } = useNuxtApp();
const { isInnitAdmin } = useInnitAuthStore();
const { readyProject, getProject } = useProjectLang();
const totalCategories = ref("");
const selectedData = ref({});
const isCategorytableDataLoading = ref(false);
const isTableDataLoading = ref(false);
const isAddCategoryModal = ref(false);
const formattedCategoryPopUp = ref([]);
const countCategoriesSelected = ref(0);
const categoryUpdateList = ref([]);
const addCategoriesTotal = ref(0);
const fromPopUp = ref(0);
const sizePopUp = ref(9);
const isHeroPreview = ref(false);
const formattedCategory = ref([]);
const selectedCategory = ref([]);
const configData = ref({});
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const queryCategory = ref("");
const project = ref({});
const isSearchExitEnable = ref(false);
const displayNoRecipeSection = ref(false);
const isEditSearchLoading = ref(false);
const hasNoResultFound = ref(false);
const disableList = ref([]);
const incId = ref(0);
const todos = ref([]);
const isloading = ref(false);
const description = ref("");
const isArticleSelected = ref(false);
const disabledDates = ref([]);
const isAddArticlesMatches = ref(false);
const scheduleDateConfirm = ref("");
const isDisplayArticlesPopup = ref(false);
const isDisplayRecipesButton = ref(false);
const isDisplayArticlesButton = ref(false);
const isEventStatus = ref(false);
const contentName = ref("");
const hasContentNameFocus = ref(false);
const selectedDate = ref("");
const searchQuery = ref("");
const hasRecipeContentForm = ref(false);
const selectedItem = ref(false);
const addRecipeListTotal = ref(0);
const addRecipeList = ref([]);
const recipeDataForhero = ref([]);
const queryPopUp = ref("");
const isSearchPopupExitEnable = ref(false);
const formattedArticles = ref([]);
const isContentPopupVisible = ref(false);
const isContentDraftPopupVisible = ref(false);
const isScheduleContentPopupVisible = ref(false);
const contentCTALink = ref("");
const isPageLoading = ref(false);
const isLiveHero = ref(false);
const createDuplicate = ref(false);
const contentList = ref([
  {
    value: "1",
    name: "Article",
    isChecked: false,
  },
  {
    value: "2",
    name: "Recipe",
    isChecked: false,
  },
]);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const isDeleteArticlePopupVisible = ref(false);
const isDeleteRecipePopupVisible = ref(false);
const isDeleteCategoryPopupVisible = ref(false);
const dummySelectedRecipe = ref([]);
const dummySelectedArticle = ref([]);
const lang = ref("");
const isAdminCheck = ref(false);

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  project.value = await getProject();
  lang.value = store.getters["userData/getDefaultLang"];

  await getDynamicHeroDataAsync();
  let sourceUrl = window.location.href;
  if (sourceUrl.includes("create-duplicate")) {
    isCampaignModified.value = true;
    createDuplicate.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    await getEditDynamicHeroDataAsync();
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }

  if (sourceUrl.includes("replace-live-hero")) {
    isReplaceLiveHero.value = true;
  }
  disabledDates.value = getDisabledDates().value;
  disableList.value = getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }
  incId.value = todos.value.length;

  await getCategoryListAsync();
  await getCategoryStatisticsPopUpAsync();
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};

const getTotalCategoryAsync = async (isin) => {
  try {
    const { language, country } = splitLangAndCountry(lang.value);
    const totalKey = $keys.EVENT_KEY_NAMES.CATEGORY_TYPE;
    const payload = { type: totalKey, country, lang: language, isins: isin };

    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", { payload });

    const stats = store.getters["categoriesGroup/getCategoryStatistics"];
    const category = stats?.[0];
    const total = category?.totalRecipes;

    if (total != null) {
      totalCategories.value = total;

      const selected = selectedCategoryData.value?.[0];
      if (selected) selected.totalRecipes = total;
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getTotalCategoryAsync:`, error);
  }
};

const getSingleCategoryAsync = async (isin) => {
  try {
    queryCategory.value = isin;
    const { language, country } = splitLangAndCountry(lang.value);
    const payload = {
      type: $keys.EVENT_KEY_NAMES.CATEGORY_TYPE,
      country,
      lang: language,
      q: isin,
      from: fromPopUp.value,
      size: sizePopUp.value,
      sort: $keys.EVENT_KEY_NAMES.SORT_BY,
    };
    await store.dispatch("categoriesGroup/getCategoryGroupListAsync", { payload });

    const response = store.getters["categoriesGroup/getCategoryGroupList"];
    selectedCategoryData.value = [{
      ...response?.results?.[0],
      totalRecipes: totalCategories.value,
    }];
    contentList.value[1].isChecked = true;
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getSingleCategoryAsync:`, error);
  }
};
const campaignModifiedAddRecipe = () => {
  isCampaignModified.value = true;
};
const deleteCategory = () => {
  isDeleteCategoryPopupVisible.value = false;
  selectedCategoryData.value = [];
  selectedData.value = {};
};
const isCategoryChecked = (cat) => {
  const clonedCategoryPopUp = [...formattedCategoryPopUp.value];
  clonedCategoryPopUp.forEach((item) => {
    item.isChecked = cat?.isin === item?.isin ? !item.isChecked : false;
  });
  formattedCategoryPopUp.value = clonedCategoryPopUp;

  countCategoriesSelected.value = 0;
  selectedData.value = {};
  clonedCategoryPopUp.forEach((item) => {
    if (item.isChecked) {
      selectedData.value = item;
      countCategoriesSelected.value += 1;
    }
  });
};

const addCategories = () => {
  selectedCategoryData.value = [];
  if (selectedData.value.isin) {
    selectedCategoryData.value.push(selectedData.value);
  }
  isCampaignModified.value = true;
  closeModal();
};
const addCategoryButton = () => {
  queryCategory.value = "";
  isCategorytableDataLoading.value = true;
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  countCategoriesSelected.value = 0;
  isAddCategoryModal.value = true;
  addCategoriesTotal.value = 0;
  isTableDataLoading.value = true;
  getCategoryListAsync();
};
const loadUpdatePopUpAsync = async () => {
  let from = fromPopUp.value + sizePopUp.value;
  if (from < addCategoriesTotal.value) {
    fromPopUp.value = from;
    const loadMoreCategoryList = await getCategoryListAsync();
    if (loadMoreCategoryList && categoryUpdateList.value) {
      await getCategoryStatisticsPopUpAsync(loadMoreCategoryList);
      categoryUpdateList.value = [
        ...categoryUpdateList.value,
        ...loadMoreCategoryList,
      ];
    }
  }

  if (
    selectedData.value &&
    formattedCategoryPopUp.value.length &&
    selectedData.value.isin
  ) {
    formattedCategoryPopUp.value.forEach((item) => {
      if (item?.isin === selectedData.value?.isin) {
        item.isChecked = true;
      }
    });
  }
};
const getCategorySearch = () => {
  if (queryCategory.value) {
    addCategoriesTotal.value = 0;
    fromPopUp.value = 0;
    formattedCategoryPopUp.value = [];
    categoryUpdateList.value = [];
    getCategoryListAsync();
    isSearchExitEnable.value = true;
  }
};

const markItems = (results, selectedItems, key) => {
  results.forEach((data) => {
    data[key] = selectedItems.some((item) => item.isin === data.isin);
  });
};
const swapFirstItemIfSelected = (list, selectedItems) => {
  if (selectedItems.length > 0) {
    const selectedIndex = list.findIndex((data) => data.isin === selectedItems[0].isin);
    if (selectedIndex > 0) {
      [list[0], list[selectedIndex]] = [list[selectedIndex], list[0]];
    }
  }
};

const getCategoryListAsync = async () => {
  try {
    const payload = {
      lang: lang.value.split("-")[0],
      q: queryCategory.value,
      from: fromPopUp.value,
      size: sizePopUp.value,
      type: "category",
      sort: "lastMod",
    };

    await store.dispatch("categoriesGroup/getCategoryGroupListAsync", { payload });
    const response = store.getters["categoriesGroup/getCategoryGroupList"];
    response.results = response?.results?.slice(0, 9).map((data) => ({
      ...data,
      isChecked: false,
      isAlreadyAddedCategory: false,
    }));

    markItems(response.results, selectedCategory.value, "isChecked");

    if (fromPopUp.value >= 9) {
      markItems(response.results, formattedCategory.value, "isAlreadyAddedCategory");
      return response.results;
    } else {
      markItems(response.results, formattedCategory.value, "isAlreadyAddedCategory");
      categoryUpdateList.value = response.results ?? [];
      displayNoRecipeSection.value = !categoryUpdateList.value.length;

      if (categoryUpdateList.value.length) {
        getCategoryStatisticsPopUpAsync();
      }
    }
    addCategoriesTotal.value = response.total;
  } catch (error) {
    displayNoRecipeSection.value = true;
    isCategorytableDataLoading.value = false;
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getCategoryListAsync:`, error);
  }
};
const resetQuery = async () => {
  addCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  queryCategory.value = "";
  isSearchExitEnable.value = false;

  await getCategoryListAsync();

  if (selectedData.value && formattedCategoryPopUp.value.length > 0) {
    formattedCategoryPopUp.value.forEach((item) => {
      if (item?.isin === selectedData.value?.isin) {
        item.isChecked = true;
      }
    });
  }
};

const handleError = () => {
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  isEditSearchLoading.value = false;
  hasNoResultFound.value = true;
  configData.filters = [];
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isEditSearchLoading.value);
};
const getCategoryStatisticsPopUpAsync = async (loadMoreCategoryList) => {
  try {
    const isinsList = loadMoreCategoryList || categoryUpdateList.value;
    const isins = isinsList.map(({ isin }) => isin).join(",");

    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", {
      payload: { lang: lang.value.split("-")[0], type: "category", isins },
    });

    const response = store.getters["categoriesGroup/getCategoryStatistics"] || [];
    if (!response.length) return;

    totalCategories.value = response[0]?.totalRecipes;

    isinsList.forEach((data) => {
      const match = response.find(({ isin }) => isin === data?.isin);
      if (match) data.totalRecipes = match.totalRecipes;
    });

    formattedCategoryPopUp.value = [...categoryUpdateList.value, ...(loadMoreCategoryList || [])];
    formattedCategoryPopUp.value.forEach(
      item => item.isChecked = item.isin === selectedData.value?.isin
    );
    isCategorytableDataLoading.value = false;
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getCategoryStatisticsPopUpAsync:`, error);
  }
};

const checkContentName = () => {
  const name = document.getElementById("contentNameRef");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    contentName.value.trim().length > 0
  ) {
    hasContentNameFocus.value = true;
  }
};
const hideContentTip = () => {
  hasContentNameFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event && event.key === "Escape") {
    closeModal();
  }
};
const toggleScheduled = () => {
  if (selectedDate.value !== "") {
    isEventStatus.value = true;
  }
};
const SelectedData = (uuid) => {
  selectedRecipe.value = [];
  selectedArticle.value = [];
  dummySelectedArticle.value = [];
  isCampaignModified.value = true;
  contentCTALink.value = "";
  selectedArticle.value = [];

  formattedArticles.value.forEach((data) => {
    data.content.forEach((item) => {
      if (item?.isAdded) {
        item.isAdded = false;
        item.isDisable = false;
      }
    });
  });

  formattedArticles.value.forEach((data) => {
    data.content.forEach((item) => {
      if (item?.uuid === uuid) {
        selectedArticle.value.push(item);
        isArticleSelected.value = false;
        item.isAdded = false;
        selectedRecipe.value = [];
      } else {
        item.isDisable = false;
      }
    });
  });
  isArticleSelected.value = false;
};
const selectedRecipeForContent = (recipe) => {
  contentCTALink.value = "";
  if (selectedRecipe.value) {
    selectedRecipe.value.isAdded = false;
  }
  recipe.isAdded = false;
  selectedArticle.value = [];
  selectedRecipe.value = [];
  dummySelectedRecipe.value = [];
  selectedItem.value = false;
  isCampaignModified.value = true;
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (!uuid) return;

  isPageLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });
    const response = await editDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      updateHeroState(response);
      updateEventStatus(response);
      updateContentDetails(response);
    }
  } catch (error) {
    handleError();
    console.error("Error in getEditDynamicHeroDataAsync:", error);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const updateHeroState = (response) => {
  isLiveHero.value = response.state === "live" && !createDuplicate.value;
};

const updateEventStatus = (response) => {
  isEventStatus.value = !!response.publishDate;
  if (!createDuplicate.value) {
    selectedDate.value = response.publishDate
      ? getTime(response.publishDate)
      : "";
  }
};

const updateContentDetails = (response) => {
  contentName.value = response.title || "";

  if (response.data) {
    contentCTALink.value = response.data.ctaLink || "";
    ctaInput.value = response.data.ctaText || "";
    description.value = response.data.body || "";

    if (contentCTALink.value.includes("rocheapp://recipe?isin=")) {
      const isin = contentCTALink.value.replace("rocheapp://recipe?isin=", "");
      handleRecipeLink(isin);
    } else if (contentCTALink.value.includes("rocheapp://learn?id=")) {
      const uuid = contentCTALink.value.replace("rocheapp://learn?id=", "");
      handleLearnLink(uuid);
    } else if (
      contentCTALink.value.includes("rocheapp://recipeCategory?isin=")
    ) {
      const catIsin = contentCTALink.value.replace(
        "rocheapp://recipeCategory?isin=",
        ""
      );
      handleRecipeCategoryLink(catIsin);
    }
  }
};
const handleRecipeLink = (isin) => {
  isDisplayRecipesButton.value = true;
  contentList.value[0].isChecked = false;
  contentList.value[1].isChecked = true;
  getRecipeAsync(isin);
};
const handleLearnLink = (uuid) => {
  isDisplayArticlesButton.value = true;
  contentList.value[0].isChecked = true;
  contentList.value[1].isChecked = false;
  getEditArticlesDataAsync(uuid);
};
const handleRecipeCategoryLink = (catIsin) => {
  getTotalCategoryAsync(catIsin);
  getSingleCategoryAsync(catIsin);
  isDisplayRecipesButton.value = true;
  contentList.value[0].isChecked = false;
  contentList.value[1].isChecked = true;
};
const searchArticles = () => {
  if (searchQuery.value) {
    isTableDataLoading.value = true;
    if (searchQuery.value.length) {
      const filteredArticles = [];
      formattedArticles.value.forEach((group) => {
        const filteredContent = group.content.filter((item) =>
          item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
        if (filteredContent.length) {
          filteredArticles.push({ ...group, content: filteredContent });
        }
      });
      formattedArticles.value.splice(
        0,
        formattedArticles.value.length,
        ...filteredArticles
      );
      isSearchPopupExitEnable.value = true;
    }
    if (!searchQuery.value.length) {
      searchQuery.value = "";
      getArticleDataAsync();
      isSearchPopupExitEnable.value = false;
    }
    setTimeout(() => {
      isTableDataLoading.value = false;
    }, 500);
  }
};
const clearSearchList = () => {
  searchQuery.value = "";
  getArticleDataAsync();
  isSearchPopupExitEnable.value = false;
};

const getEditArticlesDataAsync = async (uuid) => {
  if (uuid) {
    try {
      await store.dispatch("articles/getEditArticlesDataAsync", {
        lang: lang.value,
        uuid,
      });
      const response = store.getters["articles/getEditArticleData"];
      updateSelectedItems(selectedArticle.value, response);
    } catch (error) {
      console.error(`Error in getEditArticlesDataAsync:`, error);
    }
  }
};
const getRecipeAsync = async (isin) => {
  const params = {
    country: lang.value,
  };
  try {
    await store.dispatch("recipe/getRecipeAsync", {
      params,
      isin: isin,
    });
    const response = store.getters["recipe/getRecipeData"];
    selectedRecipe.value.push(response);
  } catch (error) {
    console.error("Error in getRecipeAsync:", error);
  }
};
const openReplacePopup = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;

    if (Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error("Error in getDynamicHeroDataAsync:", error);
  }
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
    isEventStatus.value = true;
    isCampaignModified.value = true;
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isEventStatus.value = newStatus;
};
const postDynamicFormAsync = async () => {
  initializeForm();
  const scheduleDate = calculateScheduleDate();
  const payloadCTAlink = generatePayloadCTALink();
  const payloadImage = generatePayloadImage();
  const payload = generatePayload(scheduleDate, payloadCTAlink, payloadImage);

  isloading.value = true;

  try {
    await postDynamicHeroDataAsync({ payload });
    const response = await postDynamicHeroDataList.value;

    if (Object.keys(response).length) {
      emitContentEvents();
      backToDynamicHeroListConfirm();
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} postDynamicFormAsync:`, error);
  } finally {
    isloading.value = false;
  }
};

const initializeForm = () => {
  isloading.value = true;
  isReplacementConfirmPopupVisible.value = false;
  isContentPopupVisible.value = false;
  isContentDraftPopupVisible.value = false;
  isScheduleContentPopupVisible.value = false;
};
const calculateScheduleDate = () => {
  if (isReplaceLiveHero.value) {
    isEventStatus.value = true;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
  }
  const date = new Date(selectedDate.value)
  return Math.floor(date.getTime() / 1000)
};
const generatePayloadCTALink = () => {
  const recipeLink =
    selectedRecipe.value?.[0]?.isin &&
    `rocheapp://recipe?isin=${selectedRecipe.value[0].isin}`;
  const articleLink =
    selectedArticle.value?.[0]?.uuid &&
    `rocheapp://learn?id=${selectedArticle.value[0].uuid}`;
  const categoryLink =
    selectedCategoryData.value?.[0]?.isin &&
    contentList.value?.[1]?.isChecked &&
    `rocheapp://recipeCategory?isin=${selectedCategoryData.value[0].isin}`;
  return recipeLink || articleLink || categoryLink || "";
};
const generatePayloadImage = () => {
  if (selectedRecipe.value?.length && contentList.value?.[1]?.isChecked) {
    return (
      selectedRecipe.value?.[0]?.media?.[lang.value]?.image ||
      selectedRecipe.value?.[0]?.media?.[lang.value]?.externalImageUrl ||
      ""
    );
  } else if (selectedArticle.value?.length && contentList.value?.[0]?.isChecked) {
    return selectedArticle.value?.[0]?.image || "";
  } else if (selectedCategoryData.value?.length && contentList.value?.[1]?.isChecked) {
    return selectedCategoryData.value?.[0]?.data?.[lang.value]?.image || "";
  }
  return "";
};
const generatePayload = (scheduleDate, payloadCTAlink, payloadImage) => {
  let payload = {
    publishDate:
      !selectedDate.value || !isEventStatus.value ? "" : scheduleDate,
    title: contentName.value.trim() || "",
    image: payloadImage,
    template: "content",
    state:
      isEventStatus.value && selectedDate.value ? "scheduled" : "draft",
    preview: isHeroPreview.value,
    data: {
      image: getSelectedDataImage(),
      backgroundColor: getSelectedDataBackgroundColor(),
      titleColor: getSelectedDataTitleColor(),
      title: getSelectedDataTitle(),
      body: description.value,
      ctaText: ctaInput.value,
      ctaLink: payloadCTAlink,
    },
  };

  cleanPayloadData(payload);
  return payload;
};

const getSelectedDataImage = () => {
  if (selectedRecipe.value?.length || selectedCategoryData.value?.length) {
    return "https://roche-education.s3.eu-central-1.amazonaws.com/Phil_cook.png";
  } else if (selectedArticle.value?.length) {
    return "https://roche-education.s3.eu-central-1.amazonaws.com/Phil_read.png";
  }
  return "";
};

const getSelectedDataBackgroundColor = () => {
  if (selectedRecipe.value.length || selectedCategoryData.value.length) {
    return "#EFF2FA";
  } else if (selectedArticle.value.length) {
    return "#D8F0E7";
  }
  return "";
};

const getSelectedDataTitleColor = () => {
  if (selectedRecipe.value.length || selectedCategoryData.value.length) {
    return "#4ABED4";
  } else if (selectedArticle.value.length) {
    return "#00965E";
  }
  return "";
};

const getSelectedDataTitle = () => {
  if (selectedRecipe.value.length || selectedCategoryData.value.length) {
    return "Nouvelles recettes";
  } else if (selectedArticle.value.length) {
    return "Nouveaux articles";
  }
  return "";
};

const cleanPayloadData = (payload) => {
  if (
    !selectedRecipe.value.length &&
    !selectedArticle.value.length &&
    !selectedCategoryData.value.length
  ) {
    delete payload.data.image;
    delete payload.data.backgroundColor;
    delete payload.data.titleColor;
    delete payload.data.title;
    delete payload.data.ctaLink;
  }
  if (ctaInput.value === "") {
    delete payload.data.ctaText;
  }
};

const emitContentEvents = () => {
  if (!selectedDate.value && !isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("contentSaved");
  } else if (
    selectedDate.value &&
    isEventStatus.value &&
    !isLiveHero.value &&
    !isReplaceLiveHero.value
  ) {
    triggerLoading("contentScheduled");
  } else if (isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading("contentLive");
  } else if (isReplaceLiveHero.value) {
    triggerLoading("heroReplaced");
  } else if (!selectedDate.value || !isEventStatus.value) {
    triggerLoading("contentSaved");
  } else if (selectedDate.value && isEventStatus.value) {
    triggerLoading("contentScheduled");
  }
};

const deleteArticlePopUp = () => {
  isDeleteArticlePopupVisible.value = true;
};
const deleteArticle = () => {
  selectedArticle.value = [];
  isDeleteArticlePopupVisible.value = false;
};
const deleteCategoriePopup = () => {
  isDeleteCategoryPopupVisible.value = true;
};
const deleteRecipePopUp = () => {
  isDeleteRecipePopupVisible.value = true;
};
const deleteRecipe = () => {
  selectedRecipe.value = [];
  isDeleteRecipePopupVisible.value = false;
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  routeToPage("dynamic-hero");
};
const getTime = (jsonTimestamp) => 
  new Date(jsonTimestamp * 1000).toLocaleDateString("en-US", { 
    month: "short", 
    day: "numeric", 
    year: "numeric" 
  });

const getArticleDataAsync = async () => {
  formattedArticles.value = [];
  isTableDataLoading.value = true;

  try {
    const response = await store.dispatch("articles/getArticleDataAsync", { lang: lang.value });

    const dummyUUID = dummySelectedArticle.value?.[0]?.uuid;
    const selectedUUID = selectedArticle.value?.[0]?.uuid;

    formattedArticles.value = response.map((article) => {
      const updatedContent = article.content.map((item) => {
        const isDummyMatched = dummyUUID && item.uuid === dummyUUID;
        const isSelectedMatched = selectedUUID && item.uuid === selectedUUID;

        const isDisable =
          dummyUUID && !isDummyMatched
            ? true
            : selectedArticle.value.some((selected) => selected.uuid !== item.uuid);

        return {
          ...item,
          isAdded: isDummyMatched || isSelectedMatched,
          isDisable,
        };
      });

      return {
        ...article,
        content: updatedContent,
      };
    });

    if (selectedUUID) {
      formattedArticles.value.forEach((article) => {
        const selectedItem = article.content.find((item) => item.uuid === selectedUUID);
        if (selectedItem) {
          swapFirstItemIfSelected(article.content, [selectedItem]);
          swapFirstItemIfSelected(formattedArticles.value, [article]);
        }
      });
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getArticleDataAsync:`, error);
  } finally {
    isTableDataLoading.value = false;
  }
};
const closeModal = () => {
  queryCategory.value = "";
  selectedData.value = "";
  dummySelectedRecipe.value = [];
  selectedItem.value = false;
  fromPopUp.value = 0;
  isDeleteCategoryPopupVisible.value = false;
  scheduleDateConfirm.value = selectedDate.value;
  if (!isReplaceLiveHero.value) {
    isReplaceLiveHero.value = false;
  }
  isAddCategoryModal.value = false;
  searchQuery.value = "";
  isSearchPopupExitEnable.value = false;
  isReplacementConfirmPopupVisible.value = false;
  hasRecipeContentForm.value = false;
  isDisplayArticlesPopup.value = false;
  isContentPopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isDeleteArticlePopupVisible.value = false;
  isDeleteRecipePopupVisible.value = false;
  isScheduleContentPopupVisible.value = false;
  isContentDraftPopupVisible.value = false;
};
const selectContent = (data, name) => {
  isCampaignModified.value = true;
  contentList.value.forEach((element) => {
    element.isChecked = false;
  });
  data.isChecked = true;
  if (name === "Article") {
    isAddArticlesMatches.value = true;
    isDisplayArticlesButton.value = true;
    isDisplayRecipesButton.value = false;
  } else if (name === "Recipe") {
    isDisplayRecipesButton.value = true;
    isDisplayArticlesButton.value = false;
  }
};
const openSavePopup = () => {
  isContentPopupVisible.value = true;
  isContentDraftPopupVisible.value = true;
  isScheduleContentPopupVisible.value = false;
  if (selectedDate.value !== "" && isEventStatus.value) {
    isContentPopupVisible.value = true;
    isContentDraftPopupVisible.value = false;
    isScheduleContentPopupVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
  }
};
const selectRecipe = () => {
  fromPopUp.value = 0;
  fetchRecipeDataAsync("");
  hasRecipeContentForm.value = true;
  isTableDataLoading.value = true;
};

const addArticleToContent = () => {
  if (dummySelectedArticle.value.length > 0) {
    selectedArticle.value.push(dummySelectedArticle.value[0]);
  }
  closeModal();
};
const addHeroArticle = (uuid) => {
  isCampaignModified.value = true;
  contentCTALink.value = `rocheapp://learn?id=${uuid}`;
  selectedArticle.value = [];
  dummySelectedArticle.value = [];
  formattedArticles.value.forEach((data) => {
    data.content.map((item) => {
      if (item?.uuid === uuid) {
        dummySelectedArticle.value.push(item);
        isArticleSelected.value = true;
        item.isAdded = true;
        selectedRecipe.value = [];
      } else {
        item.isDisable = true;
      }
    });
  });
};
const selectArticle = () => {
  searchQuery.value = "";
  getArticleDataAsync();
  isAddArticlesMatches.value = true;
  isDisplayArticlesPopup.value = true;
};
const fetchRecipeDataAsync = async (isin = "") => {
  try {
    const response = await RecipeService.getRecipeForCategoriesPopUp(
      project.value, queryPopUp.value, isin, fromPopUp.value, 
      sizePopUp.value, lang.value, store, $auth
    );

    response.results.forEach((data) => {
      Object.assign(data, {
        isAlreadyinCategoryRecipe: false,
        isAdded: false,
        dropDown: false,
        isSelected: true,
        isSearched: false
      });
    });
    markItems(response.results, dummySelectedRecipe.value, "isAdded");
    addRecipeListTotal.value = response.total;

    if (fromPopUp.value >= 10) {
      markItems(response.results, recipeDataForhero.value, "isAlreadyinCategoryRecipe");
    } else {
      addRecipeList.value = response.results;
      markItems(addRecipeList.value, recipeDataForhero.value, "isAlreadyinCategoryRecipe");
    }

    isTableDataLoading.value = false;
    swapFirstItemIfSelected(addRecipeList.value, selectedRecipe.value);
    return response;
  } catch (error) {
    console.error("Error fetching recipe data:", error);
    isTableDataLoading.value = false;
  }
};
const loadMoreRecipesAsync = async () => {
  let from = parseInt(fromPopUp.value) + sizePopUp.value;
  if (from < addRecipeListTotal.value) {
    fromPopUp.value = from;
    let loadMoreRecipeData = await fetchRecipeDataAsync("");
    if (loadMoreRecipeData && loadMoreRecipeData.results) {
      let newRecipes = loadMoreRecipeData.results;
      addRecipeList.value = [...addRecipeList.value, ...newRecipes];
    }
  }
};
const closeConfirmModal = () => {
  dummySelectedRecipe.value = [];
  queryPopUp.value = "";
  isSearchPopupExitEnable.value = false;
  selectedItem.value = false;
  closeModal();
  fromPopUp.value = 0;
};
const searchPopUp = () => {
  if (queryPopUp.value !== "") {
    if (queryPopUp.value !== "") {
      isSearchPopupExitEnable.value = true;
    } else {
      isSearchPopupExitEnable.value = false;
    }
    isTableDataLoading.value = true;
    fromPopUp.value = 0;
    fetchRecipeDataAsync(route.query.isin);
    let scroll = getRef("addTable");
    scroll?.scrollTo(0, 0);
  }
};
const resetPopupQuery = () => {
  queryPopUp.value = "";
  fromPopUp.value = 0;
  fetchRecipeDataAsync("");
  searchPopUp();
  isSearchPopupExitEnable.value = false;
};
const addRecipeToContent = () => {
  if (dummySelectedRecipe.value.length > 0) {
    selectedRecipe.value.push(dummySelectedRecipe.value[0]);
  }
  closeConfirmModal();
};

const selectHeroRecipe = (recipe) => {
  contentCTALink.value = `rocheapp://learn?isin=${recipe.isin}`;
  if (selectedRecipe.value) {
    selectedRecipe.value.isAdded = false;
  }
  recipe.isAdded = true;
  if (selectedRecipe.value.length > 0) {
    selectedRecipe.value = [];
    dummySelectedRecipe.value = [];
  }
  dummySelectedRecipe.value.push(recipe);
  selectedArticle.value = [];
  selectedItem.value = true;
};
const handleTypeInput = (event) => {
  const refs = ["contentNameRef", "descriptionNotes", "contentCTALink"];

  if (refs.some(ref => getRef(ref)?.contains(event.target))) {
    isCampaignModified.value = true;
  }
};
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onUnmounted(() => {
  const events = [$keys.KEY_NAMES.INPUT, $keys.KEY_NAMES.CLICK, $keys.KEY_NAMES.KEYUP];
  const handlers = [handleTypeInput, handleESCClickOutside];

  events.forEach((event, index) => {
    document.removeEventListener(event, handlers[index]);
  });
});
</script>
