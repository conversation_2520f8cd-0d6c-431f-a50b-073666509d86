<template>
  <content-wrapper wrapper-classes="padding-zero" :is-body-loading="isPageLoading">
    <div v-if="!isPageLoading" class="main-content">
      <div class="dynamic-hero-form-edit-id">ID: {{ adviceUuid }}</div>
      <dynamicHeroAdviceHeader
        :image="image"
        :isPageOverviewVisible="isPageOverviewVisible"
        :isReplaceLiveHero="isReplaceLiveHero"
        :isCampaignModified="isCampaignModified"
        :adviceName="adviceName"
        :adviceText="adviceText"
        :uploadImagePercentage="uploadImagePercentage"
        :isLiveHero="isLiveHero"
        @back="backToDynamicHeroList"
        @save="saveAdviceForm"
        @replace="replaceAdviceForm"
      />
      <div class="advice-intro-section">
        <dynamicHeroAdviceIntro
          :title="$t('DYNAMIC_HERO.ADVICE_FORM')"
          :startDateLabel="'Start date:'"
          :isReplaceLiveHero="isReplaceLiveHero"
          :isLiveHero="isLiveHero"
          :isLiveHeroReplaced="isReplaceLiveHero"
          v-model:selectedDate="selectedDate"
          :disabledDates="disabledDates"
          :isRange="false"
          :markers="markers"
          @date-click="handleDateClick"
          @day-hover="call()"
        />
        <div class="image-input-container">
          <processImage
            :image="image"
            :uploadImagePercentage="uploadImagePercentage"
            :loadedImageSize="loadedImageSize"
            :uploadImageSize="uploadImageSize"
            :checkUploadedFiles="checkUploadedFiles"
            :uploadSameImageVideo="uploadSameImageVideo"
          />
          <dynamicHeroAdviceForm
            v-model:adviceName="adviceName"
            @scheduleToggle="scheduleToggle"
            :selectedDate="selectedDate"
            v-model:isAdviceStatusVisible="isAdviceStatusVisible"
            :isReplaceLiveHero="isReplaceLiveHero"
            :isLiveHero="isLiveHero"
            :isAdviceNameFocus="isAdviceNameinFocus"
            :deleteEvent="deleteEvent"
            :isEditPage="true"
            :hideAdviceTip="hideAdviceTip"
            :checkAdviceName="checkAdviceName"
          />
        </div>
      </div>
    </div>
    <dynamicHeroAdviceContent
      :isPageLoading="isPageLoading"
      :isLiveHero="isLiveHero"
      :isHeroPreview="isHeroPreview"
      :checkAdviceText="checkAdviceText"
      v-model:adviceText="adviceText"
      @update:isHeroPreview="[(isHeroPreview = $event), (isCampaignModified = true)]"
    />
    <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !isOffline"
      :closeModal="closeModal"
      :acceptedFile="'jpg/ jpeg/ png'"
      :video="false"
      :image="true"
      :zip="false"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
    />
    <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
    />
    <saveModal
      v-if="isSaveNewsModalVisible"
      :closeModal="closeModal"
      :saveAndPublishFunction="patchDynamicHeroAdviceDataAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your Advice form?'"
      :imageName="saveImage"
    />
    <deleteModal
      v-if="isAdviceDeleted"
      :closeModal="closeModal"
      :productInfoTitle="'Delete the Advice Form?'"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="'Advice Form?'"
      :deleteItem="deleteEditDynamicHeroDataAsync"
      :availableLanguage="0"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduledDateModalVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :markers="markers"
      :PatchScheduledHero="patchDynamicHeroAdviceDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      @date-click="handleDateClickPopup"
    />
    <savingModal v-show="isNewsSaving" :status="'saving'" />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="patchDynamicHeroAdviceDataAsync"
      :closeReplacement="closeModal"
    />
    <updatingLiveHero
      v-if="isLiveHeroSaveDynamicHero"
      :closeModal="closeModal"
      :callConfirm="patchDynamicHeroAdviceDataAsync"
      :updatingText="'Please, confirm the updating live Hero'"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted, getCurrentInstance, watch } from "vue";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import updatingLiveHero from "@/components/updating-live";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal.vue";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import sizeLimit from "@/components/size-limit.vue";
import ArticlesService from "@/services/ArticlesService";
import deletingModal from "@/components/deleting-modal";
import savingModal from "@/components/saving-modal";
import deleteModal from "@/components/delete-modal";
import axios from "axios";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useProjectLang } from "@/composables/useProjectLang";
import saveImage from "@/assets/images/quiz-form.png"
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import dynamicHeroAdviceHeader from "../components/pages/dynamic-hero/dynamic-hero-advice-header.vue";
import dynamicHeroAdviceIntro from "../components/pages/dynamic-hero/dynamic-hero-advice-intro.vue";
import processImage from "../components/pages/dynamic-hero/process-image.vue";
import dynamicHeroAdviceForm from "../components/pages/dynamic-hero/dynamic-hero-advice-form.vue";
import dynamicHeroAdviceContent from "../components/pages/dynamic-hero/dynamic-hero-advice-content.vue";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useConnectionStatus } from '~/composables/useConnectionStatus';

const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  getHeroUUIDAsync,
  dynamicHeroUUIDData,
  patchDynamicHeroDataAsync,
  deleteDynamicHeroDataAsync
} = useDynamicHeroStore();
const heroData = ref([
  {template: 'Advice'}
]);
const router = useRouter();
const { watchReactiveValue } = useWatcherUtils();
const { triggerLoading, routeToPage, useCalendarMarkers, checkDuplicate, processScheduledElement, isScheduledWithPublishDate, getDisabledDates, getDisableList } = useCommonUtils();
const { convertTimeStamp } = useTimeUtils();
const { readyProject } = useProjectLang();
const { isInnitAdmin } = useInnitAuthStore();
const { getRef } = useRefUtils();
const store = useStore();
const route = useRoute();
const disableList = ref([]);
const includeId = ref(0);
const todos = ref([]);
const heroID = ref("");
const lang = ref("");
const isNewsSaving = ref(false);
const isAdviceNameinFocus = ref(false);
const isHeroPreview = ref(false);
const scheduleDateConfirm = ref("");
const isPageOverviewVisible = ref(false);
const isScheduledDateModalVisible = ref(false);
const isSaveNewsModalVisible = ref(false);
const isDeletingModalVisible = ref(false);
const isConfirmModalVisible = ref(false);
const uploadImageConfirm = ref("");
const isCampaignModified = ref(false);
const isAdviceDeleted = ref(false);
const isLiveHero = ref(false);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const selectedDate = ref("");
const adviceText = ref("");
const isAdviceStatusVisible = ref(false);
const adviceName = ref("");
const imageFile = ref([]);
const cancelImage = ref({});
const imageResponseUrl = ref("");
const imageFileName = ref("");
const uploadImagePercentage = ref(0);
const isInvalidImageModalVisible = ref(false);
const isUploadingImagePopup = ref(false);
const isMaxImagePopupVisible = ref(false);
const createDuplicate = ref(false);
const isPageLoading = ref(false);
const scheduleDate = ref("");
const adviceUuid = ref("");
const disabledDates = ref([]);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const isAdminCheck = ref(false);
const isLiveHeroSaveDynamicHero = ref(false);
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { isOffline } = useConnectionStatus();

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      initializeDataAsync();
      addEventListeners();
    }
  });
});

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  lang.value = store.getters['userData/getDefaultLang'];
  await getDynamicHeroDataAsync();
  await getEditDynamicHeroDataAsync();

  const sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isReplaceLiveHero.value = true;
  }
  if (sourceUrl.includes("overviewAdvice")) {
    isPageOverviewVisible.value = true;
  }
  if (sourceUrl.includes("create-duplicate")) {
    createDuplicate.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
  disabledDates.value = await getDisabledDates().value;
  disableList.value = await getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString()),
    };
    todos.value.push(newTodo);
  }
  includeId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const checkAdviceText = () => {
  if (!adviceText.value.trim().length) {
    adviceText.value = "";
  } else {
    adviceText.value = adviceText.value.replace(/\s+/g, " ");
  }
};
const deleteEvent = () => {
  isAdviceDeleted.value = true;
};
const deleteEditDynamicHeroDataAsync = async () => {
  isAdviceDeleted.value = false;
  isDeletingModalVisible.value = true;
  try {
    await deleteDynamicHeroDataAsync({ uuid: route.query[QUERY_PARAM_KEY.UUID] });
    routeToPage("dynamic-hero");
    triggerLoading("newDeletedSuccess");
  } catch (error) {
    console.error("Error in deleteEditDynamicHeroDataAsync:", error);
  } finally {
    closeModal();
    isDeletingModalVisible.value = false;
  }
};
const checkAdviceName = () => {
  const name = getRef("adviceNameField");
  if (name.scrollWidth > name.clientWidth && name !== document.activeElement && adviceName.value.trim().length) {
    isAdviceNameinFocus.value = true;
  }
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isAdviceStatusVisible.value = newStatus;
};
const hideAdviceTip = () => {
  isAdviceNameinFocus.value = false;
};
const replaceAdviceForm = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error(
      `${$keys.KEY_NAMES.ERROR_IN} getDynamicHeroDataAsync:`,
      error
    );
  }
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
  isAdviceStatusVisible.value = true;
  isCampaignModified.value = true;
};
const getUUIDAsync = async () => {
  try {
    await getHeroUUIDAsync({ lang: lang.value });
    const response = await dynamicHeroUUIDData.value;
    heroID.value = response?.uuid;
  } catch (error) {
    console.error("Error in getUUIDAsync:", error);
  }
};
const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  imageFile.value = evnt.target.files || evnt.srcElement.files;
  const fileType = imageFile.value[0].type.split("/")[0];

  if (fileType == "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};
const getEditDynamicHeroDataAsync = async () => {
  if (!route.query[QUERY_PARAM_KEY.UUID]) {
    return;
  }

  isPageLoading.value = true;

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });
    const response = await editDynamicHeroDataList.value;

    if (Object.keys(response).length) {
      processResponse(response);
    }
  } catch (error) {
    isPageLoading.value = false;
    console.error("Error in getEditDynamicHeroDataAsync:", error);
  }
};

const processResponse = (response) => {
  setBasicInfo(response);
  setHeroState(response);
  setImageData(response);

  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
};

const setBasicInfo = (response) => {
  isAdviceStatusVisible.value = !!response.publishDate;
  adviceName.value = response.title || "";
  if (!createDuplicate.value) {
    selectedDate.value = response.publishDate ? convertTimeStamp(response.publishDate) : "";
  }
  heroID.value = response.uuid || "";
  adviceUuid.value = response.uuid || "";
};

const setHeroState = (response) => {
  if (response.state) {
    isLiveHero.value = response.state === "live" && !createDuplicate.value;
    isHeroPreview.value = response.preview || false;
    if (response.state === "draft") {
      selectedDate.value = "";
    }
  }
};

const setImageData = (response) => {
  if (response.data) {
    adviceText.value = response.data.body || '';
    image.value = response.data.image || '';
    imageResponseUrl.value = response.data.image || '';
  }
};

const setSavingStatus = (status) => {
  isNewsSaving.value = status;
  isLiveHeroSaveDynamicHero.value = !status;
};

const handleApiCall = async (payload) => {
  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });

    handlePostDispatch();
    closeModal();
    navigateToDynamicHero();
  } catch (error) {
    isNewsSaving.value = false;
    console.error('Error in patchDynamicHeroAdviceDataAsync:', error);
  }
};

const patchDynamicHeroAdviceDataAsync = async () => {
  setSavingStatus(true);
  const scheduleDate = calculateScheduleDate();
  const payload = createPayloadPatch(scheduleDate);
  await handleApiCall(payload);
  setSavingStatus(false);
};
const calculateScheduleDate = () => {
  let scheduleDateVal;
  if (isReplaceLiveHero.value || isLiveHero.value) {
    isAdviceStatusVisible.value = true;
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
    scheduleDateVal = Math.floor(selectedDate.value.getTime() / 1000);
  } else {
    const date = new Date(selectedDate.value);
    scheduleDateVal = Math.floor(date.getTime() / 1000);
  }
  return scheduleDateVal;
};
const createPayloadPatch = (scheduleDateVal) => ({
  title: adviceName.value?.trim() ?? '',
  template: 'advice',
  publishDate: !selectedDate.value && !isAdviceStatusVisible.value ? '0' : scheduleDateVal,
  image: imageResponseUrl.value?.replace(/\?.*/, '') ?? '',
  data: {
    body: adviceText.value ?? '',
    image: imageResponseUrl.value?.replace(/\?.*/, '') ?? '',
  },
  state: !selectedDate.value && !isAdviceStatusVisible.value ? 'draft' : 'scheduled',
  preview: isHeroPreview.value ?? false,
});
const handlePostDispatch = () => {
  isNewsSaving.value = false;

  const shouldSaveAdvice = !isAdviceStatusVisible.value || !selectedDate.value;
  const isLiveOrReplaced = isLiveHero.value || isReplaceLiveHero.value;
  const isAdviceScheduled = !isLiveHero.value && !isReplaceLiveHero.value;

  if (shouldSaveAdvice && !isLiveOrReplaced) {
    triggerLoading('AdviceSaved');
  } else if (isAdviceScheduled) {
    triggerLoading('AdviceScheduled');
  } else if (isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading('contentLive');
  } else if (isReplaceLiveHero.value) {
    triggerLoading('heroReplaced');
  }
};
const navigateToDynamicHero = () => {
  router.push('dynamic-hero');
};
const uploadFiles = () => {
  isCampaignModified.value = true;
  if (imageFile.value.length) {
    imageFileName.value = imageFile.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!imageFileName.value.match(reg)) {
      imageFile.value = [];
      imageFileName.value = '';
      isInvalidImageModalVisible.value = true;
      return;
    }
    const fileSize = imageFile.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
      const element = getRef('productVideo');
      element?.blur();
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = imageFile.value;
      imageFile.value = [];
      return;
    } else if (size >= 15 * 1024 * 1024) {
      const element = getRef('productVideo');
      element.blur();
      imageFile.value = [];
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener('load', async () => {
        image.value = reader.result;
        if (image.value) {
          loadedImageSize.value = 0;
          uploadImagePercentage.value = 1;
          await uploadImageAsync();
        }
      });
      if (imageFile.value[0]) {
        reader.readAsDataURL(imageFile.value[0]);
      }
    }
  }
};

const uploadImageAsync = async () => {
  if (imageFile.value && image.value) {
    if (!heroID.value) {
      await getUUIDAsync();
    }
    const reader = new FileReader();
    reader.addEventListener(
      "load",
      async () => {
        const extension = imageFile.value[0].type.split("/")[1];
        const params = {
          entity: "article",
          content: "image",
          extension: extension,
          lang: lang.value,
          public: true,
        };
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: heroID.value,
          params,
        });
        const response = store.getters["preSignedUrl/getPreSignedUrl"];
        imageResponseUrl.value = response?.data?.url || "";
        await uploadImageFile(response?.data?.url || "", imageFile.value[0]);
        await ArticlesService.upload(response?.data?.url || "", imageFile.value[0]);
      },
      false
    );
    if (imageFile.value[0]) {
      reader.readAsDataURL(imageFile.value[0]);
    }
  }
};

const continueImage = () => {
  imageFile.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener('load', async () => {
    image.value = reader.result;
    if (image.value) {
      loadedImageSize.value = 0;
      uploadImagePercentage.value = 1;
      await uploadImageAsync();
    }
  });
  if (imageFile.value[0]) {
    reader.readAsDataURL(imageFile.value[0]);
  }
};
const uploadImageFile = async (url, file) => {
  cancelImage.value = axios?.CancelToken?.source() || null;
  try {
    await axios.put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value?.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = parseInt(
          Math.round((progressEvent.loaded / progressEvent.total) * 100)
        );
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    });
  } catch (e) {
    if (axios.isCancel(e)) {
      console.error("Image request canceled.");
    } else {
      console.error(e);
    }
  }
};
const uploadedImageFunction = (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    setTimeout(() => {
      uploadImagePercentage.value = 100;
    }, 2000);
  }
};
const uploadSameImageVideo = (event) => {
  event.target.value = "";
};

const saveAdviceForm = () => {
  if (isLiveHero.value) {
    isLiveHeroSaveDynamicHero.value = true;
  } else {
    isSaveNewsModalVisible.value = true;
    if (selectedDate.value !== "" && isAdviceStatusVisible.value) {
      isScheduledDateModalVisible.value = true;
      scheduleDateConfirm.value = selectedDate.value;
    }
  }
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading("CAMPAIGN_MODIFIED", isCampaignModified.value);
  if (isPageOverviewVisible.value) {
    routeToPage("overview");
  } else {
    routeToPage("dynamic-hero");
  }
};
const closeModal = () => {
  scheduleDateConfirm.value = scheduleDate.value;
  if (!isReplaceLiveHero.value) {
    isReplaceLiveHero.value = false;
  }
  isReplacementConfirmPopupVisible.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isNewsSaving.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isDeletingModalVisible.value = false;
  isAdviceDeleted.value = false;
  isSaveNewsModalVisible.value = false;
  isScheduledDateModalVisible.value = false;
};

const handleTypeInput = (event) => {
  if (["adviceNameField", "adviceTextField"].some((refName) => getRef(refName)?.contains(event.target))) {
    isCampaignModified.value = true;
  }
};
watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onUnmounted(() => {
  document.removeEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
</script>
