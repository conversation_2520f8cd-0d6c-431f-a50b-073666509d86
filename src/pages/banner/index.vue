<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading">
      <template v-slot:title>
        <span>{{ $t("BANNER.BANNER_LIST") }}</span>
      </template>

      <template v-slot:head>
        <button
          v-if="!hasReloadTextCheck"
          type="button"
          class="btn-green"
          @click="openNewBannerPopup()"
        >
          {{ $t("BANNER.NEW_BANNER") }}
        </button>
      </template>

      <no-result-found
        v-if="!isLoading && !bannerListData.length"
        :isReloadRequired="hasReloadTextCheck"
        :noResultText="$t('BANNER.CREATE_OR_RELOAD')"
      ></no-result-found>

      <block-wrapper
        is-transparent
        v-if="!isLoading && bannerListData && bannerListData.length > 0"
      >
        <div class="banner-list-table-container">
          <simple-table
            :column-names="columnNames"
            :column-keys="columnKeys"
            :data-source="combinedBannerData"
            table-class="banner-table"
            table-column-class="banner-body"
          >
            <template v-slot:publishDate="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-dates-ids">
                  <div class="banner-details">
                    <span class="publishdate">
                      {{
                        data.publishDate
                          ? convertTimeStamp(data.publishDate)
                          : "no date"
                      }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
            <template v-slot:endDate="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-dates-ids">
                  <div class="banner-details">
                    <span class="publishdate">
                      {{
                        data.endDate
                          ? convertTimeStamp(data.endDate)
                          : "no date"
                      }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
            <template v-slot:uuid="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-isin">
                  <span>{{ data.uuid || "" }}</span>
                </div>
              </template>
            </template>
            <template v-slot:title="{ data }">
              <template v-if="data.isToggleRow">
                <div
                  class="toggle-row-cell view-expire-hero-container"
                  @click="toogleExpiredList"
                >
                  <div v-if="!data.hasExpiredBannerData" class="text-expire">
                    {{ $t("BANNER.VIEW_EXPIRED_BANNERS") }}
                  </div>
                  <div v-else class="text-expire">
                    {{ $t("BANNER.HIDE_EXPIRED_BANNERS") }}
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="banner-title">
                  <div v-if="data.preview" class="preview-type">
                    <span class="preview-text">{{
                      $t("BUTTONS.PREVIEW_BUTTON")
                    }}</span>
                  </div>
                  <div class="banner-type-section">
                    <div class="banner-type-text">
                      <span v-if="data.type == 'all_text'">{{
                        $t("BANNER.ALL_TEXT")
                      }}</span>
                      <span v-if="data.type == 'text_button'">{{
                        $t("BANNER.TEXT_BUTTON")
                      }}</span>
                    </div>
                  </div>
                  <div class="banner-title-text">
                    {{ data.title || "" }}
                  </div>
                </div>
              </template>
            </template>
            <template v-slot:lastUpdate="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-dates-ids">
                  <div class="banner-details">
                    <span>{{
                      data.lastUpdate ? convertTimeStamp(data.lastUpdate) : ""
                    }}</span>
                  </div>
                </div>
              </template>
            </template>
            <template v-slot:status="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-state-menu-option">
                  <badge
                    :label="$t(STATE_MAPPING[data.state].tKey)"
                    :badge-type="STATE_MAPPING[data.state].badgeType"
                    :img-src="STATE_MAPPING[data.state].icon"
                  ></badge>
                </div>
              </template>
            </template>
            <template v-slot:actions="{ data }">
              <template v-if="data.isToggleRow">
                <div class="toggle-row-empty"></div>
              </template>
              <template v-else>
                <div class="banner-menu">
                  <body-menu
                    :actions="
                      data.state === 'expired'
                        ? prepareExpiredActions()
                        : prepareActions(data.state)
                    "
                    :isFullText="true"
                    @call-actions="(key) => callAction(key, data)"
                  ></body-menu>
                </div>
              </template>
            </template>
          </simple-table>
        </div>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { storeToRefs } from "pinia";
import { useNuxtApp } from "#app";

import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import ProcessModal from "@/components/modals/process-modal.vue";
import ConfirmModal from "@/components/modals/confirm-modal.vue";
import BannerTypeModal from "@/components/pages/banner/banner-type-modal.vue";
import BannerScheduleModal from "@/components/pages/banner/banner-schedule-modal.vue";
import NoResultFound from "@/components/no-result-found.vue";
import Badge from "@/components/badge/badge.vue";
import BodyMenu from "@/components/body-menu.vue";

import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useProjectLang } from "@/composables/useProjectLang";
import { useBannerStore } from "@/stores/banner";
import { useBaseModal } from "@/composables/useBaseModal";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { PROCESS_MODAL_TYPE } from "@/models/process-modal.model";
import { CONFIRM_MODAL_TYPE } from "@/models/confirm-modal.model";
import { STATE_MAPPING } from "@/сonstants/state-mapping";

const router = useRouter();
const store = useStore();
const { $keys, $t } = useNuxtApp();
const { readyProject } = useProjectLang();
const { triggerLoading, useCalendarMarkers } = useCommonUtils();
const { getTimeStamp, formatDateToReadableString, convertTimeStamp } =
  useTimeUtils();
const bannerStore = useBannerStore();
const { bannerList } = storeToRefs(bannerStore);

const { openModal, closeModal } = useBaseModal({
  BannerTypeModal: BannerTypeModal,
  BannerScheduleModal: BannerScheduleModal,
  BannerConfirmModal: ConfirmModal,
  BannerProcessModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});

const selectBannerTypes = ref([
  { key: "all text", options: "All Text" },
  { key: "text & button", options: "Text & Button" },
]);
const hasExpiredBannerData = ref(false);
const bannerListData = ref([]);
const selectedBannerUUID = ref("");
const bannerStateType = ref("");
const selectedBanner = ref({});
const hasReloadTextCheck = ref(false);
const expiredBannerCount = ref(0);
const dropdownItem = ref([]);
const startDate = ref("");
const endDate = ref("");
const bannerStartDate = ref(0);
const bannerEndDate = ref(0);
const range = ref({
  start: null,
  end: null,
});
const disabledDates = ref([]);
const disableList = ref([]);
const disableBetweenDates = ref([]);
const startEndArray = ref([]);
const lang = ref("");
const isLoading = ref(true);

const { markers } = useCalendarMarkers(disableList, "rawTitle");

const columnNames = [
  $t("COLLECTION.PUBLISHED_DATE"),
  $t("COLLECTION.END_DATE"),
  $t("BANNER.BANNER_ID"),
  $t("BANNER.BANNER_TITLE"),
  $t("MODIFIED"),
  $t("COMMON.STATUS"),
  "",
];
const columnKeys = [
  "publishDate",
  "endDate",
  "uuid",
  "title",
  "lastUpdate",
  "status",
  "actions",
];
const combinedBannerData = computed(() => {
  const nonExpired = bannerListData.value.filter(
    (item) => item.state !== "expired"
  );
  const expired = bannerListData.value.filter(
    (item) => item.state === "expired"
  );

  expiredBannerCount.value = expired.length;

  const toggleRow = {
    isToggleRow: true,
    hasExpiredBannerData: hasExpiredBannerData.value,
    expiredBannerCount: expiredBannerCount.value,
  };

  let result = [...nonExpired];

  if (expiredBannerCount.value > 0) {
    result.push(toggleRow);
  }

  if (hasExpiredBannerData.value) {
    result = [...result, ...expired];
  }

  return result;
});

const callAction = (key, item) => {
  switch (key) {
    case "edit":
      editBannerForm(item.uuid);
      break;
    case "delete":
      deleteBannerPopUpOpen(item.uuid);
      break;
    case "duplicate":
      createDuplicateBannerForm(item.uuid);
      break;
    case "schedule":
      scheduleBanner(item);
      break;
    case "unschedule":
      unscheduleBanner(item.uuid);
      break;
    default:
      break;
  }
  displayOption(item);
};

const prepareActions = (state) => {
  return [
    {
      isDisable: state !== "scheduled",
      key: "unschedule",
      label: "Unschedule",
    },
    {
      isDisable: state !== "draft",
      key: "schedule",
      label: "Schedule",
    },
    {
      isDisable: false,
      key: "edit",
      label: $t("BUTTONS.EDIT_BUTTON"),
    },
    {
      isDisable: false,
      key: "delete",
      label: $t("BUTTONS.DELETE_BUTTON"),
    },
    {
      isDisable: false,
      key: "duplicate",
      label: "Create Duplicate",
    },
  ];
};

const prepareExpiredActions = () => {
  return [
    {
      isDisable: false,
      key: "duplicate",
      label: "Create Duplicate",
    },
    {
      isDisable: false,
      key: "delete",
      label: $t("BUTTONS.DELETE_BUTTON"),
    },
  ];
};

const editBannerForm = (uuid) => {
  router.push({
    path: `/banner/${uuid}`,
  });
};

const createDuplicateBannerForm = (uuid) => {
  router.push({
    path: `/banner/${uuid}`,
    query: { duplicate: true },
  });
};

const openNewBannerPopup = () => {
  let modalSelectedType = "all text";

  openModal({
    name: "BannerTypeModal",
    props: {
      bannerTypes: selectBannerTypes.value,
      selectedType: modalSelectedType,
    },
    onCallback: (key, value) => {
      if (key === "update:selectedType") {
        modalSelectedType = value;
      }
    },
    onClose: (response) => {
      if (typeof response === "string") {
        let bannerType;

        if (response === "text & button") {
          bannerType = "text-button";
        } else {
          bannerType = "all-text";
        }
        router.push({
          path: "/banner/create",
          query: { [QUERY_PARAM_KEY.DATA]: bannerType },
        });
      } else if (response === true) {
        let bannerType;

        if (modalSelectedType === "text & button") {
          bannerType = "text-button";
        } else {
          bannerType = "all-text";
        }

        router.push({
          path: "/banner/create",
          query: { [QUERY_PARAM_KEY.DATA]: bannerType },
        });
      }
    },
  });
};

const resetSelectedItems = () => {
  if (bannerListData.value.length > 0) {
    bannerListData.value.forEach((data) => {
      data.selectedText = "";
    });
  }
};

const toogleExpiredList = () => {
  hasExpiredBannerData.value = !hasExpiredBannerData.value;
};

const deleteBannerPopUpOpen = (uuid) => {
  selectedBannerUUID.value = uuid;
  bannerListData.value.forEach((data) => {
    if (uuid === data.uuid) {
      data.selectedText = "DELETE";
    }
  });

  openModal({
    name: "BannerConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: $t("BANNER.BANNER_DELETE"),
      description: $t("BANNER.BANNER_DELETE_MESSAGE"),
    },
    onClose: (response) => response && deleteBannerAsync(uuid),
  });
};

const deleteBannerAsync = async (uuid) => {
  openModal({
    name: "BannerProcessModal",
    props: { modalType: PROCESS_MODAL_TYPE.DELETING },
  });

  try {
    await bannerStore.deleteBannerAsync({
      uuid: selectedBannerUUID.value || uuid,
      lang: lang.value,
    });
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch (error) {
    console.error("Error in deleteBannerAsync", error);
  } finally {
    closeModal("BannerProcessModal");
    isLoading.value = true;
    await getBannerListDataAsync();
    isLoading.value = false;
    resetSelectedItems();
  }
};

const displayOption = (item) => {
  dropdownItem.value = item;
};

const handleDateChange = (newValue) => {
  newValue[0]?.setHours(0, 0, 0, 0);
  newValue[1]?.setHours(23, 59, 59, 999);
  bannerEndDate.value = getTimeStamp(newValue[1]);
  bannerStartDate.value = getTimeStamp(newValue[0]);
};

const scheduleBanner = (item) => {
  startDate.value = "";
  endDate.value = "";
  bannerStartDate.value = 0;
  bannerEndDate.value = 0;
  range.value.start = null;
  range.value.end = null;

  selectedBanner.value = {};
  selectedBannerUUID.value = item.uuid;

  bannerListData.value.forEach((data) => {
    if (item.uuid === data.uuid) {
      selectedBanner.value = data;
      data.selectedText = "SCHEDULE";

      if (data.publishDate && data.endDate) {
        const startDateObj = new Date(data.publishDate * 1000);
        const endDateObj = new Date(data.endDate * 1000);

        startDate.value = formatDateToReadableString(startDateObj);
        endDate.value = formatDateToReadableString(endDateObj);

        bannerStartDate.value = data.publishDate;
        bannerEndDate.value = data.endDate;

        range.value.start = startDateObj;
        range.value.end = endDateObj;
      }
    }
  });

  bannerStateType.value = "scheduled";

  openModal({
    name: "BannerScheduleModal",
    props: {
      range: range.value,
      startDate: startDate.value,
      endDate: endDate.value,
      markers: Array.isArray(markers) ? markers : markers.value || [],
      disabledDates: disabledDates.value,
      onDateChange: handleDateChange,
    },
    onCallback: (key, value) => {
      if (key === "update:range") {
        range.value = value;
      }
    },
    onClose: (response) => {
      if (response && bannerStartDate.value && bannerEndDate.value) {
        patchBannerDataAsync();
      } else {
        resetSelectedItems();
      }
    },
  });
};

const unscheduleBanner = (uuid) => {
  selectedBannerUUID.value = uuid;

  bannerListData.value.forEach((data) => {
    if (uuid === data.uuid) {
      selectedBanner.value = data;
      data.selectedText = "UNSCHEDULE";
    }
  });

  bannerStartDate.value = 0;
  bannerEndDate.value = 0;
  bannerStateType.value = "draft";

  openModal({
    name: "BannerConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DRAFT,
      title: "Please confirm unscheduling Banner form",
      descriptionRed: "Banner form will be moved to a draft.",
      confirmBtnLabel: $t("BUTTONS.CONFIRM_BUTTON"),
    },
    onClose: (response) => response && patchBannerDataAsync(),
  });
};

const patchBannerDataAsync = async () => {
  openModal({
    name: "BannerProcessModal",
    props: {
      modalType:
        bannerStateType.value === "scheduled"
          ? PROCESS_MODAL_TYPE.SCHEDULING
          : PROCESS_MODAL_TYPE.UNSCHEDULING,
    },
  });

  try {
    const payload = {
      uuid: selectedBanner.value.uuid || "",
      title: selectedBanner.value?.title || "",
      state: bannerStateType.value || "draft",
      publishDate: bannerStartDate.value || 0,
      endDate: bannerEndDate.value || 0,
      ctaText: selectedBanner.value?.ctaText || "",
      ctaLink: selectedBanner.value?.ctaLink || "",
      preview: selectedBanner.value?.preview || false,
    };

    if (bannerStateType.value === "draft") {
      delete payload.publishDate;
      delete payload.endDate;
    }

    if (!payload.ctaLink || !payload.ctaText) {
      delete payload.ctaLink;
      delete payload.ctaText;
    }

    await bannerStore.patchBannerAsync({
      payload,
      uuid: selectedBannerUUID.value,
      lang: lang.value,
    });

    if (
      bannerStartDate.value &&
      bannerEndDate.value &&
      bannerStateType.value === "scheduled"
    ) {
      triggerLoading("bannerFormScheduled");
    }
    if (bannerStateType.value === "draft") {
      triggerLoading("bannerFormUnscheduled");
    }

    closeModal("BannerProcessModal");
    isLoading.value = true;
    await getBannerListDataAsync();
    isLoading.value = false;
  } catch (error) {
    console.error("Error in patchBannerDataAsync", error);
    closeModal("BannerProcessModal");
  } finally {
    resetSelectedItems();
  }
};

const getBannerListDataAsync = async () => {
  resetBannerData();

  try {
    await bannerStore.getBannerListAsync({
      lang: lang.value,
    });

    processBannerData();
    processDisabledDates();
  } catch (error) {
    handleFetchError(error);
  }
};

const resetBannerData = () => {
  bannerListData.value = [];
  disabledDates.value = [];
  disableList.value = [];
  expiredBannerCount.value = 0;
  startEndArray.value = [];
};

const processBannerData = () => {
  bannerListData.value = getSortedBanners(bannerList.value);

  bannerListData.value.forEach((item) => {
    if (isBannerActive(item)) {
      handleActiveBanner(item);
    }
    if (item?.state === $keys.KEY_NAMES.DRAFT) {
      clearDraftDates(item);
    }
    initializeBannerFlags(item);
    if (item?.state === $keys.KEY_NAMES.EXPIRED) {
      expiredBannerCount.value++;
    }
  });
};

const isBannerActive = (item) => {
  return (
    (item.state === $keys.KEY_NAMES.SCHEDULED ||
      item.state === $keys.KEY_NAMES.LIVE) &&
    item.publishDate &&
    item.endDate
  );
};

const handleActiveBanner = (item) => {
  const startDisableDate = new Date(item.publishDate * 1000);
  startDisableDate.setHours(0, 0, 0, 0);

  const endDisableDate = new Date(item.endDate * 1000);
  endDisableDate.setHours(23, 59, 59, 999);

  const tempDisableDate = getBetweenDates(startDisableDate, endDisableDate);

  startEndArray.value.push({
    start: startDisableDate,
    end: endDisableDate,
  });

  disabledDates.value.push(...tempDisableDate);
  addToDisableList(tempDisableDate, item);
};

const addToDisableList = (tempDisableDate, item) => {
  tempDisableDate.forEach((ele) => {
    disableList.value.push({
      date: new Date(ele),
      title: item.title || "",
      state: item.state || "",
    });
  });
};

const clearDraftDates = (item) => {
  item.publishDate = 0;
  item.endDate = 0;
};

const initializeBannerFlags = (item) => {
  item.selectedText = "";
  item.dropdown = false;
};

const processDisabledDates = () => {
  const sortedDateStrings = sortDates(disabledDates.value);
  const mainDisabledDates = bannerFormat(sortedDateStrings);
  const finalDisableDate = findDateGap(mainDisabledDates);
  disableBetweenDates.value = finalDisableDate.map((data) => new Date(data));
};

const sortDates = (dateStrings) => {
  return dateStrings.sort(
    (optionA, optionB) => new Date(optionA) - new Date(optionB)
  );
};

const bannerFormat = (sortedDateStrings) => {
  return sortedDateStrings.map((data) =>
    formatDateToReadableString(new Date(data))
  );
};

const handleFetchError = (error) => {
  console.error(`${$keys.KEY_NAMES.ERROR_IN} getBannerListDataAsync`, error);
  hasReloadTextCheck.value = true;
};

const getSortedBanners = (response) => {
  const stateOrder = { live: 1, scheduled: 2, draft: 3, expired: 4 };
  return response.slice().sort((a, b) => {
    const stateComparison = stateOrder[a.state] - stateOrder[b.state];

    if (stateComparison !== 0) return stateComparison;

    if (
      a.state === "live" ||
      a.state === "scheduled" ||
      a.state === "expired"
    ) {
      return a.publishDate - b.publishDate;
    }

    return b.lastUpdate - a.lastUpdate;
  });
};

const getBetweenDates = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  return Array.from({ length: days }, (_, index) => {
    const currentDate = new Date(start);
    currentDate.setDate(start.getDate() + index);
    return currentDate;
  });
};

const findDateGap = (dates) => {
  const missingBetweenDate = [];
  for (let i = 1; i < dates.length; i++) {
    const currentDate = new Date(dates[i]);
    const previousDate = new Date(dates[i - 1]);
    const timeDifference = currentDate - previousDate;
    const daysDifference = timeDifference / (1000 * 60 * 60 * 24);
    if (daysDifference === 2) {
      for (let j = 1; j < daysDifference; j++) {
        const missingDate = new Date(previousDate);
        missingDate.setDate(previousDate.getDate() + j);
        missingBetweenDate.push(missingDate.toDateString());
      }
    }
  }
  return missingBetweenDate;
};

const handleKeyUp = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};

const handleDocumentClick = (event) => {
  if (dropdownItem.value && dropdownItem.value.dropdown) {
    const isClickOnDots =
      event.target.classList.contains("dot") ||
      event.target.classList.contains("banner-menu-dots");
    const isClickInDropdown = event.target.closest(".banner-menu-box");

    if (!isClickOnDots && !isClickInDropdown) {
      dropdownItem.value.dropdown = false;

      dropdownItem.value = null;
    }
  }
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      lang.value = store.getters["userData/getDefaultLang"];
      await getBannerListDataAsync();
      isLoading.value = false;

      document.addEventListener($keys.KEY_NAMES.KEYUP, handleKeyUp);
      document.addEventListener($keys.KEY_NAMES.CLICK, handleDocumentClick);
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener($keys.KEY_NAMES.CLICK, handleDocumentClick);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleKeyUp);
});
</script>
