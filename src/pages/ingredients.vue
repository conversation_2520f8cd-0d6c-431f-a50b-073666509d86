<template>
<client-only>
  <content-wrapper :is-body-loading="tm_isLoading">
    <template v-slot:title><span>{{ $t('INGREDIENT.TITLE') }}</span></template>

    <block-wrapper is-transparent>
      <template v-if="isSearchEnabled" v-slot:title>{{ $t('COMMON.SEARCH_RESULT') }}</template>

      <simple-table
        v-if="tm_rows.length"
        :column-keys="columnKeys"
        :column-names="columnNames"
        :data-source="tm_rows"
        table-class="ingredient-table"
      >
        <template v-slot:name="props">
          <div class="ingredient-table-name">
            <div>
              <span class="text-14 font-bold">{{ props.data?.ingredient }}</span>
            </div>
            <div
              v-if="isAdminCheck && props.data.onlyIncluded"
              class="ingredient-table-name-tooltip simple-data-tooltip"
              :data-tooltip-text="$t('INGREDIENT.TOOLTIP_LOCKED_CAMPAIGN')"
            >
              <img alt="lock icon" src="@/assets/images/lockicon-ingredients.png" />
            </div>
          </div>
        </template>

        <template v-slot:products="props">
          <div
            v-if="props.data.isShoppable"
            class="ingredient-table-products text-14 color-charcoal-gray"
          >
            <div :class="{'font-bold': props.data.onlyPromoted}">
              <span>{{ props.data.totalPromotedProducts }} {{ $t('COMMON.PROMOTED') }}</span>
            </div>
            <div :class="{ 'color-grey': !props.data.productCount }"><span>|</span></div>
            <div :class="{ 'color-grey': !props.data.productCount }">
              <span>
                {{ props.data.productCount }} {{ $t('COMMON.MATCHES') }}
              </span>
            </div>
            <div
              v-if="props.data.onlyPromoted"
              class="ingredient-table-products-tooltip simple-data-tooltip"
              :data-tooltip-text="$t('PROMOTED_PRODUCTS_TOOLTIP')"
            >
              <img alt="wrong icon cta" src="@/assets/images/wrong-icon-cta.png" />
            </div>
          </div>
          <span v-else>{{ $t('COMMON.NOT_SHOPPABLE') }}</span>
        </template>

        <template v-slot:actions="props">
          <simple-actions
            :is-delete-btn-displayed="false"
            @editOnClick="editIngredient(props.data)"
          ></simple-actions>
        </template>
      </simple-table>

      <simple-paginate
        :is-pagination-enabled="isPaginationShowing"
        :pagination-total="tm_pagination.total"
        :pagination-size="tm_pagination.size"
      />

      <noResultFound
        v-if="!tm_rows.length && tm_isFirstLoadCompleted"
        :isReloadRequired="false"
      ></noResultFound>
    </block-wrapper>
  </content-wrapper>
</client-only>
</template>

<script setup>
import { ref, computed, onMounted, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import noResultFound from "@/components/no-result-found.vue";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import SimpleActions from "@/components/simple-actions/simple-actions.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useI18n } from "vue-i18n";
import { useProjectLang } from "@/composables/useProjectLang";
import { useTableManager } from "../composables/useTableManager.js";
import { useQueryUtils } from "../composables/useQueryUtils.js";
import { useSearchStore } from "../stores/search.js";
import { IngredientObject } from "../models/ingredients.model.js";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";

const store = useStore();
const router = useRouter();
const { t } = useI18n();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $tracker } = useNuxtApp();
const { readyProject, isAdmin } = useProjectLang();
const { getPageQuery, getSearchQuery } = useQueryUtils();
const { isSearchEnabled } = useSearchStore();
const {
  tm_rows,
  tm_isLoading,
  tm_pagination,
  tm_isFirstLoadCompleted,
} = useTableManager({
  storeId: "ingredients",
  clientKey: "flite",
  endpointKey: "searchIngredients",
  resultModel: IngredientObject,
});

const columnNames = ref([
  t('INGREDIENT.INGREDIENT_NAME'),
  t('INGREDIENT.INGREDIENT_PRODUCTS'),
  "",
]);
const columnKeys = ref(['name', 'products', 'actions']);
const isAdminCheck = ref(true);

const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (!isProjectReady) {
      return;
    }
    isAdminCheck.value = isAdmin.value;
    $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_FOOD_ITEMS, {}, { ...LOCAL_TRACKER_CONFIG });
  });
});

const editIngredient = (data) => {
  router.push({
    path: "edit-product-matches",
    query: {
      [QUERY_PARAM_KEY.NAMES]: data.ingredient,
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
      [QUERY_PARAM_KEY.SEARCH]: getSearchQuery(),
    },
  });
};
</script>
