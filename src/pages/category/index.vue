<template>
  <client-only>
    <content-wrapper :is-body-loading="tm_isLoading">
      <template v-slot:title>
        <span>{{ $t('CATEGORY.CATEGORY_TEXT') }}</span>
      </template>

      <template v-slot:head>
        <button
          v-show="!isSearchEnabled"
          type="button"
          class="btn-green"
          @click="addCategory()"
          @keydown="preventEnterAndSpaceKeyPress($event)"
        >
          {{ $t('CATEGORY.NEW_CATEGORY') }}
        </button>
      </template>

      <block-wrapper is-transparent>
        <template v-if="isSearchEnabled" v-slot:title>
          {{ $t('COMMON.SEARCH_RESULT') }}
        </template>

        <simple-table
          v-if="list.length"
          :column-names="columnNames"
          :column-keys="columnKeys"
          :data-source="list"
          table-class="cat-table"
        >
          <template v-slot:image="props">
            <img
              :src="props.data?.image || defaultImage"
              :src-placeholder="defaultImage"
              :alt="props.data?.name"
              class="simple-table-img"
            />
          </template>

          <template v-slot:isin="props">
            <span>{{ props.data?.isin }}</span>
          </template>

          <template v-slot:title="props">
            <span class="text-14 font-bold">{{ props.data?.name }}</span>
            <languages-alert
              :languages="props.data?.langs"
              :has-alert="props.data?.hasAlert"
              :lang="tm_lang"
              :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
            ></languages-alert>
          </template>

          <template v-slot:count="props">
            <p class="color-gray">
              {{ props.data?.totalRecipes }}
              <span v-if="props.data?.totalRecipes > 1">Recipes</span>
              <span v-else>Recipe</span>
            </p>
          </template>

          <template v-slot:status="props">
            <badge
              :label="$t(STATE_MAPPING[props.data?.state].tKey)"
              :badge-type="STATE_MAPPING[props.data?.state].badgeType"
              :img-src="STATE_MAPPING[props.data?.state].icon"
            ></badge>
          </template>

          <template v-slot:actions="props">
            <simple-actions
              :is-edit-btn-disabled="isActionDisabled(props.data?.state)"
              :is-edit-info-tooltip-showing="isActionDisabled(props.data?.state)"
              @editOnClick="editCategory(props.data)"
              :is-delete-btn-disabled="isActionDisabled(props.data?.state) || props.data?.totalRecipes !== 0 || props.data?.usedInHero"
              :is-delete-info-tooltip-showing="isActionDisabled(props.data?.state)"
              :delete-btn-warn-tooltip-text="props.data?.usedInHero ? $t('CATEGORY.CATEGORY_IN_HERO') : $t('CATEGORY.SIMPLE_ACTIONS_DELETE_TOOLTIP')"
              @deleteOnClick="openDeleteModal(props.data)"
            ></simple-actions>
          </template>
        </simple-table>

        <simple-paginate
          :is-pagination-enabled="isPaginationShowing"
          :pagination-total="tm_pagination.total"
          :pagination-size="tm_pagination.size"
        />

        <noResultFound
          v-if="!list.length && tm_isFirstLoadCompleted"
          :isReloadRequired="false"
          :isContentSearched="isSearchEnabled"
          :noResultText="$t('NO_RESULT.CATEGORY')"
        ></noResultFound>
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useNuxtApp } from '#app';
import ContentWrapper from '@/components/content-wrapper/content-wrapper.vue';
import BlockWrapper from '@/components/block-wrapper/block-wrapper.vue';
import SimpleTable from '@/components/simple-table/simple-table.vue';
import SimpleActions from '@/components/simple-actions/simple-actions.vue';
import Badge from '@/components/badge/badge.vue';
import NoResultFound from '@/components/no-result-found.vue';
import SimplePaginate from '@/components/simple-paginate.vue';
import LanguagesAlert from '@/components/languages-alert.vue';
import ConfirmModal from '@/components/modals/confirm-modal.vue';
import ProcessModal from '@/components/modals/process-modal.vue';
import { LOCAL_TRACKER_CONFIG } from '@/сonstants/trackerConfig';
import { STATE_MAPPING } from '@/сonstants/state-mapping';
import { QUERY_PARAM_KEY } from '@/сonstants/query-param-key';
import { CONFIRM_MODAL_TYPE } from '@/models/confirm-modal.model';
import { PROCESS_MODAL_TYPE } from '@/models/process-modal.model';
import { useProjectLang } from '@/composables/useProjectLang';
import { useTableManager } from '@/composables/useTableManager';
import { useQueryUtils } from '@/composables/useQueryUtils';
import { useSearchStore } from '@/stores/search';
import { useDynamicHeroStore } from '@/stores/dynamic-hero';
import { useBaseModal } from '@/composables/useBaseModal';
import { useEventUtils } from '@/composables/useEventUtils';
import defaultImage from '~/assets/images/default_recipe_image.png';

const { $keys } = useNuxtApp();
const store = useStore();
const router = useRouter();
const { t } = useI18n();
const { readyProject } = useProjectLang();
const { getDynamicHeroListDataAsync, disabledArticleDataListGetter } = useDynamicHeroStore();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const { $tracker, $eventBus } = useNuxtApp();
const { getPageQuery, getSearchQuery } = useQueryUtils();
const { isSearchEnabled } = useSearchStore();

const {
  tm_rows,
  tm_isLoading,
  tm_pagination,
  tm_isFirstLoadCompleted,
  tm_lang,
  tm_fetch,
} = useTableManager({
  storeId: 'categories',
  clientKey: 'flite',
  endpointKey: 'getCategoryMasterData',
  defaultParams: {
    includeAlerts: true,
  },
  smoothUpdate: true,
});

const { openModal, closeModal } = useBaseModal({
  deleteModal: {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: t('DESCRIPTION_POPUP.DELETE_CATEGORY'),
      description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} ${t('DESCRIPTION_POPUP.CATEGORY')}`,
    },
  },
  deletingModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
});

const projectId = ref(undefined);
const deleteCategoryDetails = ref('');
const timer1 = ref(null);
const timer2 = ref(null);

const columnNames = [
  '',
  t('CATEGORY.CATEGORY_ISIN'),
  t('CATEGORY.CATEGORY_TITLE'),
  t('CATEGORY.CATEGORY_RECIPE_COUNT'),
  t('CATEGORY.CATEGORY_STATUS'),
  '',
];

const columnKeys = ['image', 'isin', 'title', 'count', 'status', 'actions'];

const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));

const list = computed(() => {
  if (!tm_rows.value.length) {
    return [];
  }

  if (projectId.value === 'roche_main') {
    return tm_rows.value.map((data) => {
      return {
        ...data,
        usedInHero: data?.isin && disabledArticleDataListGetter.value?.includes(data.isin)
      };
    });
  }

  return tm_rows.value;
});

onMounted(() => {
  resetStore();
  readyProject(async ({ isProjectReady, id }) => {
    if (isProjectReady) {
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_CATEGORIES, {}, { ...LOCAL_TRACKER_CONFIG});
      if (id === 'roche_main') {
        projectId.value = id;
        const lang = store.getters['userData/getDefaultLang'];
        await getDynamicHeroListDataAsync({ lang });
      }
    }
  });
});

onBeforeUnmount(() => {
  resetStore();
  if (timer1.value) clearTimeout(timer1.value);
  if (timer2.value) clearTimeout(timer2.value);
});

const editCategory = (category) => {
  const states = [$keys.STATE.PUBLISHING, $keys.STATE.UNPUBLISHING];
  if (states.includes(category.state)) {
    return;
  }

  router.push({
    path: `/category/${category?.isin}`,
    query: {
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
      [QUERY_PARAM_KEY.SEARCH]: getSearchQuery(),
      [QUERY_PARAM_KEY.IS_IN_HERO]: category.usedInHero || false,
    },
  });
};

const addCategory = () => {
  router.push({
    path: '/category/create',
    query: {
      backFrom: getPageQuery(),
    },
  });
};

const isActionDisabled = (state) => {
  return state === $keys.STATE.PUBLISHING || state === $keys.STATE.UNPUBLISHING;
};

const openDeleteModal = (category) => {
  if (category?.state === $keys.STATE.PUBLISHING) {
    return;
  }

  openModal({
    name: 'deleteModal',
    onClose: (result) => result && postCategoryList(category.isin),
  });
};

const resetStore = () => {
  store.dispatch('categories/resetCategoryList');
};

// Delete functionality (keeping existing logic)
const postCategoryList = (isin) => {
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: 'removeAll',
      entityType: 'recipe',
      isin: isin,
    },
  };

  openModal({ name: 'deletingModal' });

  store.dispatch('categories/postCategoryRecipeAsync', { payload })
    .then((response) => {
      checkOperationStatusAsync(response.opId, isin);
    })
    .catch(() => {
      closeModal('deletingModal');
      $eventBus.emit('somethingWentWrong');
    });
};

const checkOperationStatusAsync = async (operationId, isin) => {
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  await getOperationStatusAsync(operationId);

  if (states.includes(deleteCategoryDetails.value.state)) {
    postCategoryGroupList(isin);
  } else {
    timer1.value = setTimeout(() => checkOperationStatusAsync(operationId, isin), 2000);
  }
};

const getOperationStatusAsync = async (operationId) => {
  deleteCategoryDetails.value = '';
  await store.dispatch('categories/getOperationStatusAsync', { operationId });
  deleteCategoryDetails.value = store.getters['categories/getOperationStatus'];
};

const postCategoryGroupList = async (isin) => {
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: 'removeAll',
      entityType: 'recipeCategoryGroup',
      isin: isin,
    },
  };

  try {
    const response = await store.dispatch('categories/postCategoryRecipeAsync', { payload });
    let deleteCategoryOperationId = response.opId;
    await checkOperationGroupStatusAsync(deleteCategoryOperationId, isin);
  } catch {
    closeModal('deletingModal');
    $eventBus.emit('somethingWentWrong');
  }
};

const checkOperationGroupStatusAsync = async (operationId, isin) => {
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  await getOperationStatusAsync(operationId);

  if (states.includes(deleteCategoryDetails.value.state)) {
    await deleteCategoryListAsync(isin);
    await tm_fetch({});
    closeModal('deletingModal');
  } else {
    timer2.value = setTimeout(() => checkOperationGroupStatusAsync(operationId, isin), 2000);
  }
};

const deleteCategoryListAsync = async (isin) => {
  await store.dispatch('categories/deleteCategoryList', { isin })
    .then(() => {
      closeModal('deletingModal');
      $eventBus.emit('newDeletedSuccess');
    })
    .catch(() => {
      closeModal('deletingModal');
      $eventBus.emit('somethingWentWrong');
    });
};
</script>
