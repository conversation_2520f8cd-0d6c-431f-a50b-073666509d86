<template>
  <content-wrapper :is-body-loading="tm_isLoading">
    <template v-slot:title> <span>{{ $t('ORGANIZATION.ORG_TEXT') }}</span> </template>

    <template v-slot:head>
      <button
        type="button"
        class="btn-green"
        @click="addNewOrganizations()"
        @keydown="preventEnterAndSpaceKeyPress($event)"
      >
        {{ $t('ORGANIZATION.NEW_ORG') }}
      </button>
    </template>

    <block-wrapper is-transparent>
      <simple-table
        v-if="tm_rows.length"
        :column-names="columnNames"
        :column-keys="columnKeys"
        :data-source="tm_rows"
        table-class="organization-table"
      >
        <template v-slot:image="props">
          <img
            :src="props.data?.image?.url || defaultImage"
            :src-placeholder="defaultImage"
            :alt="props.data?.name"
            class="simple-table-img"
          />
        </template>

        <template v-slot:isin="props">
          <span>{{ props.data?.isin }}</span>
        </template>

        <template v-slot:name="props">
          <span class="text-14 font-bold">{{ props.data?.name }}</span>
        </template>

        <template v-slot:status="props">
          <badge
            :label="$t(STATE_MAPPING[props.data?.state].tKey)"
            :badge-type="STATE_MAPPING[props.data?.state].badgeType"
            :img-src="STATE_MAPPING[props.data?.state].icon"
          ></badge>
        </template>

        <template v-slot:actions="props">
          <simple-actions
            :is-edit-btn-disabled="isActionDisabled(props.data?.state)"
            :is-edit-info-tooltip-showing="isActionDisabled(props.data?.state)"
            @editOnClick="editOrganizations(props.data)"
            :is-delete-btn-disabled="isActionDisabled(props.data?.state)"
            :is-delete-info-tooltip-showing="isActionDisabled(props.data?.state)"
            @deleteOnClick="deleteOrganization(props.data)"
          ></simple-actions>
        </template>
      </simple-table>

      <simple-paginate
        :is-pagination-enabled="isPaginationShowing"
        :pagination-total="tm_pagination.total"
        :pagination-size="tm_pagination.size"
      />

      <noResultFound
        v-if="!tm_rows.length && tm_isFirstLoadCompleted"
        :isReloadRequired="false"
        :noResultText="$t('NO_RESULT.ORGANIZATION')">
      </noResultFound>
    </block-wrapper>
  </content-wrapper>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import noResultFound from "@/components/no-result-found";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import Badge from "@/components/badge/badge.vue";
import { STATE_MAPPING } from "@/сonstants/state-mapping";
import SimpleActions from "@/components/simple-actions/simple-actions.vue";
import { useProjectLang } from "@/composables/useProjectLang";
import defaultImage from "~/assets/images/default_recipe_image.png";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useTableManager } from "../composables/useTableManager.js";
import { useQueryUtils } from "../composables/useQueryUtils.js";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import ConfirmModal from "../components/modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import ProcessModal from "../components/modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";

// Setup store, router, and i18n
const store = useStore();
const router = useRouter();
const { t } = useI18n();
const { readyProject } = useProjectLang();
const { $tracker, $auth, $keys } = useNuxtApp();
const { triggerLoading } = useCommonUtils();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const { getPageQuery } = useQueryUtils();
const {
  tm_rows,
  tm_isLoading,
  tm_pagination,
  tm_isFirstLoadCompleted,
  tm_lang,
  tm_fetch,
} = useTableManager({
  storeId: "organizations",
  clientKey: "flite",
  endpointKey: "getSearchOrganizations",
  smoothUpdate: true,
});

const { openModal, closeModal } = useBaseModal({
  "deleteModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: 'Delete Organization?',
      description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} organization?`,
    },
  },
  "deletingModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
});

// Reactive variables and constants
const columnNames = ref([
  "",
  t('ISIN'),
  t('USERS.NAME'),
  t('COMMON.STATUS'),
  "",
]);
const columnKeys = ref(['image', 'isin', 'name', 'status', 'actions']);

const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));

const deleteOrganization = (step) => {
  if (step.state === $keys.STATE.PUBLISHING || step.state === $keys.STATE.UNPUBLISHING) {
    return;
  }

  openModal({
    name: "deleteModal",
    onClose: (data) => data && deleteOrganizationsListAsync(step.isin),
  });
};

const deleteOrganizationsListAsync = async (isin) => {
  openModal({ name: "deletingModal" });
  const payload = {
    user: $auth?.user?.value?.email,
    isin,
  };
  const finishAction = (messageType) => {
    tm_fetch({});
    closeModal("deletingModal");
    triggerLoading(messageType);
  };
  await store.dispatch("organizations/deleteOrganizationsListAsync", {
    payload,
    lang: tm_lang.value,
  })
    .then(() => finishAction("newDeletedSuccess"))
    .catch(() => finishAction("somethingWentWrong"));
};

const addNewOrganizations = () => {
  router.push({
    path: "/add-organizations",
    query: {
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
    },
  });
};

const editOrganizations = (step) => {
  router.push({
    path: "/edit-organizations",
    query: {
      [QUERY_PARAM_KEY.ISIN]: step.isin,
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
    },
  });
};

const isActionDisabled = (state) => {
  return state === $keys.STATE.PUBLISHING || state === $keys.STATE.UNPUBLISHING;
};

const resetStore = () => {
  store.dispatch("organizations/resetOrganisationList");
};

onMounted(() => {
  resetStore();
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_ORGANIZATION, {}, { ...LOCAL_TRACKER_CONFIG });
    }
  });
});

onBeforeUnmount(() => {
  resetStore();
});
</script>
