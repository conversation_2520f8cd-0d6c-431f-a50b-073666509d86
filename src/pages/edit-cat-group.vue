<template>
  <client-only>
    <content-wrapper class="padding-zero">
      <div class="background-image-categories-group" v-show="!isPageLoading">
        <img alt="" class="background-image" :src="`${image}`" />
        <div class="back-btn" @click="backToCategories()">
          <img
            alt=""
            class="back-arrow-image"
            src="@/assets/images/back-arrow.png"
          />
          <span class="back-to-categories text-title-2">{{
            $t('CATEGORY_GROUP.BACK_MESSAGE')
          }}</span>
        </div>
        <div class="head-btn">
          <button type="button"
            :class="
              isCampaignModified &&
              categoriesName.trim() != '' &&
              !isRecipeVariantNameEmpty &&
              (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                ? 'btn-green'
                : 'disabled-button btn-green'
            "
            @click="displayPopup()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            {{ categoriesStatus === "active" ? "Publish" : "Save" }}
          </button>
          <button type="button"
            @click="backToCategories()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
            class="btn-green-outline"
          >
            {{ $t('BUTTONS.CANCEL_BUTTON') }}
          </button>
        </div>
        <div Class="edit-cat-group-isin text-title-2 font-normal">ISIN: {{ categoriesGroupISIN }}</div>
      </div>
      <div class="input-categories-group-section" v-show="!isPageLoading">
        <div class="input-section">
          <div class="input-sub-section">
            <div class="left-section">
              <div
                class="image-section"
                :style="{ backgroundImage: image ? `url('${image}')` : '' }"
                id="categoryImage"
              >
                <div class="image-main-div" id="recipeVideo">
                  <div class="image-inner-container">
                    <div
                      class="progress-image"
                      v-show="
                        uploadImagePercentage >= 1 &&
                        uploadImagePercentage <= 99
                      "
                    >
                      <div class="progress-image-content">
                        <div
                          v-show="
                            uploadImagePercentage >= 1 &&
                            uploadImagePercentage <= 5
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 6 &&
                            uploadImagePercentage <= 11
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 12 &&
                            uploadImagePercentage <= 17
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 18 &&
                            uploadImagePercentage <= 24
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 25 &&
                            uploadImagePercentage <= 30
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 31 &&
                            uploadImagePercentage <= 36
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 37 &&
                            uploadImagePercentage <= 41
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 42 &&
                            uploadImagePercentage <= 49
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 50 &&
                            uploadImagePercentage <= 55
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 56 &&
                            uploadImagePercentage <= 61
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 62 &&
                            uploadImagePercentage <= 67
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 68 &&
                            uploadImagePercentage <= 74
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 75 &&
                            uploadImagePercentage <= 80
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 81 &&
                            uploadImagePercentage <= 86
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 87 &&
                            uploadImagePercentage <= 92
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 93 &&
                            uploadImagePercentage <= 98
                          "
                        >
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                          />
                        </div>
                        <div v-show="uploadImagePercentage == 99">
                          <img
                            alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                          />
                        </div>
                        <div class="upload-text">
                          <div
                            class="upload-heading text-light-h4"
                            v-if="
                              uploadImagePercentage >= 1 &&
                              uploadImagePercentage <= 98
                            "
                          >
                            Upload is in progress
                          </div>
                          <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                          <span class="upload-media text-light-h6"
                            >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                            {{ (uploadImageSize / 1024000).toFixed(1) }}
                            MB</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <img
                    @click="DeleteImage()"
                    alt=""
                    class="delete-icon-image"
                    src="@/assets/images/deleteVideoBtn.png"
                    v-if="
                      image &&
                      (uploadImagePercentage == 0 ||
                        uploadImagePercentage == 100)
                    "
                  />
                  <img
                    alt=""
                    v-if="
                      image &&
                      (uploadImagePercentage == 0 ||
                        uploadImagePercentage == 100)
                    "
                    class="display-image-section"
                    :src="`${image}`"
                  />
                  <div
                    class="replace-image-tag text-light-h3"
                    v-if="
                      uploadImagePercentage == 0 ||
                      uploadImagePercentage == 100
                    "
                  >
                    <div class="hover-image">
                      <input
                        type="file"
                        class="upload-input"
                        title="Update Picture"
                        @click="uploadSameImageVideo($event)"
                        @change="checkUploadedFiles"
                        accept=".jpg,.png,.jpeg"
                        ref="productVideo"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="text-section"
                :class="{
                  'simple-data-tooltip': categoriegroupnameFocus,
                }"
                :data-tooltip-text="categoriegroupnameFocus && categoriesName"
              >
                <img
                  alt=""
                  v-if="categoriesName == ''"
                  class="compulsory-field-category-group"
                  src="@/assets/images/asterisk.svg?skipsvgo=true"
                />
                <input
                  class="title text-title-1"
                  id="title-name"
                  @mouseover="checkEditCategorieName()"
                  @mouseleave="hideeditcategorynameTip()"
                  @input="hideeditcategorynameTip()"
                  @keydown="hideeditcategorynameTip()"
                  autocomplete="off"
                  placeholder="Name your category group"
                  v-model.trim="categoriesName"
                />
              </div>
              <div class="image-details">
                <span class="bold text-title-2">Category Group Image: </span>
                <span class="normal text-title-2 font-normal"
                  >jpg,png format (recom. 1MB, max 15 MB)</span
                >
              </div>
            </div>
            <div class="right-section-category">
              <div
                :class="
                  categoriesState === 'published' ||
                  categoriesState === 'publishing'
                    ? 'publish-btn'
                    : 'publish-btn'
                "
              >
                <span
                  :class="
                    formatedCategory.length >= 1
                      ? 'text text-title-2'
                      : 'text inactive-publish'
                  "
                >
                  {{ $t('COMMON.PUBLISH') }}
                </span>
                <div
                  class="publish-toggle-section"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-edge': formatedCategory.length === 0 && categoriesName,
                  }"
                  :data-tooltip-text="(formatedCategory.length === 0 && categoriesName) && $t('CATEGORY.TOOL_TIP_CATEGORY')"
                >
                  <label
                    :class="
                      formatedCategory.length >= 1
                        ? 'switch text-light-h4'
                        : 'switch inactive-publish'
                    "
                  >
                    <input
                      type="checkbox"
                      :checked="categoriesStatus == 'active'"
                      @click.prevent="
                        !categoriesName
                          ? publishToggleBtnPopup()
                          : !categoriesName || formatedCategory.length === 0
                          ? ''
                          : publishToggleBtn()
                      "
                    />
                    <span
                      class="slider-round"
                      :class="{
                        isSliderActive:
                          formatedCategory.length == 0 || !categoriesName,
                      }"
                    ></span>
                  </label>
                </div>
              </div>
                <div
                  class="delete-btn"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-warn': formatedCategory.length,
                  }"
                >
                  <div class="simple-data-tooltip-content" v-if="formatedCategory.length">
                    <img src="@/assets/images/info.svg?skipsvgo=true" alt="info-icon" class="tooltip-icon" />
                    <span>{{ $t('CATEGORY_GROUP.SIMPLE_ACTIONS_DELETE_TOOLTIP') }}</span>
                  </div>
                  <button
                    type="button"
                    class="table-delete-btn btn-reset"
                    @click="deleteCategoryListPopUp()"
                    :class="formatedCategory.length ? 'disable-delete' : ''" >
                    <img alt="delete-icon" class="image" src="../assets/images/delete-icon.png"/>
                    <span class="text text-h3">
                      {{ $t('CATEGORY_GROUP.DELETE_CATEGORY_GROUP') }}
                    </span>
                  </button>
                </div>
            </div>
          </div>
          <div
            v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
            class="category-variant-section"
          >
            <div class="category-variants-main">
              <div class="category-variants">Categories Group Variants:</div>
              <div
                class="add-variant-section"
                :class="{
                  'simple-data-tooltip simple-data-tooltip-edge': recipeVariantLanguageList.length < 1,
                }"
                :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
              >
                <div
                  :class="
                    recipeVariantLanguageList.length > 0
                      ? 'add-variant-main'
                      : 'disable-add-variant-main add-variant-main'
                  "
                  @click="
                    recipeVariantLanguageList.length > 0
                      ? openRecipeVariantPopUp()
                      : ''
                  "
                >
                  <div class="add-variant-btn">
                    <img alt="" src="@/assets/images/category-add.png" />
                  </div>
                  <div class="add-variant-text text-h3">Add variant</div>
                </div>
              </div>
            </div>
            <div
              class="add-category-variant text-title-2 font-normal"
              v-if="recipeVariantList.length < 1 || isEnabledEditCategoryGroupMessage"
            >
              Add categories group variants to support multiple languages.
            </div>
            <div v-else class="category-variant-card-main">
              <template v-for="(categoryVariant, index) in recipeVariantList">
                <variant-card-field
                  v-if="categoryVariant?.lang !== lang"
                  v-model="categoryVariant.name"
                  :prefix-label="displayLanguageCode(categoryVariant.lang)"
                  input-placeholder="Enter name"
                  @input-change="inputcontentChanged(index)"
                  @delete-action="deleteCategoryVariant(categoryVariant, index)"
                ></variant-card-field>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div
        :class="
          !isPageLoading
            ? 'categories-group-section'
            : 'categories-group-section categories-group-section-data'
        "
      >
        <div class="content">
          <div class="loading" v-if="isPageLoading">
            <div class="content">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
                <div class="loader-text-container">
                  <div class="loading-text text-title-2 font-normal">
                    <p>{{ $t('LOADER.LOADING') }}</p>
                  </div>
                </div>
            </div>
          </div>
          <div class="categories-header-section" v-if="!isPageLoading">
            <div
              v-if="formatedCategory.length <= 1"
              class="categories-header"
            >
              {{
                formatedCategory && formatedCategory.length
                  ? formatedCategory.length
                  : 0
              }}
              Category in Group
            </div>
            <div
              v-else-if="formatedCategory.length > 1"
              class="categories-header"
            >
              {{
                formatedCategory && formatedCategory.length
                  ? formatedCategory.length
                  : 0
              }}
              Categories in Group
            </div>
            <div class="add-categories-btn" @click="addCategory">
              <div class="add-category-option text-h3">
                <img alt="" src="@/assets/images/add_icon.png" />
                <p>{{ $t('CATEGORY.ADD_CATEGORY') }}</p>
              </div>
            </div>
          </div>
          <div
            class="add-zero-section category-recipe-section"
            v-if="formatedCategory.length == 0 && !isPageLoading"
          >
            <div class="zero-promoted">
              <span class="bold text-title-2"> 0 CATEGORY IN GROUP. </span>
              <span class="normal text-title-2 font-normal"> Add category in category group. </span>
            </div>
          </div>
          <div
            class="table-header"
            v-if="formatedCategory.length > 0 && !isPageLoading"
          >
            <div class="container text-h3">
              <div class="first-column"></div>
              <div class="category-group-isin">
                <span>{{ $t('CATEGORY.CATEGORY_ISIN') }} </span>
              </div>
              <div class="category-group-title">
                <span>{{ $t('CATEGORY.CATEGORY_TITLE') }}</span>
              </div>
              <div class="category-group-count">
                <span> {{ $t('COMMON.RECIPE_COUNT') }}</span>
              </div>
              <div class="status">
                <span>{{ $t('COMMON.STATUS') }}</span>
              </div>
            </div>
          </div>
          <div
            class="categories-table-content"
            v-if="formatedCategory.length && !isPageLoading"
          >
          <table class="categories-table" id="categories-table">
                <caption></caption>
                <tbody>
                  <draggable
                    :list="formatedCategory"
                    class="all-content"
                    :scroll-sensitivity="200"
                    :force-fallback="true"
                    ghost-class="hidden-list"
                    @start="drag = true"
                    @end="drag = false"
                    handle=".draggable-icon"
                    @change="handleDrag"
                  >
                    <tr
                      class="body"
                      v-for="(categories, index) in formatedCategory"
                      :key="index"
                    >
                    <td>
                      <div class="draggable-icon">
                        <div class="instruction-drag-icon">
                          <img
                            alt=""
                            class="promoted-handle"
                            src="@/assets/images/drag-vertically.svg?skipsvgo=true"
                          />
                        </div>
                      </div>
                    </td>
                      <td class="category-image">
                        <div class="image-categories">
                          <img
                            alt="category"
                            class="image"
                            :src="categories?.image || categories?.data?.[lang]?.image || defaultImage"
                          />
                        </div>
                      </td>
                      <td class="category-isin">
                        <div class="category-isin">
                          {{ categories.isin ? categories.isin : "" }}
                        </div>
                      </td>
                      <td class="category-name">
                        <div
                          :class="{
                            'simple-data-tooltip': !hasDraggableTooltipDisplay && isCategoryListTooltipVisible,
                          }"
                          :data-tooltip-text="(!hasDraggableTooltipDisplay && isCategoryListTooltipVisible) && categories?.name"
                        >
                          <div
                            @mouseover="checkCategoryName(index)"
                            @mouseleave="hideCategorynameTip(index)"
                            :id="`category-item${index}`"
                            class="category-name-recipe text-title-3"
                          >
                            {{
                              (categories?.name || categories?.data?.[lang]?.name) ?? ""
                            }}
                          </div>
                        </div>
                      </td>
                      <td class="category-recipes">
                        <div class="categories-details">
                          <span v-if="categories.totalRecipes > 1"
                            >{{ categories.totalRecipes }}
                            {{ $t('COMMON.RECIPES') }}</span
                          >
                          <span v-if="categories.totalRecipes <= 1"
                            >{{ categories.totalRecipes }} Recipe</span
                          >
                        </div>
                      </td>
                      <td class="category-published">
                        <div class="categories-btn">
                          <div
                            class="published-state text-light-h4"
                            v-if="getCategoryStatus(categories?.status, categories?.state) === $t('TAG.PUBLISHED')"
                          >
                            <span>
                              <img
                                alt="icon"
                                src="@/assets/images/published-icon.png"
                              />{{ $t('COMMON.PUBLISHED') }}</span
                            >
                          </div>
                          <div
                            v-else-if="categories.state !== $keys.STATE.PUBLISHING"
                            class="unpublished-state-categories"
                          >
                            <span>
                              <img
                                alt="icon"
                                class="image"
                                src="@/assets/images/unpublished-icon.png"
                              />{{ $t('COMMON.UNPUBLISHED') }}</span
                            >
                          </div>
                          <div
                            class="preview-state"
                            v-if="categories.state === 'preview'"
                          >
                            <span>{{ $t('BUTTONS.PREVIEW_BUTTON') }}</span>
                          </div>
                          <div
                            class="failed-state"
                            v-if="categories.state === 'failed'"
                          >
                            <span>Failed</span>
                          </div>
                          <div
                            class="publishing-state-cat"
                            v-if="categories.state === 'publishing'"
                          >
                            <span
                              ><img
                                alt=""
                                src="@/assets/images/updating-icon.png"
                              />{{ $t('UPDATING') }}</span
                            >
                          </div>
                          <div
                            class="timeout-state"
                            v-if="categories.state === 'timeout'"
                          >
                            <span>timeout</span>
                          </div>
                        </div>
                        <button
                          class="edit-btn btn-reset"
                          @click="editCategories(categories || '')"
                          :class="{
                            'disable-category-group-edit-button simple-data-tooltip': categories?.state === 'publishing'
                          }"
                          :data-tooltip-text="categories?.state === 'publishing' && $t('COMMON.CANNOT_EDIT_WHILE_UPDATING')"
                        >
                          <img
                            alt=""
                            src="@/assets/images/edit_icon_black.png"
                          />
                        </button>
                        <button
                          class="menu btn-reset"
                          :class="{
                            'disable-category-group-delete-button simple-data-tooltip': categories?.state === 'publishing'
                          }"
                          :data-tooltip-text="categories?.state === 'publishing' && $t('COMMON.CANNOT_DELETE_WHILE_UPDATING')"
                        >
                          <img
                            alt=""
                            class="table-edit-btn"
                            @click="deleteRecipeListPopUp(index, categories)"
                            src="../assets/images/delete-icon.png"
                          />
                        </button>
                      </td>
                    </tr>
                  </draggable>
                </tbody>
              </table>
          </div>
        </div>
      </div>
      <catModel
        :isCatGrpNameVisible="isCatGrpNameVisible"
        :isUpdateCategoryModal="isUpdateCategoryModal"
        :closeModal="closeModal"
        :getCategorySearch="getCategorySearch"
        :isSearchExitEnable="isSearchExitEnable"
        :isTableDataLoading="isTableDataLoading"
        :formatedCategoryPopUp="formatedCategoryPopUp"
        :isChecked="isChecked"
        :defaultImage="defaultImage"
        :styleForTooltip="styleForTooltip"
        :checkAddCategoryGroupName="checkAddCategoryGroupName"
        :hideAddCategoryGroupNameTip="hideAddCategoryGroupNameTip"
        :fromPopUp="fromPopUp"
        :sizePopUp="sizePopUp"
        :updateCategoriesTotal="updateCategoriesTotal"
        :loadUpdatePopUpAsync="loadUpdatePopUpAsync"
        :countCategoriesSelected="countCategoriesSelected"
        :addCategoriesToGroupBtn="addCategoriesToGroupBtn"
        :hasNoCategoryFound="hasNoCategoryFound"
        :resetQuery="resetQuery"
        :queryCategory="queryCategory"
        :saveButtonMessage="$t('CATEGORY_GROUP.ADD_CATEGORY_TO_GROUP')"
        :categoriesTitle="categoriesName"
        :categoriesSubtitle="'Select Categories to add to the group.'"
      />
      <unableToContentModal
        v-if="hasUnableToPublishArticle"
        :closeModal="closeModal"
        :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
      />
      <deleteModal
        v-if="isDeleteCategoryModal"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY')"
        :productDescriptionOne="
          $t('DESCRIPTION_POPUP.REMOVE_CATEGORY_POPUP')
        "
        :productDescriptionTwo="'category group?'"
        :deleteItem="deleteCategoryBtn"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <deleteModal
        v-if="isDeleteCategoryGroupModal"
        :closeModal="closeModal"
        :productInfoTitle="$t('DESCRIPTION_POPUP.DELETE_CATEGORY_GROUP')"
        :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
        :productDescriptionTwo="'category group?'"
        :deleteItem="deleteCategoryGroup"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.DELETE_BUTTON')"
      />
      <unpublishModal
        v-if="isUnPublishModalVisible"
        :description="'Do you want to unpublish this category group?'"
        :noteMessage="'Unpublishing this category group will remove it and all associated categories from the widget. Proceed?'"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :unpublishFunction="unPublishConfirm"
        :closeModal="closeModal"
      />
      <saveModal
        v-if="isPublishModalVisible && formatedCategory.length !== 0"
        :closeModal="closeModal"
        :saveAndPublishFunction="publishConfirm"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"
        :imageName="unpublishIcon"
      />
      <saveModal
        v-if="isSaveModalVisible && categoriesStatus != 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        :imageName="saveIcon"
      />
      <saveModal
        v-if="isSaveModalVisible && categoriesStatus == 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :buttonName="'Publish'"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
        :imageName="publishIcon"
      />
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backtoCategoryGroup"
        :closeModal="closeModal"
      />
      <addVariant
        v-if="isAddVariantCategoryNamePopup"
        :closeModal="closeModal"
        :typeName="'Category Group'"
        :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
        :itemName="categoriesName"
        @addConfirmVariant="addRecipeVariant"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @backToRoute="backToSelectLanguageVariantPopUp"
      />
      <selectTheLanguageModal
        v-if="hasRecipeVariantLanguagePopup"
        :closeModal="closeModal"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
        @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
        @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
        :recipeVariantLanguageList="recipeVariantLanguageList"
        :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
      />
      <invalidImageVideoPopup
        v-show="isInvalidImageModalVisible && !isOffline"
        :closeModal="closeModal"
        :acceptedFile="' jpg,png'"
        :video="false"
        :image="true"
        :zip="false"
      />
      <deleteModal
        v-if="isDeleteCategoryImage"
        :closeModal="closeModal"
        :productInfoTitle="'Delete Image?'"
        :productDescriptionOne="'Are you sure you want to Delete '"
        :productDescriptionTwo="'this image?'"
        :deleteItem="deleteCategorygroupImage"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <deleteModal
        v-if="isRemoveCategoryVariantVisible"
        :closeModal="closeModal"
        :productInfoTitle="'Remove Categories group Variant?'"
        :productDescriptionOne="'Are you sure you want to remove this variant from the'"
        :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORIES_GROUP')"
        :deleteItem="removeCategoryVariant"
        :availableLanguage="0"
        :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
      />
      <savingModal
        v-show="isCategorySaving"
        :status="categoriesStatus == 'active' ? 'publishing' : 'saving'"
      />
      <sizeLimit
        v-if="isUploadingImagePopup"
        :continueImage="continueImage"
        :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
        :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
        :closeModal="closeModal"
        :isUploadingImagePopup="isUploadingImagePopup"
      />
      <sizeLimit
        v-if="isMaxImagePopupVisible"
        :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
        :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
        :closeModal="closeModal"
        :isMaxImagePopupVisible="isMaxImagePopupVisible"
      />
      <deletingModal v-show="isDeletingModalVisible" />
    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, reactive, onMounted, watch, onBeforeUnmount, getCurrentInstance, watchEffect } from 'vue';
import catModel from "@/components/cat-model";
import unpublishModal from "@/components/unpublish-modal";
import savingModal from "@/components/saving-modal";
import deletingModal from "@/components/deleting-modal";
import sizeLimit from "@/components/size-limit.vue";
import saveModal from "@/components/save-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import cancelModal from "@/components/cancel-modal";
import deleteModal from "@/components/delete-modal";
import addVariant from "@/components/add-variant";
import selectTheLanguageModal from "@/components/select-the-language";
import CategoriesService from "@/services/CategoriesService";
import RecipeService from "@/services/RecipeService";
import unableToContentModal from "@/components/unable-to-content-modal";
import axios from "axios";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { useI18n } from "vue-i18n";
import { useDelayTimer } from "~/composables/useDelayTimer";
import { useConnectionStatus } from '~/composables/useConnectionStatus';


// composables
import { useRefUtils } from "~/composables/useRefUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useEventUtils } from "~/composables/useEventUtils";
import { useProjectLang } from "@/composables/useProjectLang";

// images
import unpublishIcon from "@/assets/images/unpublished.png";
import saveIcon from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";
import publishIcon from "@/assets/images/publish-variant-icon.png";

// utility
import { useRouter, useRoute } from 'vue-router';
import { useStore } from "vuex";
import { useNuxtApp } from "#app";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";


const { readyProject, getProject, isAdmin , getAvailableLangs  } = useProjectLang();
const { triggerLoading } = useCommonUtils();
const project = ref({});
const isDeleteCategoryImage = ref(false);
const isDeletingModalVisible = ref(false);
const categoriegroupnameFocus = ref(false);
const isMaxImagePopupVisible = ref(false);
const uploadImageConfirm = ref("");
const isUploadingImagePopup = ref(false);
const isInvalidImageModalVisible = ref(false);
const isPageLoading = ref(false);
const isCategorySaving = ref(false);
const isDeleteCategoryGroupModal = ref(false);
const isDeleteCategoryModal = ref(false);
const categoriesStatus = ref("hidden");
const categoriesState = ref("");
const countCategoriesSelected = ref(0);
const isUpdateCategoryModal = ref(false);
const categoriesName = ref("");
const categoriesIsins = ref([]);
const categoriesGroupISIN = ref("");
const categoryUpdateList = ref([]);
const categoriesList = ref([]);
const totalCategoriesList = ref([]);
const selectedCategory = ref([]);
const indexOfCategory = ref(0);
const updateCategoriesTotal = ref(0);
const fromCategories = ref(0);
const sizeCategories = ref(10);
const categoriesTotal = ref(0);
const fromPopUp = ref(0);
const sizePopUp = ref(9);
const categoriesImg = ref("");
const categoryIsin = ref("");
const totalCategoryIsins = ref([]);
const selectedCategoryIsins = ref([]);
const isSaveModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const formatedCategory = ref([]);
const formatedCategoryPopUp = ref([]);
const isCampaignModified = ref(false);
const defaultImage = ref("@/assets/images/default_recipe_image.png");
const isConfirmModalVisible = ref(false);
const editCategoriesIsin = ref("");
const queryCategory = ref("");
const hasUnableToPublishArticle = ref(false);
const isSearchExitEnable = ref(false);
const isTableDataLoading = ref(false);
const hasRecipeVariantLanguagePopup = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const recipeVariantSelectedLanguage = ref("");
const recipeVariantLanguageList = ref([]);
const isAddVariantCategoryNamePopup = ref(false);
const variantName = ref("");
const recipeVariantList = ref([]);
const categoryVariantDataIndex = ref("");
const isRemoveCategoryVariantVisible = ref(false);
const selectedDefaultLang = ref([]);
const deletedvariant = ref(false);
const recipeVariantLanguageIndex = ref(0);
const lang = ref("");
const drag = ref(false);
const finalSelectedLanguage = ref([]);
const saveRemovedCategoryGroupVariants = ref([]);
const initiallyVariantSupported = ref([]);
const isEnabledEditCategoryGroupMessage = ref(false);
const isRecipeVariantNameEmpty = ref(false);
const finalAvailableLangs = ref([]);
const styleForTooltip = reactive({
  visibility: "hidden",
});
const isCatGrpNameVisible = ref(false);
const hasNoCategoryFound = ref(false);
const showLoader = ref(false);
const router = useRouter();
const cancelImage = ref({});
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const hasDraggableTooltipDisplay = ref(false);
const isAdminCheck = ref(false);
const route = useRoute();
const { $auth, $eventBus } = useNuxtApp();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const store = useStore();
const { getRef } = useRefUtils();
const { t } = useI18n();
const file = ref("");
const filesName = ref("");
const imageResponseUrl = ref("");
const { delay } = useDelayTimer();
const { preventEnterAndSpaceKeyPress } = useEventUtils()
const { isOffline } = useConnectionStatus();
const isCategoryListTooltipVisible = ref(false);
onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
    isPageLoading.value = true;
    triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);
    project.value = await getProject();

    if (!(project.value && project.value.id)) {
      router.push({ path: "/create-project" });
      return;
    }

    isAdminCheck.value = await isAdmin.value; // assuming `$auth.isAdmin` is available
    lang.value = store.getters["userData/getDefaultLang"];
    finalAvailableLangs.value = await getAvailableLangs();
    await getEditCategoryGroupListAsync();

    document.addEventListener("input", handleClickNameInput);
    document.addEventListener("click", handleClickOutsidePopup);
    document.addEventListener("keyup", handleESCClickOutside);

    selectedDefaultLang.value.push(lang.value);

    if (finalAvailableLangs.value) {
      finalAvailableLangs.value.forEach((language) => {
        let langData = {};
        if (language === "es-US") {
          langData = {
            language: language,
            language_name: "Spanish",
            languageFlag: "/images/flags/spain-flag.png",
          };
        } else if (language === "fr-FR") {
          langData = {
            language: language,
            language_name: "French",
            languageFlag: "/images/flags/france-flag.png",
          };
        }
        if (!selectedDefaultLang.value.includes(language)) {
          recipeVariantLanguageList.value.push(langData);
        }
      });
    }
    }
  });
});
const handleDrag = (event) => {
  if (event.moved) {
    isCampaignModified.value = true;
  }
};
const getCategoryStatus = (status, state) => {
  if (status !== $keys.KEY_NAMES.HIDDEN && state === t('TAG.PUBLISHED')) {
    return t('TAG.PUBLISHED');
  }
  return t('TAG.UNPUBLISHED');
};

const deleteCategorygroupImage = () => {
  imageResponseUrl.value = "";
  image.value = "";
  categoriesImg.value = "";
  let element = document.getElementById("categoryImage");
  element.style.backgroundImage = "";
  element.style.backgroundImage =
    "url('@/assets/images/upload-image-category.png');";
  isDeleteCategoryImage.value = false;
  isCampaignModified.value = true;
};

const DeleteImage = () => {
  isDeleteCategoryImage.value = true;
};

const backtoCategoryGroup = () => {
  if (editCategoriesIsin.value) {
    editCategoriesConfirm(editCategoriesIsin.value);
  } else {
    backToCategoriesConfirm();
    closeModal();
  }
};

const publishToggleBtnPopup = () => {
  if (categoriesStatus.value !== "active") {
    hasUnableToPublishArticle.value = true;
  } else {
    publishToggleBtn();
  }
};

const handleESCClickOutside = (event) => {
  if (event && event.key === "Escape" && !isDeletingModalVisible.value) {
    closeModal();
  }
};

const checkAddCategoryGroupName = (index, isin) => {
  let name = getRef("addCategoryGroupName" + index + isin);
  if (name.scrollWidth > name.clientWidth) {
    isCatGrpNameVisible.value = true;
  }
};
const hideAddCategoryGroupNameTip = (index, isin) => {
  let name = getRef("addCategoryGroupName" + index + isin);
  if (name.scrollWidth > name.clientWidth) {
    isCatGrpNameVisible.value = false;
  }
};

const uploadSameImageVideo = (event) => {
  event.target.value = "";
};

const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  file.value = evnt.target.files || evnt.srcElement.files;
  const fileType = file.value[0].type.split("/")[0];
  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};

const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();
  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = Math.round((progressEvent.loaded / progressEvent.total) * 100);
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {
      // Handle success if needed
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};
const uploadedImageFunctionAsync = async (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000); // Making the transition to 100% more noticeable.
    uploadImagePercentage.value = 100;
  }
};

const inputcontentChanged = (index) => {
  isCampaignModified.value = true;
};

const checkEditCategorieName = () => {
  const name = getRef("title-name");
  if (
    name.scrollWidth > name.clientWidth &&
    name !== document.activeElement &&
    categoriesName.value?.trim().length
  ) {
    categoriegroupnameFocus.value = true;
  }
};

const hideeditcategorynameTip = () => {
  categoriegroupnameFocus.value = false;
};

const checkVariantNameEmpty = () => {
  if (recipeVariantList.value.length === 0) {
    isRecipeVariantNameEmpty.value = false;
    return isRecipeVariantNameEmpty.value;
  }

  let count = 0;
  recipeVariantList.value.forEach((data) => {
    const variantName = data.name;
    if (variantName.trim().length === 0) {
      count++;
    }
  });

  isRecipeVariantNameEmpty.value = count > 0;
};

const checkCategoryName = (index) => {
  const name = getRef("category-item" + index);
  if (name.scrollWidth > name.clientWidth) {
    isCategoryListTooltipVisible.value = true;
  }
};

const hideCategorynameTip = (index) => {
  const name = getRef("category-item" + index);
  if (name.scrollWidth > name.clientWidth) {
    isCategoryListTooltipVisible.value = false;
  }
};

const displayLanguageCode = (item) => {
  let selectedCode = "";
  if (item !== lang.value) {
    const arr = item.split("-");
    selectedCode = arr[0].toUpperCase();
  }
  return selectedCode;
};
const addRecipeVariant = (item) => {
  variantName.value = item;
  if (variantName.value !== '') {
    const categories_isin = formatedCategory.value.map(data => data.isin);

    const newVariantData = {
      name: variantName.value.trim(),
      lang: recipeVariantLanguage.value,
      categories: categories_isin || [],
    };

    recipeVariantList.value.push(newVariantData);
    isAddVariantCategoryNamePopup.value = false;
    variantName.value = '';
    isCampaignModified.value = true;

    // Remove selected language from the language list
    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(data => {
      if (data.language === recipeVariantLanguage.value) {
        return false;
      }
      return true;
    });

    // Remove selected language from saved removed categories
    saveRemovedCategoryGroupVariants.value = saveRemovedCategoryGroupVariants.value.filter(data => data !== recipeVariantLanguage.value);
  }
};

// Function for handling next category variant name modal
const nextCategoryVariantNameModalPopUp = (item) => {
  if (item === '') {
    recipeVariantLanguage.value = recipeVariantLanguageList.value[0].language;
  } else {
    recipeVariantLanguage.value = item;
  }
  hasRecipeVariantLanguagePopup.value = false;
  isAddVariantCategoryNamePopup.value = true;
  hasRecipeVariantLanguageResult.value = false;
};

// Function for showing language matches
const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};

// Function to go back to select language variant popup
const backToSelectLanguageVariantPopUp = () => {
  isAddVariantCategoryNamePopup.value = false;
  hasRecipeVariantLanguagePopup.value = true;
};

// Function to open the recipe variant popup
const openRecipeVariantPopUp = () => {
  setTimeout(() => {
    hasRecipeVariantLanguagePopup.value = true;
    hasRecipeVariantLanguageResult.value = false;
    recipeVariantLanguage.value = '';
  });
};

// Function to set the recipe variant language matches
const setRecipeVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  recipeVariantLanguageIndex.value = index; // Assuming recipeVariantLanguageIndex is defined elsewhere
  hasRecipeVariantLanguageResult.value = false;

  // Move selected language to the front of the list
  recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(data => {
    if (data.language === recipeVariantLanguage.value) {
      recipeVariantLanguageList.value.unshift(data);
      return false;
    }
    return true;
  });
};

// Function to delete a category variant
const deleteCategoryVariant = (data, index) => {
  isRemoveCategoryVariantVisible.value = true;
  categoryVariantDataIndex.value = index;
  deletedvariant.value = data.lang;
};

// Function to remove the category variant
const removeCategoryVariant = () => {
  initiallyVariantSupported.value.forEach((data, index) => {
    if (data.lang === deletedvariant.value) {
      saveRemovedCategoryGroupVariants.value.push(deletedvariant.value);
      initiallyVariantSupported.value.splice(index, 1);
    }
  });

  recipeVariantList.value.splice(categoryVariantDataIndex.value, 1);

  let langData = {};
  if (deletedvariant.value === "es-US") {
    langData = {
      language: deletedvariant.value,
      language_name: "Spanish",
      languageFlag: "/images/flags/spain-flag.png",
    };
  } else if (deletedvariant.value === "fr-FR") {
    langData = {
      language: deletedvariant.value,
      language_name: "French",
      languageFlag: "/images/flags/france-flag.png",
    };
  }

  recipeVariantLanguageList.value.push(langData);
  isCampaignModified.value = true;
  closeModal(); // Assuming closeModal is defined elsewhere
};
    // Function to open the delete category list popup
const deleteCategoryListPopUp = () => {
  isDeleteCategoryGroupModal.value = true;
};

// Function to open the delete recipe list popup
const deleteRecipeListPopUp = (index, categories) => {
  if (categories.state === 'publishing') {
    return;
  }
  categoryIsin.value = categories.isin;
  indexOfCategory.value = index;
  isDeleteCategoryModal.value = true;
};

// Function to delete a category
const deleteCategoryBtn = () => {
  isCampaignModified.value = true;
  formatedCategory.value.splice(indexOfCategory.value, 1);

  if (formatedCategory.value && formatedCategory.value.length < 1) {
    unPublishConfirm(); // Assuming this function is defined elsewhere
  }

  closeModal(); // Assuming this function is defined elsewhere
  categoryIsin.value = '';
  indexOfCategory.value = 0;
  triggerLoading($keys.KEY_NAMES.DELETED);
};

// Async function for save button click
const saveButtonClickAsync = async () => {
  if (recipeVariantList.length < 1) {
    isEnabledEditCategoryGroupMessage.value = true;
  }
  isCategorySaving.value = true;
  await postUpdateCategoriesGroupAsync(); // Assuming this function is defined elsewhere
  isCampaignModified.value = false;
};

// Function to handle publish toggle button action
const publishToggleBtn = () => {
  if (
    categoriesStatus.value === 'active' &&
    (categoriesState.value === 'readyToPublish' || categoriesState.value === 'published')
  ) {
    isUnPublishModalVisible.value = true;
  }

  if (
    categoriesStatus.value === 'hidden' &&
    (categoriesState.value === 'readyToPublish' || categoriesState.value === 'published')
  ) {
    isPublishModalVisible.value = true;
  }
};

// Function to patch the publish category
const patchPublishCategory = () => {
  if (route.query.isin && categoriesStatus.value) {
    const payload = {
      status: categoriesStatus.value,
    };

    return store.dispatch('categoriesGroup/patchCategoryAsync', {
      isin: route.query.isin,
      payload,
    })
      .then(() => {
        isSaveModalVisible.value = false;
        isCategorySaving.value = false;

        router.push({
          path: '/cat-group',
        });

        const loadingStatus = categoriesStatus.value !== 'active' ? 'savedSuccess' : 'isPublishedData';
        triggerLoading(loadingStatus); // Assuming this function is defined elsewhere
      })
      .catch(() => {
        showLoader.value = false;
        isCategorySaving.value = false;
      });
  }
};
const getEditCategoryGroupListAsync = async () => {
  isPageLoading.value = true;
  triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);

  await store.dispatch('categories/getEditCategoryGroupListAsync', {
      isin: route.query.isin,
      sectionType: 'categoryGroup',
    });

  try {
    const response = store.getters['categories/getEditCategoryGroupList'];
    processResponseData(response);
    updateCategoryState(response);

    if (response.data[lang.value]) {
      updateCategoryDetails(response.data[lang.value]);
    }

    isCampaignModified.value = false;
  } catch {
    showLoader.value = false;
    isPageLoading.value = false;
    triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);
  }
};

const processResponseData = (response) => {
  selectedDefaultLang.value = Object.keys(response.data);
  selectedDefaultLang.value.forEach((language) => {
    if (response?.data?.[language] && language !== lang.value) {
      let newVariantData = {
        name: response.data[language].name,
        lang: language,
        categories: response.data[lang.value].categories,
      };
      recipeVariantList.value.push(newVariantData);
      initiallyVariantSupported.value.push(newVariantData);
    }
  });
};

const updateCategoryState = (response) => {
  categoriesGroupISIN.value = response?.isin ?? '';
  categoriesStatus.value = response?.status ?? '';
  categoriesState.value = response?.state ?? '';
};

const updateCategoryDetails = (categoryData) => {
  categoriesImg.value = categoryData?.image ?? '';
  categoriesName.value = categoryData?.name ?? '';
  categoriesIsins.value = categoryData?.categories ?? [];

  if (categoriesImg.value) {
    image.value = categoriesImg.value;
    const categoryImage = getRef('categoryImage');
    categoryImage.style.backgroundImage = `url(${categoriesImg.value})`;
  }

  if (categoriesIsins.value.length) {
    getCategoryForGroupAsync(categoriesIsins.value);
  } else {
    unPublishConfirm();
    isPageLoading.value = false;
    triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);
  }
};

const getCategoryForGroupAsync = async (isin) => {
  isPageLoading.value = true;
  triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);

  if (isin.length) {
    categoriesList.value = [];
    formatedCategory.value = [];
    const payload = {
      from: fromCategories.value,
      size: sizeCategories.value,
      type: "Category",
      isins: isin.join(','),
    };

    try {
      await store.dispatch('categoriesGroup/getCategoryForCategoryGroupListAsync', { payload });
      const response = await store.getters['categoriesGroup/getCategoryForCategoryGroupList'];
      categoriesList.value = response?.results ?? [];
      categoriesTotal.value = categoriesList.value.length;
      categoriesList.value.forEach((data) => {
        data.dropDown = false;
      });

      if (categoriesList.value.length) {
        await getCategoryStatistics();
      }
    } catch {
      showLoader.value = false;
    } finally {
      isPageLoading.value = false;
      triggerLoading('$keys.KEY_NAMES.ROUTE_LOADING', isPageLoading.value);
    }
  }
};
    const getCategorySearch = (query) => {
  if (query) {
    isTableDataLoading.value = true;
    queryCategory.value = query;
    updateCategoriesTotal.value = 0;
    fromPopUp.value = 0;
    formatedCategoryPopUp.value = [];
    categoriesList.value = [];
    getCategoryListAsync();
  }
};

const resetQuery = () => {
  isTableDataLoading.value = true;
  updateCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formatedCategoryPopUp.value = [];
  categoriesList.value = [];
  queryCategory.value = '';
  getCategoryListAsync();
  isSearchExitEnable.value = false;
  hasNoCategoryFound.value = false;
};

const isChecked = (item) => {
  countCategoriesSelected.value = 0;

  if (item.isChecked) {
    item.isChecked = false;
    selectedCategoryIsins.value = selectedCategoryIsins.value.filter(
      (data) => data !== item.isin
    );
    selectedCategory.value = selectedCategory.value.filter(
      (data) => data.isin !== item.isin
    );
  } else {
    item.isChecked = true;
    categoryUpdateList.value.forEach((data) => {
      if (data.isin === item.isin) {
        selectedCategoryIsins.value.push(data.isin);
        selectedCategory.value.push(data);
      }
    });
  }

  countCategoriesSelected.value = selectedCategoryIsins.value.length || 0;
};

const deleteCategoryGroup = () => {
  deleteCategoryList(categoriesGroupISIN.value);
};

const deleteCategoryList = (isin) => {
  isCampaignModified.value = false;
  closeModal();
  isDeletingModalVisible.value = true;

  return CategoriesService.deleteCategoryList(project.value, store, $auth, isin)
    .then(() => {
      isDeletingModalVisible.value = false;
      router.push('/cat-group');
      triggerLoading('deletedSuccess');
    })
    .catch(() => {
      showLoader.value = false;
      isDeletingModalVisible.value = false;
       triggerLoading('somethingWentWrong');
    });
};
const addCategoryButton = () => {
  fromPopUp.value = 0;
  isTableDataLoading.value = true;
  getCategoryListAsync();
};

const loadUpdatePopUpAsync = async () => {
  const from = parseInt(fromPopUp.value) + sizePopUp.value;
  if (from < updateCategoriesTotal.value) {
    fromPopUp.value = from;
    const loadMoreRecipeList = await getCategoryListAsync();
    if (loadMoreRecipeList && categoryUpdateList.value) {
      await getCategoryStatisticsPopUpAsync(loadMoreRecipeList);
      categoryUpdateList.value = [...categoryUpdateList.value, ...loadMoreRecipeList];
    }
  }
};

const addCategoriesToGroupBtn = () => {
  categoriesTotal.value += 1;
  totalCategoryIsins.value = [];
  totalCategoriesList.value = [];
  isCampaignModified.value = true;
  if (selectedCategory.value && formatedCategory.value) {
    totalCategoriesList.value = [...selectedCategory.value, ...formatedCategory.value];
  }
  formatedCategory.value = totalCategoriesList.value;
  closeModal();
};

const getCategoryStatistics = () => {
  const isins = categoriesList.value.map((data) => data.isin);
  return CategoriesService.getCategoryStatistics(
    project.value,
    store,
    $auth,
    lang.value,
    'category',
    isins
  )
    .then((response) => {
      categoriesList.value.forEach((data) => {
        response.find((item) => {
          if (item.isin === data.isin) {
            data.totalRecipes = item.totalRecipes;
          }
        });
      });
      formatedCategory.value = [...categoriesList.value];
      triggerLoading('routeLoading');
    })
    .catch(() => {
      showLoader.value = false;
      triggerLoading('routeLoading');
    });
};

const getCategoryStatisticsPopUpAsync = async (loadMoreRecipeList) => {
  const isins = loadMoreRecipeList
    ? loadMoreRecipeList.map((data) => data.isin)
    : categoryUpdateList.value.map((data) => data.isin);
  const payload = {
    type: 'category',
    country: lang.value.split('-')[1],
    lang: lang.value.split('-')[0],
    isins: isins.join(',')
  };
  await store.dispatch('categoriesGroup/getCategoryStatisticsAsync', { payload });
    const response = store.getters['categoriesGroup/getCategoryStatistics'];
    hasNoCategoryFound.value = false;

    const updateCategoryList = (list) => {
      list.forEach((data) => {
        response.find((item) => {
          if (item.isin === data.isin) {
            data.totalRecipes = item.totalRecipes;
          }
        });
      });
    };


    if (!loadMoreRecipeList) {
      updateCategoryList(categoryUpdateList.value);
      formatedCategoryPopUp.value = [...categoryUpdateList.value];
    } else {
      updateCategoryList(loadMoreRecipeList);
      formatedCategoryPopUp.value = [...categoryUpdateList.value, ...loadMoreRecipeList];
    }
  try {

  } catch {
    showLoader.value = false;
    hasNoCategoryFound.value = true;
  }
};
const getCategoryListAsync = async () => {
  const payload = {
      lang: lang.value,
      q: queryCategory.value,
      from: fromPopUp.value,
      size: sizePopUp.value,
      sort: "lastMod", // Adjust if necessary
    };
    await store.dispatch("categories/getCategoryMasterData", { payload });
    const response = store.getters["categories/getCategoryMasterData"];
    response.results = response.results.slice(0, 9);

    response.results.forEach((data) => {
      data.isChecked = false;
      data.isAlreadyAddedCategory = false;
    });

    selectedCategoryIsins.value.forEach((category) => {
      response.results.forEach((data) => {
        if (category === data.isin) {
          data.isChecked = true;
        }
      });
    });

    response.results.forEach((data) => {
      formatedCategory.value.forEach((item) => {
        if (data.isin === item.isin) {
          data.isAlreadyAddedCategory = true;
        }
      });
    });

    if (fromPopUp.value >= 9) {
      return response.results;
    } else {

      categoryUpdateList.value = response.results ?? [];
      if (categoryUpdateList) {
        await getCategoryStatisticsPopUpAsync();
      }
    }
    isSearchExitEnable.value = queryCategory.value !== "";
    updateCategoriesTotal.value = response.total;


  try {

 isTableDataLoading.value = false;

  } catch {
    showLoader.value = false;
    isPageLoading.value = false;
    triggerLoading(store.getters['KEY_NAMES'].ROUTE_LOADING, isPageLoading.value);
  }
};
const updatedRecipeVariantList = (variantList) => {
  finalSelectedLanguage.value = [];
  let categoriesIsin = [];

  if (formatedCategory.value?.length) {
    formatedCategory.value.forEach((data) => {
      categoriesIsin.push(data.isin);
    });
  }

  if (variantList?.length) {
    variantList.map((item) => {
      if (item && item.name !== "" && item.lang !== "") {
        item[item.lang] = {
          name: item.name || "",
          categories: categoriesIsin || [],
        };
      }
    });
  }

  let updatedVariantList = Object.assign({}, ...variantList);
  delete updatedVariantList.lang;
  delete updatedVariantList.name;
  delete updatedVariantList.categories;

  finalSelectedLanguage.value = Object.keys(updatedVariantList);
  return updatedVariantList;
};

const setLanguageVariant = (variantList) => {
  if (finalSelectedLanguage.value.length && variantList?.[lang.value]) {
    const copyObjectData = finalSelectedLanguage.value.map(item => ({
      [item]: variantList[lang.value],
    }));
    return Object.assign({}, ...copyObjectData);
  }
  return {};
};

const setPayLoadWithVariantAsync = async (payload) => {
  // if (payload.image?.[lang.value]) {
  //   payload.image = await setLanguageVariant(payload.image);
  // }
  return payload;
};

const postUpdateCategoriesGroupAsync = async () => {
  const categoriesIsin = formatedCategory.value.map(data => data?.isin);
  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: lang.value,
    categories: categoriesIsin || [],
  };
  const isinFromQuery = route?.query?.isin;

  recipeVariantList.value.push(defaultVariantData);

  if (isinFromQuery) {
    let payload = {
      isin: isinFromQuery,
      type: "categoryGroup",
      data: updatedRecipeVariantList(recipeVariantList.value),
      image: {
        [lang.value]: imageResponseUrl.value
          ? imageResponseUrl.value.replace(/\?.*/, "")
          : categoriesImg.value,
      },
    };

    if (imageResponseUrl.value || categoriesImg.value) {
      payload = await setPayLoadWithVariantAsync(payload);
    } else {
      delete payload.image;
    }

    return store.dispatch("categoriesGroup/postCategoryOrCategoryGroupAsync", { payload })
      .then(async () => {
        if (saveRemovedCategoryGroupVariants.value.length) {
          await deleteVariant();
        }
        patchPublishCategory();
      })
      .catch(() => {
        showLoader.value = false;
        isCategorySaving.value = false;
      });
  }
};

const deleteVariant = async () => {
  try {
    await CategoriesService.deleteLanguageVariant(
      project.value,
      store,
      $auth,
      saveRemovedCategoryGroupVariants.value,
      route.query.isin
    );
  } catch (e) {
    console.error(e);
  }
};

const uploadImageAsync = async () => {
  if (file.value) {
    const reader = new FileReader();
    reader.addEventListener(
      'load',
      async () => {
        const extension = file.value[0].type.split('/')[1];
        const params = {
          entity: 'recipeCategoryGroup',
          content: 'image',
          lang: lang.value,
          extension,
          public: true,
        };
        if (route.query.isin) {
          await store.dispatch('preSignedUrl/getPreSignedImageUrlAsync', {
            isin: route.query.isin,
            params,
          });
          const response = store.getters['preSignedUrl/getPreSignedUrl'];
          await uploadImageFile(
            response.data.url || '',
            file.value[0]
          );
          await RecipeService.upload(
            response.data.url || '',
            file.value[0]
          );
          imageResponseUrl.value = response.data.url || '';
        }
      },
      false
    );
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};

const uploadSameImage = (event) => {
  event.target.value = '';
};

const continueImage = async () => {
  file.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.addEventListener(
    'load',
    async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    },
    false
  );
  if (file.value[0]) {
    reader.readAsDataURL(file.value[0]);
  }
};

const uploadFiles = async () => {
  isCampaignModified.value = true;
  if (file.value.length > 0) {
    filesName.value = file.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!filesName.value.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = null;
      filesName.value = '';
      return;
    }
    const fileSize = file.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size >= 1 * 1024 * 1024) {
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = file.value;
      file.value = null;
    }
    if (size >= 15 * 1024 * 1024) {
      file.value = null;
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.addEventListener(
        'load',
        async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        },
        false
      );
      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  }
};
const addCategory = () => {
  formatedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  addCategoryButton();
  countCategoriesSelected.value = 0;
  selectedCategoryIsins.value = [];
  isUpdateCategoryModal.value = true;
};

const closeModal = () => {
  const scroll = getRef("updateGroupContent");
  if (scroll) scroll.scrollTo(0, 0);

  isConfirmModalVisible.value = false;
  isDeleteCategoryGroupModal.value = false;
  isDeleteCategoryModal.value = false;
  isUpdateCategoryModal.value = false;
  isDeleteCategoryImage.value = false;
  isPublishModalVisible.value = false;
  isUnPublishModalVisible.value = false;
  isSaveModalVisible.value = false;
  isAddVariantCategoryNamePopup.value = false;
  isRemoveCategoryVariantVisible.value = false;
  hasRecipeVariantLanguagePopup.value = false;
  isSearchExitEnable.value = false;
  isUploadingImagePopup.value = false;
  updateCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formatedCategoryPopUp.value = [];
  categoriesList.value = [];
  queryCategory.value = '';
  selectedCategory.value = [];
  selectedCategoryIsins.value = [];
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
  variantName.value = '';
  isDeletingModalVisible.value = false;
};

const editCategories = (categories) => {
  if (categories.state === 'publishing') {
    return;
  }
  editCategoriesIsin.value = categories.isin;
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    editCategoriesConfirm(categories.isin);
  }
};

const editCategoriesConfirm = (isin) => {
  router.push({
    path: `/category/${isin}`,
    query: {
      [QUERY_PARAM_KEY.FROM]: route.query[QUERY_PARAM_KEY.ISIN],
      [QUERY_PARAM_KEY.BACK_FROM]: route.query[QUERY_PARAM_KEY.BACK_FROM],
    },
  });
};

const backToCategories = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToCategoriesConfirm();
  }
};

const backToCategoriesConfirm = () => {
  isCampaignModified.value = false;
  router.push({
    path: '/cat-group',
    query: {
      [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
      [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH] || undefined,
    },
  });
};

const displayPopup = () => {
  isSaveModalVisible.value = true;
};

const unPublishConfirm = () => {
  isCampaignModified.value = true;
  categoriesStatus.value = 'hidden';
  closeModal();
};

const publishConfirm = () => {
  isCampaignModified.value = true;
  categoriesStatus.value = 'active';
  closeModal();
};

const handleClickNameInput = (event) => {
  const titleName = getRef('title-name');
  const inputCategoryVariant = document.querySelector('.input-for-category-group-variant');
  if (titleName?.contains(event.target) || inputCategoryVariant?.contains(event.target)) {
    isCampaignModified.value = true;
  }
};

const handleClickOutsidePopup = (event) => {
  if (hasRecipeVariantLanguagePopup.value) {
    const dropdown = document.querySelector('.category-group-dropdown');
    if (!dropdown?.contains(event.target)) {
      hasRecipeVariantLanguagePopup.value = false;
    }
  }
};
onBeforeUnmount(() => {
  document.removeEventListener("input", handleClickNameInput);
  document.removeEventListener("click", handleClickOutsidePopup);
  document.removeEventListener("keyup", handleESCClickOutside);
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
});

watch(isOffline, (offlineStatus) => {
  if (offlineStatus) {
    closeModal();
  }
});

watchEffect(() => {
  if (drag.value) {
    hasDraggableTooltipDisplay.value = true;
  } else {
    hasDraggableTooltipDisplay.value = false;
  }
  if(isCampaignModified.value) {
    triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
    checkVariantNameEmpty()
  }
});
</script>
