<template>
  <client-only>
    <content-wrapper :is-body-loading="isPageLoading">
      <div class="main-inner-section">
        <loader v-if="isloading" :status="'saving'" />
        <div
          v-if="!isPageLoading"
          class="collection-info-container"
          :class="isTagPopupVisible ? 'collection-info-margin-container' : ''"
        >
          <detailPageTopSection
            :enableContinueButton="enableContinueButton"
            @backToCollection="cancelButton"
            @displaySavePopup="displaySavePopup"
            :isCollectionPublished="isCollectionPublished"
            :selectedTags="selectedTags"
            :isCampaignModified="isCampaignModified"
          />
          <div class="collection-details">
            <collectionFormSection
              :enableContinueButton="enableContinueButton"
              v-model:collectionName="collectionName"
              v-model:isCollectionPublished="isCollectionPublished"
              :isNewCollection="isNewCollection"
              :checkCollectionNameText="checkCollectionNameText"
              :displayDeleteCollection="displayDeleteCollection"
              :setCampaignModified="setCampaignModified"
              :isCollectionState="isCollectionState"
            />
          </div>
          <div class="heading-featured-recipes text-title-2">
            {{ t("COLLECTION.FEATURED_RECIPES_BASED_ON_TAGS") }}
          </div>
          <div class="tags-details">
            <collectionTagDetailSection
              id="collection-tag-detail-section"
              :selectedTags="selectedTags"
              :tags="tags"
              :tagsTotal="tagsTotal"
              :recipesCount="recipesCount"
              :recipeList="recipeList"
              v-model:currentPage="currentPage"
              :sizeTags="sizeTags"
              :inputPlaceholder="inputPlaceholder"
              v-model:selectTag="selectTag"
              :isTagPopupVisible="isTagPopupVisible"
              :itemsPerPage="itemsPerPage"
              :chooseTag="chooseTagAsync"
              :removeTag="removeTag"
              :focusTagPanel="focusTagPanelAsync"
              :debounceTagEnter="debounceTagEnter"
              :nextPageAsync="nextPageAsync"
              :prevPageAsync="prevPageAsync"
              :allAvailableTags="allAvailableTags"
              :isTagPopupVisibleText="isTagPopupVisibleText"
              :isTagsLoading="isTagsLoading"
              :toggleDropdown="toggleDropdown"
              :checkSelected="checkSelected"
              :previewRecipe="previewRecipe"
              :lang="lang"
              :pageChange="pageChange"
              :fromTags="fromTags"
              :isRecipeLoading="isRecipeLoading"
              :recipeTagData="recipeTagData"
              :setCampaignModified="setCampaignModified"
              :handleEnter="handleEnter"
            />
          </div>
        </div>
      </div>
      <collectionSaveModal
        v-if="isSaveModalVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveCollectionData"
        :buttonName="buttonName"
        :description="'Do you want to ' + buttonName + ' your Collection?'"
        :descriptionTwo="'Click on Publish will replace the current published Collection.'"
        :imageName="imageName"
      />
      <collectionDeleteModal
        v-if="isCollectionDeletePopupVisible"
        :closeModal="closeModal"
        :deleteItem="deleteCollectionAsync"
        productInfoTitle="Do you want to Delete your Collection?"
        buttonText="Delete"
      />
      <deletingModal v-show="isDeletingModalVisible" />
      <Modal v-if="isPreviewRecipeOpen" @close="closeModal">
        <template #noProductMatches>
          <div class="collection-open-recipe-preview-modal">
            <div class="collection-recipe-main-preview-header">
              <div class="collection-recipe-preview-header text-h2">
                {{ t("COMMON.RECIPE_PREVIEW") }}
              </div>
            </div>
            <button type="button" @click="closeModal" class="btn-reset">
              <img
                alt="cross-icon"
                :class="
                  isPreviewRecipePopupLoading
                    ? 'collection-close-preview-recipe-modal'
                    : 'disable-cross collection-close-preview-recipe-modal'
                "
                src="@/assets/images/exit-gray.png"
              />
            </button>
            <div class="collection-open-preview-recipe-main">
              <recipePreviewDetail
                :rISIN="recipeIsin"
                :checkRecipePreviewVideo="checkRecipePreviewVideo"
                @loaderProgress="clickToLoad"
              >
              </recipePreviewDetail>
            </div>
          </div>
        </template>
      </Modal>
      <cancelModal
        v-if="isConfirmModalVisible"
        :availableLang="[]"
        :isCampaignModifiedFromShoppableReview="false"
        :callConfirm="backToCollection"
        :closeModal="closeModal"
      />
    </content-wrapper>
  </client-only>
</template>
<script setup>
import collectionSaveModal from "@/components/collection-save-modal.vue";
import collectionTagDetailSection from "@/components/collection-tag-detail-section.vue";
import collectionFormSection from "@/components/collection-form-section.vue";
import detailPageTopSection from "@/components/detail-page-top-section.vue";
import RecipeService from "@/services/RecipeService";
import deletingModal from "@/components/deleting-modal";
import collectionDeleteModal from "@/components/collection-delete-modal.vue";
import Modal from "@/components/Modal.vue";
import recipePreviewDetail from "@/components/recipe-preview-detail.vue";
import cancelModal from "@/components/cancel-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import { useNuxtApp } from "#app";
import { ref, onMounted, computed } from "vue";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
const { $tracker, $auth } = useNuxtApp();
import { useProjectLang } from "@/composables/useProjectLang";
// composables
import { useRefUtils } from "~/composables/useRefUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import loader from "@/components/saving-modal";

// images
import publishGreenIcon from "@/assets/images/publish-green-icon.png";
import saveIcon from "@/assets/images/featured-saved.png";

// Reactive state variables (previously data properties)
let isConfirmModalVisible = ref(false);
const defaultImage = ref("@/assets/images/default_recipe_image.png");
let collectionName = ref("");
let isPageLoading = ref(false);
let isTagPopupVisible = ref(false);
const query = ref("");
let sizeTags = ref(10);
let fromTags = ref(0);
const fromRecipes = ref(0);
const tagsTotal = ref(0);
const isNewCollection = ref(false);
let isSaveModalVisible = ref(false);
const selectTag = ref("");
let tags = ref([]);
let allAvailableTags = ref(0);
const selectedTags = ref([]);
const dropdownItem = ref([]);
const recipeList = ref([]);
const recipesCount = ref(0);
const isloading = ref(false);
const isCollectionState = ref("");
const isCollectionPublished = ref(false);
const isCollectionDeletePopupVisible = ref(false);
const isDeletingModalVisible = ref(false);
let isTagPopupVisibleText = ref("Suggested Tags");
let isTagsLoading = ref(false);
const currentPage = ref(1);
const itemsPerPage = ref(12);
const sizeRecipes = ref(10);
const existingTagsInCollection = ref([]);
const existingCollectionTagResults = ref([]);
const isPreviewRecipePopupLoading = ref(false);
const isVideoPreview = ref(false);
const isPreviewRecipeOpen = ref(false);
const recipeIsin = ref("");
const isRecipeLoading = ref(false);
let buttonName = ref("Save");
const recipeTagData = ref([]);
const isCampaignModified = ref(false);
const project = ref({});
const store = useStore();
const nuxtApp = useNuxtApp();
const { t } = useI18n();
const { routeToPage, triggerLoading, scrollToTop } = useCommonUtils();
const fromCollections = ref(0); // or another initial value
const sizeCollections = ref(10);
const instance = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const { getRef } = useRefUtils();
const $keys = instance.appContext.config.globalProperties.$keys;
const { readyProject, getProject } = useProjectLang();

onMounted(async () => {
   readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isNewCollection.value = !route.query.uuid;
      isPageLoading.value = true;
      project.value = await getProject();

      document.addEventListener("click", handleClickOutside);
      document.addEventListener("keyup", handleESCClickOutside);
      try {
        await Promise.all([
          getTagMasterDataAsync(),
          route.query.uuid
            ? getEditCollectionAsync()
            : getCollectionListDataAsync(),
        ]);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        isPageLoading.value = false;
        triggerLoading(nuxtApp.$keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
      }
    }
  });
});
const inputPlaceholder = computed(() => {
  return selectedTags.value.length >= 1
    ? "Enter Tags"
    : "Enter Tags to generate Featured Recipes list.";
});

const imageName = computed(() => {
  return isCollectionPublished.value
    ? publishGreenIcon
    : saveIcon;
});
const lang = computed(() => {
  return store?.getters["userData/getDefaultLang"] || null; // Fallback to null
});
const backToCollection = () => {
  routeToPage("collections");
};
const handleEnter = () => {
  isTagPopupVisible.value = false;
  const tagInput = getRef("collection-tag-detail-section")?.getRef("tagInput");
  if (tagInput) {
    tagInput.blur();
  }
};
const shiftExistingTags = () => {
  if (existingCollectionTagResults.value.length === 0) {
    return;
  }
  const existingTagsSet = [];
  existingCollectionTagResults.value.map((existingTag) => {
    tags.value.map((tag, tagIndex) => {
      if (existingTag.isin === tag.isin) {
        tags.value.splice(tagIndex, 1);
        existingTagsSet.push(tag);
      }
    });
  });
  if (existingTagsSet.length) {
    tags.value = tags.value.concat(existingTagsSet);
  }
};
const setCampaignModified = () => {
  isCampaignModified.value = true;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
};
const getMultipleTagDataAsync = async (flag, existingTagsInCollectionList) => {
  try {
    let isins = [];
    if (flag) {
      isins = [...new Set(
        recipeList.value.flatMap(recipe => recipe?.tags?.[lang.value] || [])
      )].join(",");
    } else {
      new Set(existingTagsInCollectionList.map((tag) => tag.isin));
      isins = [...uniqueIsins].join(",");
    }
    await store.dispatch("collection/getMultipleTagDataAsync", {
      isins,
      lang: lang.value,
    });
    let collectionTagData = store.getters["collection/getCollectionTagData"];
    if (flag) {
      recipeTagData.value = collectionTagData.results;
    } else {
      existingCollectionTagResults.value = collectionTagData.results;
    }
  } catch (error) {
    console.error(error);
  }
};
const pageChange = (page) => {
  scrollToTop();
  currentPage.value = page;
  fromRecipes.value = page * sizeRecipes.value - sizeRecipes.value;
  getRecipeDataForTagAsync();
};
const getCollectionListDataAsync = async () => {
  await store.dispatch("collection/getCollectionDataAsync", {
    lang: lang.value,
    from: fromCollections.value,
    size: sizeCollections.value,
  });
  let collectionListData = store.getters["collection/getCollectionData"];
  if (Array.isArray(collectionListData) && collectionListData.length) {
    const allTags = collectionListData
      .flatMap((item) => item.tags?.filter((tag) => tag.isin))
      .filter(Boolean);
    existingTagsInCollection.value = [...new Set(allTags)];
    await getMultipleTagDataAsync(false, existingTagsInCollection.value);
  }
};
const checkSelected = (tag) => {
  const existingTag = selectedTags.value.find((item) => item.isin === tag.isin);
  if (existingTag) {
    tag.isSelected = true;
    return true;
  }
  return false;
};
const nextPageAsync = async () => {
  if (fromTags.value + 10 <= allAvailableTags.value) {
    fromTags.value += 10;
    await getTagMasterDataAsync();
    shiftExistingTags();
  }
};
const prevPageAsync = async () => {
  fromTags.value = Math.max(fromTags.value - 10, 0);
  await getTagMasterDataAsync();
  shiftExistingTags();
};
const saveCollectionData = () => {
  const isEdit = Boolean(route.query.uuid);
  const method = isEdit ? patchCollectionDataAsync : postCollectionDataAsync;
  method();
  closeModal();
  isloading.value = true;
};
const patchCollectionDataAsync = async () => {
  const selectedIsins = selectedTags.value.map((tag) => tag.isin);
  const payload = {
    title: collectionName.value,
    tags: selectedIsins,
    state: isCollectionPublished.value
      ? t("COLLECTION.PUBLISHED")
      : t("COLLECTION.UNPUBLISHED"),
    preview: false,
  };
  await store.dispatch("collection/patchCollectionDataAsync", {
    uuid: route.query.uuid || "",
    payload,
    onSuccess: notifySuccess,
  });
};
const postCollectionDataAsync = async () => {
  const selectedIsins = selectedTags.value.map((tag) => tag.isin);
  const payload = {
    title: collectionName.value,
    tags: selectedIsins,
    state: isCollectionPublished.value
      ? t("COLLECTION.PUBLISHED")
      : t("COLLECTION.UNPUBLISHED"),
    preview: false,
  };
  await store.dispatch("collection/postCollectionDataAsync", {
    payload,
    onSuccess: notifySuccess,
    lang: lang.value,
  });
};
const notifySuccess = () => {
  triggerLoading(
    $keys.KEY_NAMES.COLLECTION_PUBLISH_TOAST,
    isCollectionPublished.value
  );
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  routeToPage("collections");
};
const getRecipeDataForTagAsync = async () => {
  try {
    isRecipeLoading.value = true;
    recipesCount.value = 0;
    recipeList.value = [];
    const selectedIsins = selectedTags.value
      .filter(item => item && item.isin)
      .map(item => item.isin)
      .join(",");
    const response = await RecipeService.getRecipeForCategories(
      project.value, "", selectedIsins, fromRecipes.value, sizeRecipes.value, [], [], lang.value, store, $auth
    );
    recipeList.value = response.data.results.map(item => ({ ...item, dropDown: false }));
    tagsTotal.value = response.data.total || 0;
    recipesCount.value = response.data.total || 0;
    if (recipeList.value.length) {
      await getMultipleTagDataAsync(true);
    }
  } catch (error) {
    console.error("Error fetching recipe data:", error);
  } finally {
    isRecipeLoading.value = false;
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const getEditCollectionAsync = async () => {
  try {
    await store.dispatch("collection/getEditCollectionDataAsync", {
      uuid: route.query.uuid,
      lang: lang.value,
    });
    const collectionData = store.getters["collection/getEditCollectionData"];
    if (collectionData) {
      collectionName.value = collectionData.data.title ?? "";
      selectedTags.value = collectionData.data.tags ?? [];
      recipesCount.value = collectionData.data.recipeCount ?? 0;
      isCollectionPublished.value = collectionData.data.state === t("COLLECTION.PUBLISHED");
      isCollectionState.value = isCollectionPublished.value ? t("COLLECTION.PUBLISHED") : t("COLLECTION.UNPUBLISHED");
      isPageLoading.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
      if (selectedTags.value.length) {
        isRecipeLoading.value = true;
        await getRecipeDataForTagAsync();
      }
    }
  } catch (error) {
    console.error(error);
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const displayDeleteCollection = () => {
  if (isCollectionState.value !== t("COLLECTION.PUBLISHED")) {
    isCollectionDeletePopupVisible.value = !isCollectionPublished.value;
  }
};
const deleteCollectionAsync = async () => {
  isCollectionDeletePopupVisible.value = false;
  isDeletingModalVisible.value = true;
  await store.dispatch("collection/deleteCollectionDataAsync", {
    uuid: route.query.uuid,
    onDeleteSuccess: deleteSuccess,
  });
};
const deleteSuccess = () => {
  closeModal();
  triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  routeToPage("collections");
};
const checkCollectionNameText = () => {
  collectionName.value = collectionName.value.trim().replace(/\s+/g, " ");
};
const removeTag = (tag) => {
  tag.isSelected = false;
  const updatedTags = selectedTags.value.filter((item) => item !== tag);
  if (updatedTags.length !== selectedTags.value.length) {
    selectedTags.value = updatedTags;
    if (updatedTags.length) {
      currentPage.value = 1;
      fromRecipes.value = 0;
      getRecipeDataForTagAsync();
    } else {
      recipesCount.value = 0;
      recipeList.value = [];
    }
    setCampaignModified();
  }
};
const focusTagPanelAsync = async () => {
  isTagPopupVisible.value = true;
  isTagPopupVisibleText = selectTag.value ? "Found Tags" : "Suggested Tags";
  if (fromTags.value !== 0) {
    fromTags.value = 0;
    sizeTags.value = 10;
  }
  await getTagMasterDataAsync();
  if (existingTagsInCollection.length) {
    shiftExistingTags();
  }
};
const chooseTagAsync = async (tag) => {
  if (tag.isSelected) {
    return;
  }
  setCampaignModified();
  tag.isSelected = true;
  selectedTags.value.push(tag);
  if (selectTag.value !== "") {
    selectTag.value = "";
    await getTagMasterDataAsync();
  }
  currentPage.value = 1;
  fromRecipes.value = 0;
  await getRecipeDataForTagAsync();
};
const debounceTagEnter = async () => {
  await getTagMasterDataAsync();
  await focusTagPanelAsync();
};
const getTagMasterDataAsync = async () => {
  try {
    isTagsLoading.value = true;
    const query = selectTag.value ?? "";
    if (query) {
      fromTags.value = 0;
      sizeTags.value = 10;
    }
    await store.dispatch("tagData/getTagMasterDataAsync", {
      categoryAlert: true,
      recipeTotal: true,
      query: query,
      from: fromTags.value,
      size: sizeTags.value,
      lang: lang.value,
      state: t("TAG.PUBLISHED"),
    });
    const tagsData = store.getters["tagData/getTags"];
    if (tagsData) {
      tags.value = tagsData.data.results.map((tag) => ({
        ...tag,
        isSelected: false,
      }));
      allAvailableTags.value = tagsData.data?.total;
    }
  } catch (error) {
    console.error("Error fetching tag data:", error);
  } finally {
    isPageLoading.value = false;
    isTagsLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};
const cancelButton = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    routeToPage("collections");
  }
};
const displaySavePopup = () => {
  isSaveModalVisible.value = true;
  buttonName.value = isCollectionPublished.value ? "Publish" : "Save";
};
const handleClickOutside = (event) => {
  const clickedElement = event.target;
  const crossIconClicked = clickedElement.classList.contains("cross-icon");
  if (dropdownItem.value?.dropDown) {
    const collectionTagDetailSection = getRef("collection-tag-detail-section");
    const menuSelected = collectionTagDetailSection?.querySelectorAll(".menu-selected");
    if (menuSelected[0] && !menuSelected[0].contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
  if (isTagPopupVisible.value) {
    const popupVisible = getRef("collection-tag-detail-section");

    if (
      popupVisible &&
      !popupVisible.contains(event.target) &&
      !crossIconClicked
    ) {
      isTagPopupVisible.value = false;
      selectTag.value = "";
    }
  }
};
const enableContinueButton = () => {
  return (
    collectionName.value &&
    collectionName.value.trim() !== "" &&
    recipesCount.value >= 5
  );
};
const closeModal = () => {
  isConfirmModalVisible.value = false;
  isSaveModalVisible.value = false;
  isCollectionDeletePopupVisible.value = false;
  isDeletingModalVisible.value = false;
  if (isPreviewRecipePopupLoading.value) {
    isPreviewRecipeOpen.value = false;
  }
};
const handleESCClickOutside = (event) => {
  if (event.key === "Escape" && !isVideoPreview.value && !isDeletingModalVisible.value) {
    closeModal();
  }
};
const toggleDropdown = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;
  recipeList.value.forEach((data) => {
    if (item.isin !== data.isin) {
      data.dropDown = false;
    }
  });
};
const previewRecipe = (isin) => {
  if (isin) {
    recipeIsin.value = isin;
    isPreviewRecipeOpen.value = true;
    clickToLoad();
  }
};
const checkRecipePreviewVideo = (data) => {
  isVideoPreview.value = !!data;
};
const clickToLoad = (condition) => {
  isPreviewRecipePopupLoading.value = condition;
};
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keyup", handleESCClickOutside);
});
</script>
