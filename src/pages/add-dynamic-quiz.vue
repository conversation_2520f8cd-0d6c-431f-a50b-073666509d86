<template>
  <content-wrapper :is-body-loading="isPageLoading">
    <div
      v-if="!isPageLoading"
      class="dynamic-hero-quiz-sub-container main-inner-section"
    >
      <div class="dynamic-hero-quiz-top-container">
        <div
          @click="backToDynamicHeroList()"
          class="back-to-dynamic-hero-list-section"
        >
          <div class="back-arrow-image">
            <img
              alt=""
              class="back-arrow-image"
              src="~/assets/images/back-arrow.png"
            />
          </div>
          <div class="back-text">
            {{ $t('SWITCH_PAGES.BACK_TO_DYNAMIC_HERO_LIST') }}
          </div>
        </div>
        <div class="dynamic-hero-quiz-continue-and-cancel-section">
          <div class="cancel-button">
            <button type="button" @click="backToDynamicHeroList()">
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
          <div
            v-if="!isReplaceLiveHero"
            :class="
              quizName.trim() !== '' &&
              quizQuestionText.trim() !== '' &&
              quizResultText.trim() !== '' &&
              isSelectRadioResult &&
              (isCTALinkIsValid || quizCTALinkText.trim() == '')
                ? 'continue-button'
                : 'disable-button continue-button'
            "
            @click="saveQuizForm()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            <button type="button">{{ $t('BUTTONS.CONTINUE') }}</button>
          </div>
          <div
            v-if="isReplaceLiveHero"
            :class="
              quizName.trim() !== '' &&
              quizQuestionText.trim() !== '' &&
              quizResultText.trim() !== '' &&
              isSelectRadioResult &&
              (isCTALinkIsValid || quizCTALinkText.trim() == '')
                ? 'continue-button'
                : 'disable-button continue-button'
            "
            @click="replaceQuizForm()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            <button type="button">{{ $t('REPLACE') }}</button>
          </div>
        </div>
      </div>
      <div class="dynamic-hero-quiz-middle-container">
        <div class="dynamic-hero-quiz-middle-section">
          <div class="dynamic-hero-quiz-name-and-calender-section">
            <div class="dynamic-hero-quiz-form-name-section">
              <div class="dynamic-hero-quiz-form-info-question-image-section">
                <img
                  alt="quiz icon"
                  class="hero-quiz-image"
                  src="~/assets/images/hero-quiz-image.png"
                />
              </div>
              <div class="dynamic-hero-quiz-form-text">{{ $t('DYNAMIC_HERO.QUIZ_FORM') }}</div>
            </div>
            <div class="dynamic-hero-quiz-form-calender-section">
              <div
                class="news-date-picker-container"
              >
                <div class="start-date-text">Start date:</div>
                  <CalendarPicker
                    v-model="scheduleDate"
                    :isRange="false"
                    :markers="markers"
                    :disabled-dates="disabledDates"
                    :isHeroLive="isReplaceLiveHero"
                    :isLiveHeroReplaced="isReplaceLiveHero"
                    @update:model-value="handleDateChange"
                  />
              </div>
            </div>
          </div>
          <div class="dynamic-hero-quiz-name-and-schedule-section">
            <div class="dynamic-hero-quiz-name-section"
              :class="{
                'simple-data-tooltip': quizNameinfocus,
              }"
              :data-tooltip-text="quizNameinfocus && quizName"
            >
              <div v-if="quizName == ''" class="compulsory-quiz-field">*</div>
              <input
                class="quiz-name-field"
                type="text"
                ref="quizInputFieldRef"
                autocomplete="off"
                v-model.trim="quizName"
                placeholder="Quiz name in CMS"
                @mouseover="checkQuizName()"
                @keydown="hideQuizTip()"
                @mouseleave="hideQuizTip()"
                @input="hideQuizTip()"
              />
            </div>
            <div
              v-if="!isReplaceLiveHero"
              class="dynamic-hero-quiz-schedule-section"
            >
              <div
                :class="
                  scheduleDate == ''
                    ? 'schedule-text disabled-text'
                    : 'schedule-text'
                "
              >
                Schedule
              </div>
              <div class="schedule-toggle-button">
                <label
                  class="switch"
                  :class="{
                    'simple-data-tooltip simple-data-tooltip-edge': scheduleDate == 0,
                  }"
                  :data-tooltip-text="scheduleDate == 0 && $t('DYNAMIC_HERO.HERO_SCHEDULE_TOOLTIP')"
                >
                  <input
                    type="checkbox"
                    :disabled="scheduleDate == '' ? true : false"
                    :checked="scheduleDate != 0 && isQuizStatus != ''"
                    @click="isQuizStatus = !isQuizStatus"
                  />
                  <span
                    :class="
                      scheduleDate == ''
                        ? 'slider-round disabled-slider'
                        : 'slider-round'
                    "
                  ></span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dynamic-hero-quiz-bottom-container">
        <div class="dynamic-hero-quiz-question-section">
          <div class="dynamic-hero-quiz-question-container">
            <div class="dynamic-hero-quiz-question-heading">
              <span class="heading-text">Question</span>
              <span class="compulsory-field">*</span>
            </div>
            <div class="preview-section-dynamic">
              <div class="text-section-prev">{{ $t('DYNAMIC_HERO.HERO_PREVIEW') }}</div>
              <label
                for="hero-preview"
                class="switch"
                :class="{
                  'simple-data-tooltip simple-data-tooltip-edge': !isHeroPreview
                }"
                :data-tooltip-text="!isHeroPreview && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
              >
                <input id="hero-preview" @change="isHeroPreview = !isHeroPreview" type="checkbox" aria-label="Hero Preview Toggle" />
                <span class="slider-round"></span>
              </label>
            </div>
          </div>
          <div class="dynamic-hero-quiz-question-input-section">
            <textarea
              @input="checkQuestion"
              maxlength="244"
              class="input-text-area"
              ref="quizQuestionFieldRef"
              v-model="quizQuestionText"
            ></textarea>
            <div
              v-if="quizQuestionText !== ''"
              class="question-section-word-count-section"
            >
              {{ quizQuestionText.length }}/244
            </div>
          </div>
        </div>
        <div class="dynamic-hero-quiz-result-section">
          <div class="dynamic-hero-quiz-result-head-section">
            <div class="dynamic-hero-quiz-result-heading">
              <span class="heading-text">Result:</span>
              <span class="compulsory-field">*</span>
            </div>
            <div class="dynamic-hero-quiz-result-radio-button-section">
              <div
                class="radio-button-section"
                v-for="(data, index) in quizResultList"
                :key="index"
                @click="!data.isChecked && selectResult(data, data.name)"
              >
                <div class="round">
                  <input v-if="data.isChecked" id="quiz-result" type="radio" />
                  <label for="quiz-result" aria-label="quizResult"></label>
                </div>
                <div class="result-text">{{ data.name }}</div>
              </div>
            </div>
          </div>
          <div class="dynamic-hero-quiz-result-input-section">
            <textarea
              @input="checkResult"
              maxlength="244"
              class="input-text-area"
              id="quizResultFieldRef"
              v-model="quizResultText"
            ></textarea>
            <div
              v-if="quizResultText !== ''"
              class="result-section-word-count-section"
            >
              {{ quizResultText.length }}/244
            </div>
          </div>
        </div>
        <div class="dynamic-hero-quiz-cta-link-section">
          <div class="dynamic-hero-quiz-cta-link-heading">
            <span class="heading-text">CTA link:</span>
          </div>
          <div class="dynamic-hero-quiz-cta-link-input-section">
            <div class="input-section">
              <input
                class="input-text-area"
                ref="quizCTALinkRef"
                v-model="quizCTALinkText"
                autocomplete="off"
                @input="validateisCTALinkInprogress"
                placeholder="Enter link"
              />
            </div>
            <div class="cta-link-input-verify-section">
              <div v-if="isCTALinkInprogress" class="cta-link-progress-check">
                <div class="loader-image"></div>
              </div>
              <div class="cta-link-correct-check link-image-check">
                <img
                  v-if="!isCTALinkInprogress && isCTALinkIsValid"
                  class="correct-icon"
                  alt=""
                  src="@/assets/images/tick-icon.png"
                />
              </div>
              <div class="cta-link-wrong-check link-image-check">
                <img
                  v-if="!isCTALinkInprogress && !isCTALinkIsValid && isCTALinkBroken"
                  class="wrong-icon"
                  alt=""
                  src="@/assets/images/red-info.svg?skipsvgo=true"
                />
              </div>
            </div>
          </div>
          <div
            v-if="!isCTALinkIsValid && isCTALinkBroken"
            class="cta-broken-link-validation-section"
          >
            <span class="cta-link-broken-message">This link is broken.</span>
          </div>
        </div>
      </div>
    </div>
    <saveModal
      v-if="isSavingQuizDraft"
      :closeModal="closeModal"
      :saveAndPublishFunction="postEditDynamicHeroDataAsync"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your Quiz form?'"
      imageName="@/assets/images/quiz-form.png"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduleQuizPopupVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="handleHeroSchedule"
      @schedule="handleHeroSchedule"
      :PatchScheduledHero="postEditDynamicHeroDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      :markers="markers"
      @date-click="handleDateClickPopup"
    />
    <savingModal v-show="isQuizSaving" :status="'saving'" />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="postEditDynamicHeroDataAsync"
      :closeReplacement="closeModal"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import { useTimeUtils } from "../composables/useTimeUtils";
import { useRoute } from "vue-router";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import { useCommonUtils } from "../composables/useCommonUtils";
import replacementModal from "@/components/confirm-replacement-modal.vue";
import savingModal from "@/components/saving-modal";
import saveModal from "@/components/save-modal.vue";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useProjectLang } from "@/composables/useProjectLang";
import { useStore } from "vuex";
import cancelModal from "@/components/cancel-modal";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";

const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  postDynamicHeroDataAsync,
} = useDynamicHeroStore();
const route = useRoute();
const quizInputFieldRef = ref(null);
const quizQuestionFieldRef = ref(null);
const quizResultFieldRef = ref(null);
const quizCTALinkRef = ref(null);
const store = useStore();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const disableList = ref([]);
const quizNameinfocus = ref(false);
const disabledDates = ref([]);
const incId = ref(0);
const todos = ref([]);
const isloading = ref(false);
const isHeroPreview = ref(false);
const isLiveHero = ref(false);
const scheduleDateConfirm = ref('');
const isQuizSaving = ref(false);
const isQuizStatus = ref(false);
const isSelectRadioResult = ref(true);
const isCampaignModified = ref(false);
const isSavingQuizDraft = ref(false);
const quizName = ref('');
const isScheduleQuizPopupVisible = ref(false);
const scheduleDate = ref('');
const quizQuestionText = ref('');
const quizResultText = ref('');
const quizCTALinkText = ref('');
const quizResultOption = ref('');
const heroData = ref([
  {template: 'quiz'}
]);
const quizResultList = reactive([
  {
    value: '1',
    name: 'True',
    key: 'vrai',
    isChecked: true,
  },
  {
    value: '2',
    name: 'False',
    key: 'faux',
    isChecked: false,
  },
]);
const defaultQuizImage = ref('https://innit-web-widget.s3.us-west-2.amazonaws.com/img/quiz_hero.svg?skipsvgo=true');
const isCTALinkInprogress = ref(false);
const isCTALinkIsValid = ref(false);
const isCTALinkBroken = ref(false);
const isConfirmModalVisible = ref(false);
const isCreateDuplicate = ref(false);
const isPageLoading = ref(false);
const isReplaceLiveHero = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const lang = ref('');
const isAdminCheck = ref(false);
const { readyProject, isAdmin } = useProjectLang();

// Composables
const { formatJsonTimestamp } = useTimeUtils();
const { preventEnterAndSpaceKeyPress } = useEventUtils();
const { triggerLoading, checkDuplicate, routeToPage, processScheduledElement, isScheduledWithPublishDate, getDisabledDates, getDisableList, useCalendarMarkers } = useCommonUtils();
const { watchReactiveValue } = useWatcherUtils();
const { markers } = useCalendarMarkers(disableList);
onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});

const handleHeroSchedule = () => {
  isScheduleQuizPopupVisible.value = false;
};

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  lang.value = store.getters['userData/getDefaultLang'];

  await getDynamicHeroDataAsync();

  const sourceUrl = window.location.href;

  if (sourceUrl.includes('create-duplicate')) {
    isCampaignModified.value = true;
    isCreateDuplicate.value = true;
    isSelectRadioResult.value = true;
    isPageLoading.value = true;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    await getEditDynamicHeroDataAsync();
  } else {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }

  if (sourceUrl.includes('replace-live-hero')) {
    isReplaceLiveHero.value = true;
  }
  disabledDates.value = getDisabledDates().value;
  disableList.value = getDisableList().value;
  if (disabledDates.value) {

    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }

  incId.value = todos.value.length;
};
const handleDateClickPopup = (newValue) => {
  scheduleDate.value = newValue;
};
const handleDateChange = () => {
  isCampaignModified.value = true;
  isQuizStatus.value = true;
};
const addEventListeners = () => {
  document.addEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
  document.addEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
};
const checkQuizName = () => {
  const name = quizInputFieldRef.value;
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    quizName.value.trim().length > 0
  ) {
    quizNameinfocus.value = true;
  }
};
const hideQuizTip = () => {
  quizNameinfocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event?.key === 'Escape') {
    closeModal();
  }
};
const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });

    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getDynamicHeroDataAsync:`, error);
  }
};
const getEditDynamicHeroDataAsync = async () => {
  const uuid = route.query[QUERY_PARAM_KEY.UUID];
  if (!uuid) return;

  isPageLoading.value = true;
  triggerLoading("Route Loading", isPageLoading.value);

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid,
    });

    const response = await editDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      processResponseData(response);
    }
  } catch (error) {
    console.error(`Error in getEditDynamicHeroDataAsync:`, error);
  } finally {
    isPageLoading.value = false;
    triggerLoading("Route Loading", isPageLoading.value);
  }
};
const processResponseData = (response) => {
  isLiveHero.value = response.state === 'live' && !isCreateDuplicate.value
  quizName.value = response.title || ''
  isQuizStatus.value = !!response.publishDate
  if (!isCreateDuplicate.value) {
    scheduleDate.value = response.publishDate ? formatJsonTimestamp(response.publishDate) : ''
  }
  if (response.data) {
    processQuizData(response.data)
  }
}
const processQuizData = (data) => {
  quizCTALinkText.value = data.ctaLink || ''
  if (quizCTALinkText.value.trim() !== '') {
    validateisCTALinkInprogress()
  }
  quizQuestionText.value = data.body || ''
  quizResultText.value = data.commentary || ''

  if (data.answer) {
    const answer = data.answer.toLowerCase()
    quizResultList[0].isChecked = answer === 'vrai'
    quizResultList[1].isChecked = answer !== 'vrai'
  }
  isPageLoading.value = false;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
}
const replaceQuizForm = () => {
  isReplacementConfirmPopupVisible.value = true
}
const checkQuestion = () => {
  quizQuestionText.value = quizQuestionText.value.trim() === ' ' ? '' : quizQuestionText.value.replace(/\s+/g, ' ')
}
const checkResult = () => {
  quizResultText.value = quizResultText.value.trim() === ' ' ? '' : quizResultText.value.replace(/\s+/g, ' ')
}

const postEditDynamicHeroDataAsync = async () => {
  try {
    isloading.value = true
    isQuizSaving.value = true
    const answerData = calculateAnswerData()
    const scheduleDateVal = calculateScheduleDate()

    const payload = preparePayload(answerData, scheduleDateVal)

    await postDynamicHeroDataAsync({ payload });

    handleSuccess()
  } catch (error) {
    handleError(error)
  } finally {
    isloading.value = false
    isQuizSaving.value = false
  }
}
const calculateAnswerData = () => {
  let answerData = ''
  quizResultList.forEach((data) => {
    if (data.isChecked) {
      if (data.name === 'True') {
        answerData = 'vrai'
      } else if (data.name === 'False') {
        answerData = 'faux'
      }
    }
  })
  return answerData
}
const calculateScheduleDate = () => {
  if (isReplaceLiveHero.value) {
    isQuizStatus.value = true
    scheduleDate.value = new Date()
    scheduleDate.value.setHours(0, 0, 0, 0)
  }
  const date = new Date(scheduleDate.value)
  return Math.floor(date.getTime() / 1000)
}
const preparePayload = (answerData, scheduleDateVal) => {
  let payload = {
    title: quizName.value?.trim() ?? '',
    template: 'quiz',
    publishDate: !scheduleDate.value || !isQuizStatus.value ? '' : scheduleDateVal,
    image: defaultQuizImage.value,
    data: {
      answer: answerData,
      body: quizQuestionText.value,
      ctaLink: quizCTALinkText.value?.trim() ?? '',
      commentary: quizResultText.value,
    },
    state: isQuizStatus.value && scheduleDate.value ? 'scheduled' : 'draft',
    preview: isHeroPreview.value ?? false,
  }

  if (!quizCTALinkText.value?.trim()) {
    delete payload.data.ctaLink
  }

  return payload
}
const handleSuccess = () => {
  isScheduleQuizPopupVisible.value = false
  isSavingQuizDraft.value = false

  if (!isLiveHero.value && !isReplaceLiveHero.value) {
    const status = isQuizStatus.value && scheduleDate.value ? 'quizScheduled' : 'quizSaved'
    triggerLoading(status)
  } else if (isLiveHero.value && !isReplaceLiveHero.value) {
    triggerLoading('contentLive')
  } else if (isReplaceLiveHero.value) {
    triggerLoading('heroReplaced')
  }

  backToDynamicHeroListConfirm()
}

const handleError = (error) => {
  closeModal()
  console.error(`${$keys.KEY_NAMES.ERROR_IN} postEditDynamicHeroDataAsync:`, error)
}
const saveQuizForm = () => {
  if (scheduleDate.value && isQuizStatus.value) {
    isScheduleQuizPopupVisible.value = true
    scheduleDateConfirm.value = scheduleDate.value
  } else {
    isSavingQuizDraft.value = true
  }
}
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true
  } else {
    backToDynamicHeroListConfirm()
  }
}
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value)
  routeToPage('dynamic-hero')
}
const selectResult = (data, name) => {
  isSelectRadioResult.value = true
  quizResultOption.value = data.key
  quizResultList.forEach((element) => {
    element.isChecked = false
  })
  data.isChecked = true
}
const validateisCTALinkInprogress = () => {
  if (quizCTALinkText.value.trim() === "") {
    isCTALinkInprogress.value = false
    isCTALinkIsValid.value = false
    isCTALinkBroken.value = false
  } else {
    isCTALinkInprogress.value = true
    checkCTALink()
  }
}
const checkCTALink = () => {
  const urlPattern = /^(https?|ftp):\/\/[^/\s]+(\/[^/\s]*)*$/
  const rocheAppPattern = /^(rocheapp):\/\/[^/\s]+(\/[^/\s]*)*$/
  const linkInput = quizCTALinkText.value.trim()

  if (urlPattern.test(linkInput) || (rocheAppPattern.test(linkInput) && linkInput !== "")) {
    isCTALinkIsValid.value = true
  } else {
    isCTALinkBroken.value = true
    isCTALinkIsValid.value = false
  }

  setTimeout(() => {
    isCTALinkInprogress.value = false
  }, 1000)
}
const handleTypeInput = (event) => {
  if (quizInputFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizQuestionFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizResultFieldRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
  if (quizCTALinkRef.value?.contains(event.target)) {
    isCampaignModified.value = true
  }
}

const closeModal = () => {
  scheduleDateConfirm.value = scheduleDate.value
  isReplacementConfirmPopupVisible.value = false
  isSavingQuizDraft.value = false
  isConfirmModalVisible.value = false
  isScheduleQuizPopupVisible.value = false
  isQuizSaving.value = false
}
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onBeforeUnmount(() => {
  document.removeEventListener('input', handleTypeInput)
  document.removeEventListener('keyup', handleESCClickOutside)
})
</script>
