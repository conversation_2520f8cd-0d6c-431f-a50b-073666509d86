<template>
  <content-wrapper
    wrapper-classes="padding-zero"
    :is-body-loading="isPageLoading"
  >
    <div class="edit-content-form-hero">
      <div v-if="!isPageLoading" class="main-content">
        <div class="dynamic-hero-form-edit-id">ID: {{ contentUuid }}</div>
        <dynamicHeroContentHeader
          :isPageOverviewVisible="isPageOverviewVisible"
          :isHeroLive="isHeroLive"
          :isLiveHeroReplaced="isLiveHeroReplaced"
          :isCampaignModified="isCampaignModified"
          :contentName="contentName"
          :contentDescriptionText="contentDescriptionText"
          :checkForTextPresence="checkForTextPresence"
          @back="backToDynamicHeroList"
          @openReplace="openReplacePopup"
          @openSave="openSavePopup"
        />
        <div class="edit-content-intro-section">
          <dynamicHeroContentIntro
            baseClass="edit"
            :heading="$t('DYNAMIC_HERO.CONTENT_FORM')"
            :startDateText="$t('DYNAMIC_HERO.START_DATE')"
            v-model:selectedDate="selectedDate"
            :isLiveHeroReplaced="isLiveHeroReplaced"
            :isHeroLive="isHeroLive"
            :disabledDates="disabledDates"
            :isRange="false"
            :markers="markers"
            @date-click="handleDateClick"
          />
          <dynamicHeroContentForm
            baseClass="edit"
            :isEditPage="true"
            v-model:contentName="contentName"
            :contentList="contentList"
            :selectedDate="selectedDate"
            :isReplaceLiveHero="isLiveHeroReplaced || isHeroLive"
            :isEventStatusDisplayed="isEventStatusDisplayed"
            @selectContent="selectContent"
            @scheduleToggle="scheduleToggle"
            @deleteEvent="deleteEvent"
            :hasContentNameFocus="isContentNameInFocus"
            :checkContentName="checkContentName"
            :hideContentTip="hideContentTip"
          />
        </div>
        <dynamicHeroContentBody
          containerClass="edit-form-container-dynamic"
          :contentList="contentList"
          :selectedArticle="selectedArticle"
          :selectedRecipe="selectedRecipe"
          :selectedCategoryData="selectedCategoryData"
          :description="contentDescriptionText"
          :ctaInput="contentCTAText"
          :showPreview="!isHeroLive"
          :isPreviewEnabled="isHeroPreview"
          @update:description="contentDescriptionText = $event"
          @update:ctaInput="contentCTAText = $event"
          @deleteRecipe="deleteRecipePopUp"
          @deleteCategory="deleteCategoriePopup"
          :isRecipesButtonVisible="isRecipesButtonVisible"
          :isArticlesButtonVisible="isArticlesButtonVisible"
          @update:isPreviewEnabled="[(isHeroPreview = $event), (isCampaignModified = true)]"
          :selectRecipe="selectRecipe"
          :addCategoryButton="addCategoryButton"
          :selectArticle="selectArticle"
          :deleteArticlePopUp="deleteArticlePopUp"
          :replaceArticle="replaceArticle"
          :defaultImage="defaultImage"
          :updateShowAsterisk="updateShowAsterisk"
        />
      </div>
    </div>
    <RecipeModal
      v-if="isRecipeContentFormVisible"
      :recipeList="addRecipeList"
      :lang="lang"
      :query="queryPopUp"
      @update:query="queryPopUp = $event"
      :isTableLoading="isTableDataLoading"
      :defaultImage="defaultImage"
      :canLoadMore="fromPopUp + sizePopUp < addRecipeListTotal"
      :isSearchPopupExitEnable="isSearchPopupExitEnable"
      :selected-item="isItemSelected"
      @close="closeModal"
      @search="searchPopUp"
      @resetQuery="resetPopupQuery"
      @selectRecipe="selectHeroRecipe"
      @confirmSelection="selectedRecipeForContent"
      @loadMore="loadMoreRecipesAsync"
      @done="addRecipeToContent"
    />
    <dynamicHeroArticleModel
      v-if="isArticlesPopupVisible"
      :isAddArticlesMatches="isAddArticlesMatches"
      :searchQuery="searchQuery"
      @update:searchQuery="searchQuery = $event"
      :formattedArticles="formattedArticles"
      :isTableDataLoading="isTableDataLoading"
      :isSearchExitEnable="isSearchPopupExitEnable"
      :defaultImage="defaultImage"
      @close="closeModal"
      @clear-search="clearSearchList"
      @add-article="addHeroArticle"
      @selected-article="SelectedData"
      @done="addArticleToContent"
      :searchArticles="searchArticles"
    />
    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backToDynamicHeroListConfirm"
      :closeModal="closeModal"
    />
    <DynamicHeroScheduleModal
      :isVisible="isScheduleEventPopupVisible"
      :heroData="heroData"
      :disabledDates="disabledDates"
      @close="closeModal"
      @schedule="closeModal"
      :PatchScheduledHero="patchDynamicHeroEditDataAsync"
      :selectedDateValue="scheduleDateConfirm"
      :markers="markers"
      @date-click="handleDateClickPopup"
    />
    <replacementModal
      v-if="isReplacementConfirmPopupVisible"
      :closeModal="closeModal"
      :replacementText="$t('DYNAMIC_HERO.REPLACEMENT_LIVE_HERO')"
      :saveReplacement="patchDynamicHeroEditDataAsync"
      :closeReplacement="closeModal"
    />
    <dynamicHeroAddCategoryModel
      v-if="isAddCategoryModal"
      :queryCategory="queryCategory"
      @update:queryCategory="queryCategory = $event"
      :isSearchExitEnable="isSearchExitEnable"
      :isTableDataLoading="isCategoryTableDataLoading"
      :formattedCategoryPopUp="formattedCategoryPopUp"
      :defaultImage="defaultImage"
      :lang="lang"
      :canLoadMore="fromPopUp + sizePopUp < addCategoriesTotal"
      :saveButtonMessage="'Done'"
      :categoriesTitle="'Add Categories to Content form'"
      :categoriesSubtitle="'Select categories to add to your group.'"
      @close="closeModal"
      @getCategorySearch="getCategorySearch"
      @resetQuery="resetQuery"
      @isCategoryChecked="isCategoryChecked"
      @loadMore="loadUpdatePopUpAsync"
      @done="addCategories"
    />
    <saveModal
      v-if="isSaveDynamicContent"
      :closeModal="closeModal"
      :saveAndPublishFunction="saveDynamicContent"
      :availableLang="[]"
      :buttonName="$t('BUTTONS.SAVE_BUTTON')"
      :description="'Do you want to save as draft your Content form?'"
      :imageName="'@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png'"
    />
    <deleteModal
      v-if="isContentDeleted"
      :closeModal="closeModal"
      :productInfoTitle="'Delete the content?'"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
      :productDescriptionTwo="'content?'"
      :deleteItem="deleteDynamicHeroEditDataAsync"
      :availableLanguage="0"
    />
    <savingModal v-show="isContentSaving" :status="'saving'" />
    <updatingLiveHero
      v-if="isLiveHeroSaveDynamicHero"
      :closeModal="closeModal"
      :callConfirm="saveDynamicContent"
      :updatingText="'Please, confirm the updating live Hero'"
    />
    <deletingModal v-show="isDeletingModalVisible" />
    <deleteModal
      v-if="isDeleteCategoryPopupVisible"
      :closeModal="closeModal"
      :deleteItem="deleteCategory"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_CATEGORY_POPUP')"
      :productDescriptionTwo="'content hero?'"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    <deleteModal
      v-if="isDeleteArticlePopupVisible"
      :closeModal="closeModal"
      :deleteItem="deleteArticle"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_ARTICLE')"
      :productDescriptionOne="'Are you sure you want to remove this article from the'"
      :productDescriptionTwo="'content hero?'"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    <deleteModal
      v-if="isDeleteRecipePopupVisible"
      :closeModal="closeModal"
      :deleteItem="deleteRecipe"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
      :productDescriptionTwo="'content hero?'"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
  </content-wrapper>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { useNuxtApp } from "#app";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import saveModal from "@/components/save-modal";
import savingModal from "@/components/saving-modal";
import deletingModal from "@/components/deleting-modal";
import updatingLiveHero from "@/components/updating-live";
import RecipeService from "@/services/RecipeService";
import deleteModal from "@/components/delete-modal";
import cancelModal from "@/components/cancel-modal";
import dynamicHeroContentBody from "../components/pages/dynamic-hero/dynamic-hero-content-body.vue";
import dynamicHeroContentForm from "../components/pages/dynamic-hero/dynamic-hero-content-form.vue";
import dynamicHeroArticleModel from "../components/pages/dynamic-hero/dynamic-hero-article-model.vue";
import replacementModal from "@/components/confirm-replacement-modal";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import RecipeModal from "../components/recipe-modal.vue";
import dynamicHeroContentIntro from "../components/pages/dynamic-hero/dynamic-hero-content-intro.vue";
import dynamicHeroContentHeader from "../components/pages/dynamic-hero/dynamic-hero-content-header.vue";
import DynamicHeroScheduleModal from "../components/pages/dynamic-hero/dynamic-hero-schedule-modal.vue";
import dynamicHeroAddCategoryModel from "../components/pages/dynamic-hero/dynamic-hero-add-category-model.vue";
import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";
import { useProjectLang } from "@/composables/useProjectLang";
import { useInnitAuthStore } from "../stores/innit-auth.js";
import { useDynamicHeroStore } from "../stores/dynamic-hero.js";
import defaultImage from "~/assets/images/default_recipe_image.png";

const heroData = ref([
  {template: 'Content'}
]);
const selectedArticle = ref([]);
const selectedCategoryData = ref([]);
const selectedRecipe = ref([]);
const contentCTAText = ref("");
const store = useStore();
const route = useRoute();
const { getRef } = useRefUtils();
const { watchReactiveValue } = useWatcherUtils();
const {
  triggerLoading,
  routeToPage,
  splitLangAndCountry,
  updateSelectedItems,
  updateShowAsterisk,
  checkForTextPresence,
  useCalendarMarkers,
  checkDuplicate,
  processScheduledElement,
  isScheduledWithPublishDate,
  getDisabledDates,
  getDisableList,
} = useCommonUtils(
  selectedArticle,
  selectedCategoryData,
  selectedRecipe,
  contentCTAText
);
const { $keys, $auth } = useNuxtApp();
const { isInnitAdmin } = useInnitAuthStore();
const { readyProject, getProject } = useProjectLang();
const isAdminCheck = ref(false);
const isDataChecked = ref(false);
const isDeleteCategoryPopupVisible = ref(false);
const selectedData = ref({});
const isCategoryTableDataLoading = ref(false);
const isTableDataLoading = ref(false);
const isAddCategoryModal = ref(false);
const scheduleDate = ref("");
const formattedCategoryPopUp = ref([]);
const countCategoriesSelected = ref(0);
const categoryUpdateList = ref([]);
const addCategoriesTotal = ref(0);
const formattedCategory = ref([]);
const selectedCategory = ref([]);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const queryCategory = ref("");
const isSearchExitEnable = ref(false);
const isNoRecipeSectionDisplayed = ref(false);
const project = ref({});
const disableList = ref([]);
const includeId = ref(0);
const showLoader = ref(false);
const todos = ref([]);
const isScheduleEventPopupVisible = ref(false);
const formattedArticles = ref([]);
const contentState = ref("");
const scheduleDateConfirm = ref("");
const contentUuid = ref("");
const disabledDates = ref([]);
const isAddArticlesMatches = ref(false);
const totalCategories = ref("");
const isArticlesPopupVisible = ref(false);
const isRecipesButtonVisible = ref(false);
const isArticlesButtonVisible = ref(false);
const isDeletingModalVisible = ref(false);
const isEventStatusDisplayed = ref(false);
const contentName = ref("");
const isContentNameInFocus = ref(false);
const searchQuery = ref("");
const isPageOverviewVisible = ref(false);
const isPageLoading = ref(false);
const isHeroPreview = ref(false);
const selectedDate = ref("");
const isRecipeContentFormVisible = ref(false);
const fromPopUp = ref(0);
const isItemSelected = ref(false);
const sizePopUp = ref(9);
const addRecipeListTotal = ref(0);
const addRecipeList = ref([]);
const recipeDataForhero = ref([]);
const queryPopUp = ref("");
const isSearchPopupExitEnable = ref(false);
const isContentPopupVisible = ref(false);
const contentCTALink = ref("");
const contentDescriptionText = ref("");
const isSaveDynamicContent = ref(false);
const isContentDeleted = ref(false);
const contentList = ref([
  {
    value: "1",
    name: "Article",
    isChecked: false,
  },
  {
    value: "2",
    name: "Recipe",
    isChecked: false,
  },
]);
const isContentSaving = ref(false);
const isHeroLive = ref(false);
const isLiveHeroSaveDynamicHero = ref(false);
const isLiveHeroReplaced = ref(false);
const isReplacementConfirmPopupVisible = ref(false);
const isDeleteArticlePopupVisible = ref(false);
const isDeleteRecipePopupVisible = ref(false);
const dummySelectedRecipe = ref([]);
const dummySelectedArticle = ref([]);
const lang = ref("");
const {
  dynamicHeroDataList,
  getDynamicHeroListDataAsync,
  editDynamicHeroDataList,
  getEditPageDynamicHeroDataAsync,
  patchDynamicHeroDataAsync,
  deleteDynamicHeroDataAsync
} = useDynamicHeroStore();

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
    }
  });
});

const initializeDataAsync = async () => {
  isPageLoading.value = true;
  isAdminCheck.value = isInnitAdmin.value;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  project.value = await getProject();
  lang.value = store.getters["userData/getDefaultLang"];
  await getDynamicHeroDataAsync();
  await getEditDynamicHeroDataAsync();

  const sourceUrl = window.location.href;
  if (sourceUrl.includes("replace-live-hero")) {
    isLiveHeroReplaced.value = true;
  }
  if (sourceUrl.includes("overviewContent")) {
    isPageOverviewVisible.value = true;
  }
  disabledDates.value = await getDisabledDates().value;
  disableList.value = await getDisableList().value;
  if (disabledDates.value) {
    const newTodo = {
      dates: [new Date(), ...disabledDates.value].map(date => new Date(date).toString())
    };
    todos.value.push(newTodo);
  }
  includeId.value = todos.value.length;
};
const { markers } = useCalendarMarkers(disableList);
const addEventListeners = () => {
  document.addEventListener("keyup", handleESCClickOutside);
  document.addEventListener("input", handleTypeInput);
};
const addArticleToContent = () => {
  if (dummySelectedArticle.value.length) {
    selectedArticle.value.push(dummySelectedArticle.value[0]);
  }
  isArticlesPopupVisible.value = false;
  closeModal();
};
const deleteCategory = () => {
  isDeleteCategoryPopupVisible.value = false;
  selectedCategoryData.value = [];
  selectedData.value = {};
  isCampaignModified.value = true;
};
const isCategoryChecked = (cat) => {
  const clonedCategoryPopUp = [...formattedCategoryPopUp.value];
  clonedCategoryPopUp.forEach((item) => {
    if (cat?.isin === item?.isin) {
      item.isChecked = !item.isChecked;
    } else {
      item.isChecked = false;
    }
  });
  formattedCategoryPopUp.value = clonedCategoryPopUp;
  countCategoriesSelected.value = 0;
  selectedData.value = {};
  clonedCategoryPopUp.forEach((item) => {
    if (item?.isChecked) {
      selectedData.value = item;
      countCategoriesSelected.value += 1;
    }
  });
};

const addCategories = () => {
  selectedCategoryData.value = [];
  if (selectedData.value.isin) {
    selectedCategoryData.value.push(selectedData.value);
  }
  isCampaignModified.value = true;
  closeModal();
};
const addCategoryButton = async () => {
  isCategoryTableDataLoading.value = true;
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  countCategoriesSelected.value = 0;
  isAddCategoryModal.value = true;
  addCategoriesTotal.value = 0;
  isTableDataLoading.value = true;
  await getCategoryListAsync();
};
const loadUpdatePopUpAsync = async () => {
  let from = fromPopUp.value + sizePopUp.value;
  if (from < addCategoriesTotal.value) {
    fromPopUp.value = from;
    const loadMoreCategoryList = await getCategoryListAsync();
    if (loadMoreCategoryList && categoryUpdateList.value) {
      await getCategoryStatisticsPopUp(loadMoreCategoryList);
      categoryUpdateList.value = [
        ...categoryUpdateList.value,
        ...loadMoreCategoryList,
      ];
    }
  }
  if (
    selectedData.value &&
    formattedCategoryPopUp.value.length &&
    selectedData.value?.isin
  ) {
    formattedCategoryPopUp.value.forEach((item) => {
      if (item?.isin === selectedData.value?.isin) {
        item.isChecked = true;
      }
    });
  }
};
const getCategorySearch = () => {
  if (queryCategory.value) {
    addCategoriesTotal.value = 0;
    fromPopUp.value = 0;
    formattedCategoryPopUp.value = [];
    categoryUpdateList.value = [];
    getCategoryListAsync();
  }
};
const getSingleCategoryAsync = async (isin) => {
  queryCategory.value = isin;
  try {
    const { language, country } = splitLangAndCountry(lang.value);
    const payload = {
      type: $keys.EVENT_KEY_NAMES.CATEGORY_TYPE,
      country,
      lang: language,
      q: queryCategory.value,
      from: fromPopUp.value,
      size: sizePopUp.value,
      sort: $keys.EVENT_KEY_NAMES.SORT_BY,
    };
    await store.dispatch("categoriesGroup/getCategoryGroupListAsync", {
      payload,
    });
    const response = store.getters["categoriesGroup/getCategoryGroupList"];
    queryCategory.value = "";
    selectedCategoryData.value = [];
    if (response?.results?.length) {
      selectedCategoryData.value.push(response.results[0]);
      selectedCategoryData.value[0].totalRecipes = totalCategories.value;
    } else {
      selectedCategoryData.value = [];
    }
    contentList.value[1].isChecked = true;
  } catch (error) {
    console.error(error);
  }
};
const getTotalCategoryAsync = async (isin) => {
  try {
    const arrayIsin = [isin];
    const { language, country } = splitLangAndCountry(lang.value);
    const payload = {
      type: $keys.EVENT_KEY_NAMES.CATEGORY_TYPE,
      country,
      lang: language,
      isins: arrayIsin.join(","),
    };
    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", {
      payload,
    });
    const response = store.getters["categoriesGroup/getCategoryStatistics"];
    if (response?.length) {
      totalCategories.value = response[0]?.totalRecipes;
      if (selectedCategoryData.value?.[0]) {
        selectedCategoryData.value[0].totalRecipes = totalCategories.value;
      }
    }
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getTotalCategoryAsync:", error);
  }
};
const getCategoryListAsync = async () => {
  try {
    const { language } = splitLangAndCountry(lang.value);
    const payload = {
      lang: language,
      q: queryCategory.value,
      from: fromPopUp.value,
      size: sizePopUp.value,
      type: "category",
      sort: 'lastMod',
    };

    await store.dispatch("categoriesGroup/getCategoryGroupListAsync", {
      payload,
    });
    const response = store.getters["categoriesGroup/getCategoryGroupList"];

    response.results = response?.results?.slice(0, 9);
    response.results.forEach((data) => {
      data.isChecked = false;
      data.isAlreadyAddedCategory = false;
    });

    selectedCategory.value.forEach((category) => {
      response.results.forEach((data) => {
        if (category?.isin === data?.isin) {
          data.isChecked = true;
        }
      });
    });

    if (fromPopUp.value >= 9) {
      response.results.forEach((data) => {
        formattedCategory.value.forEach((item) => {
          if (data?.isin === item?.isin) {
            data.isAlreadyAddedCategory = true;
          }
        });
      });
      return response.results;
    } else {
      response.results.forEach((data) => {
        formattedCategory.value.forEach((item) => {
          if (data?.isin === item?.isin) {
            data.isAlreadyAddedCategory = true;
          }
        });
      });
      categoryUpdateList.value = response?.results ?? [];
      if (!categoryUpdateList.value.length) {
        isNoRecipeSectionDisplayed.value = true;
      }
      if (categoryUpdateList.value.length) {
        getCategoryStatisticsPopUp();
      }
    }

    isSearchExitEnable.value = queryCategory.value !== "";
    addCategoriesTotal.value = response.total;
    isTableDataLoading.value = false;

    if (selectedData.value && formattedCategoryPopUp?.length) {
      formattedCategoryPopUp.forEach((item) => {
        if (item?.isin === selectedData.value?.isin) {
          item.isChecked = true;
        }
      });
    }
  } catch (error) {
    showLoader.value = false;
    isNoRecipeSectionDisplayed.value = true;
    isCategoryTableDataLoading.value = false;
    console.error(`${$keys.KEY_NAMES.ERROR_IN}getCategoryListAsync:`, error);
  }
};
const resetQuery = () => {
  addCategoriesTotal.value = 0;
  fromPopUp.value = 0;
  formattedCategoryPopUp.value = [];
  categoryUpdateList.value = [];
  queryCategory.value = "";
  getCategoryListAsync();
  isSearchExitEnable.value = false;

  if (selectedData.value && formattedCategoryPopUp.value.length > 0) {
    formattedCategoryPopUp.value.forEach((item) => {
      if (item?.isin === selectedData.value?.isin) {
        item.isChecked = true;
      }
    });
  }
};
const getCategoryStatisticsPopUp = async (loadMoreCategoryList) => {
  try {
    let isins = [];
    if (!loadMoreCategoryList) {
      isins = categoryUpdateList.value.map((data) => data.isin);
    } else {
      isins = loadMoreCategoryList.map((data) => data.isin);
    }

    const payload = {
      lang: lang.value.split("-")[0],
      type: "category",
      isins: isins.join(","),
    };

    await store.dispatch("categoriesGroup/getCategoryStatisticsAsync", {
      payload,
    });

    const response = store.getters["categoriesGroup/getCategoryStatistics"];

    totalCategories.value = response[0]?.totalRecipes;

    if (!loadMoreCategoryList) {
      categoryUpdateList.value.forEach((data) => {
        const match = response.find((item) => item?.isin === data?.isin);
        if (match) {
          data.totalRecipes = match.totalRecipes;
        }
      });
      formattedCategoryPopUp.value = [...categoryUpdateList.value];
    }

    if (loadMoreCategoryList) {
      loadMoreCategoryList.forEach((data) => {
        const match = response.find((item) => item?.isin === data?.isin);
        if (match) {
          data.totalRecipes = match.totalRecipes;
        }
      });
      formattedCategoryPopUp.value = [
        ...categoryUpdateList.value,
        ...loadMoreCategoryList,
      ];
    }
    formattedCategoryPopUp.value.forEach(
      item => item.isChecked = item.isin === selectedData.value?.isin
    );
    isCategoryTableDataLoading.value = false;
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  } catch (error) {
    showLoader.value = false;
  }
};
const deleteCategoriePopup = () => {
  isDeleteCategoryPopupVisible.value = true;
};
const checkContentName = () => {
  let name = getRef("contentNameRef");
  if (
    name?.scrollWidth > name?.clientWidth &&
    name !== document.activeElement &&
    contentName.value.trim().length > 0
  ) {
    isContentNameInFocus.value = true;
  }
};
const hideContentTip = () => {
  isContentNameInFocus.value = false;
};
const handleESCClickOutside = (event) => {
  if (event && event.key === "Escape") {
    closeModal();
  }
};

const getDynamicHeroDataAsync = async () => {
  try {
    await getDynamicHeroListDataAsync({ lang: lang.value });
    const response = await dynamicHeroDataList.value;
    if (response && Object.keys(response).length) {
      checkDuplicate();
      response.forEach((element) => {
        if (isScheduledWithPublishDate(element)) {
          processScheduledElement(element);
        }
      });
    }
  } catch (error) {
    console.error(
      `${$keys.KEY_NAMES.ERROR_IN} getDynamicHeroDataAsync:`,
      error
    );
  }
};
const handleDateClickPopup = (newValue) => {
  selectedDate.value = newValue;
};
const handleDateClick = () => {
  isEventStatusDisplayed.value = true;
  isCampaignModified.value = true;
};
const openSavePopup = () => {
  if (isHeroLive.value) {
    isLiveHeroSaveDynamicHero.value = true;
  } else if (selectedDate.value && isEventStatusDisplayed.value) {
    isScheduleEventPopupVisible.value = true;
    scheduleDateConfirm.value = selectedDate.value;
    isSaveDynamicContent.value = false;
  } else {
    isSaveDynamicContent.value = true;
    isScheduleEventPopupVisible.value = false;
  }
};
const getEditDynamicHeroDataAsync = async () => {
  isPageLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  if (!route.query[QUERY_PARAM_KEY.UUID]) {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    return;
  }

  try {
    await getEditPageDynamicHeroDataAsync({
      lang: lang.value,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });
    const response = editDynamicHeroDataList.value;
    if (Object.keys(response).length) {
      handleResponseState(response);
      await handleResponseData(response);
    }
  } catch (error) {
    console.error("Error in getEditDynamicHeroDataAsync:", error);
  } finally {
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  }
};

const handleResponseState = (response) => {
  isHeroLive.value = response.state === "live";
  isHeroPreview.value = response.preview || false;
  contentState.value = response.state || "";
  isEventStatusDisplayed.value = !!response.publishDate;
  selectedDate.value = response.publishDate
    ? getTime(response.publishDate)
    : "";
  contentName.value = response.title || "";
  contentUuid.value = response.uuid || "";
  if (contentState.value === "draft" && selectedDate.value) {
    isEventStatusDisplayed.value = false;
    selectedDate.value = "";
  }
};

const handleResponseData = async (response) => {
  if (!response.data) return;

  contentCTALink.value = response.data.ctaLink || "";
  contentCTAText.value = response.data.ctaText || "";
  contentDescriptionText.value = response.data.body || "";

  if (contentCTALink.value.includes("rocheapp://recipe?isin=")) {
    await handleRecipeLinkAsync(contentCTALink.value);
  } else if (contentCTALink.value.includes("rocheapp://learn?id=")) {
    await handleLearnLink(contentCTALink.value);
  } else if (contentCTALink.value.includes("rocheapp://recipeCategory?isin=")) {
    handleRecipeCategoryLink(contentCTALink.value);
  }

  if (selectedArticle.value.length === 0) {
    contentList.value[0].isChecked = false;
    isArticlesButtonVisible.value = false;
  }
};

const handleRecipeLinkAsync = async (ctaLink) => {
  const isin = ctaLink.replace("rocheapp://recipe?isin=", "");
  isRecipesButtonVisible.value = true;

  const isIsinEmpty = !isin;
  isDataChecked.value = isIsinEmpty;
  contentList.value[0].isChecked = false;
  contentList.value[1].isChecked = !isIsinEmpty;

  if (!isIsinEmpty) {
    await getRecipeAsync(isin);
  }
};

const handleLearnLink = async (ctaLink) => {
  let uuid = ctaLink.replace("rocheapp://learn?id=", "");
  isArticlesButtonVisible.value = true;
  contentList.value[0].isChecked = true;
  contentList.value[1].isChecked = false;
  await getEditArticlesDataAsync(uuid);
};

const handleRecipeCategoryLink = (ctaLink) => {
  const categoryId = ctaLink.replace("rocheapp://recipeCategory?isin=", "");
  getTotalCategoryAsync(categoryId);
  getSingleCategoryAsync(categoryId);
  isRecipesButtonVisible.value = true;
};
const getRecipeAsync = async (isin) => {
  const params = {
    country: lang.value,
  };
  try {
    await store.dispatch("recipe/getRecipeAsync", {
      params,
      isin: isin,
    });
    const response = store.getters["recipe/getRecipeData"];
    selectedRecipe.value.push(response);
  } catch (error) {
    console.error("Error in getRecipeAsync:", error);
  }
};
const getEditArticlesDataAsync = async (uuid) => {
  if (!uuid) {
    return;
  }

  const uuidParam = uuid?.trim()
  if (!uuidParam || uuidParam === "undefined" || uuidParam === "null") {
    return;
  }

  try {
    await store.dispatch("articles/getEditArticlesDataAsync", {
      lang: lang.value,
      uuid: uuidParam,
    });
    const response = store.getters["articles/getEditArticleData"];
    updateSelectedItems(selectedArticle.value, response);
  } catch (error) {
    console.error(
      $keys.KEY_NAMES.ERROR_IN + "getEditArticlesDataAsync:",
      error
    );
    return null;
  }
};
const saveDynamicContent = () => {
  isSaveDynamicContent.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  patchDynamicHeroEditDataAsync();
};
const openReplacePopup = () => {
  isReplacementConfirmPopupVisible.value = true;
};
const scheduleToggle = (newStatus) => {
  isCampaignModified.value = true;
  isEventStatusDisplayed.value = newStatus;
};
const setSavingState = (isSaving) => {
  isContentSaving.value = isSaving;
  isScheduleEventPopupVisible.value = !isSaving;
};

const createPayload = () => {
  const scheduleDateValue = calculateScheduleDate();
  const payloadImage = getPayloadImage();
  const payloadCTAlink = getPayloadCTALink();
  const { title, image, backgroundColor, titleColor } = getDataFields();

  let payload = {
    uuid: route.query[QUERY_PARAM_KEY.UUID],
    title: contentName.value.trim(),
    template: "content",
    publishDate:
      !selectedDate.value || !isEventStatusDisplayed.value ? "0" : scheduleDateValue,
    image: payloadImage,
    data: {
      title,
      image,
      backgroundColor,
      titleColor,
      ctaLink: payloadCTAlink,
      ctaText: contentCTAText.value,
      body: contentDescriptionText.value,
    },
    state:
      !selectedDate.value || !isEventStatusDisplayed.value
        ? "draft"
        : "scheduled",
    preview: isHeroPreview.value,
  };

  if (isEmptySelection()) {
    delete payload.data.image;
    delete payload.data.backgroundColor;
    delete payload.data.title;
    delete payload.data.titleColor;
    delete payload.data.ctaLink;
  }

  if (!contentCTAText.value) {
    delete payload.data.ctaText;
  }

  if (!selectedDate.value || !isEventStatusDisplayed.value) {
    delete payload.publishDate;
  }

  return payload;
};

const handleApiCall = async (payload) => {
  try {
    await patchDynamicHeroDataAsync({
      payload,
      uuid: route.query[QUERY_PARAM_KEY.UUID],
    });
    handleSuccess();
    routeToPage("dynamic-hero");
  } catch (error) {
    console.error(
      $keys.KEY_NAMES.ERROR_IN + "patchDynamicHeroEditDataAsync:",
      error
    );
  }
};

const patchDynamicHeroEditDataAsync = async () => {
  setSavingState(true);
  const payload = createPayload();
  await handleApiCall(payload);
  setSavingState(false);
};

const calculateScheduleDate = () => {
  if (isLiveHeroReplaced.value || isHeroLive.value) {
    selectedDate.value = new Date();
    selectedDate.value.setHours(0, 0, 0, 0);
    const unixTimestampMs = selectedDate.value.getTime();
    isEventStatusDisplayed.value = true;
    return Math.floor(unixTimestampMs / 1000);
  } else {
    const date = new Date(selectedDate.value);
    return Math.floor(date.getTime() / 1000);
  }
};
const getPayloadImage = () => {
  if (selectedRecipe.value.length && contentList.value[1].isChecked) {
    return (
      selectedRecipe.value[0]?.media[lang.value]?.image ||
      selectedRecipe.value[0]?.media[lang.value]?.externalImageUrl ||
      ""
    );
  } else if (selectedArticle.value.length && contentList.value[0].isChecked) {
    return selectedArticle.value[0].image || "";
  } else if (selectedCategoryData.value.length && contentList.value[1].isChecked) {
    return selectedCategoryData.value[0].data[lang.value].image || "";
  }
  return "";
};
const getPayloadCTALink = () => {
  if (selectedRecipe.value.length && contentList.value[1].isChecked) {
    return `rocheapp://recipe?isin=${selectedRecipe.value[0].isin}`;
  } else if (selectedArticle.value.length && contentList.value[0].isChecked) {
    return `rocheapp://learn?id=${selectedArticle.value[0].uuid}`;
  } else if (selectedCategoryData.value.length && contentList.value[1].isChecked) {
    return `rocheapp://recipeCategory?isin=${selectedCategoryData.value[0].isin}`;
  }
  return "";
};
const getDataFields = () => {
  const isRecipeOrCategory =
    selectedRecipe.value.length || selectedCategoryData.value.length;
  const isArticle = selectedArticle.value.length;

  let title = "";
  let image = "";
  let backgroundColor = "";
  let titleColor = "";

  if (isRecipeOrCategory) {
    title = "Nouvelles recettes";
    image =
      "https://roche-education.s3.eu-central-1.amazonaws.com/Phil_cook.png";
    backgroundColor = "#EFF2FA";
    titleColor = "#4ABED4";
  } else if (isArticle) {
    title = "Nouveaux articles";
    image =
      "https://roche-education.s3.eu-central-1.amazonaws.com/Phil_read.png";
    backgroundColor = "#D8F0E7";
    titleColor = "#00965E";
  }

  return {
    title,
    image,
    backgroundColor,
    titleColor,
  };
};
const isEmptySelection = () => {
  return (
    selectedRecipe.value.length === 0 &&
    selectedArticle.value.length === 0 &&
    selectedCategoryData.value.length === 0
  );
};
const handleSuccess = () => {
  isContentSaving.value = false;

  if (!isEventStatusDisplayed.value || !selectedDate.value) {
    if (!isHeroLive.value && !isLiveHeroReplaced.value) {
      triggerLoading("contentSaved");
    }
  } else if (isEventStatusDisplayed.value && selectedDate.value) {
    if (!isHeroLive.value && !isLiveHeroReplaced.value) {
      triggerLoading("contentScheduled");
    }
  } else if (isHeroLive.value && !isLiveHeroReplaced.value) {
    triggerLoading("contentLive");
  } else if (isLiveHeroReplaced.value) {
    triggerLoading("heroReplaced");
  }
};
const deleteDynamicHeroEditDataAsync = async () => {
  isDeletingModalVisible.value = true;

  try {
    await deleteDynamicHeroDataAsync({ uuid: route.query[QUERY_PARAM_KEY.UUID] });
    routeToPage("dynamic-hero");
    triggerLoading("newDeletedSuccess");
    closeModal();
  } catch (error) {
    closeModal();
    console.error(
      $keys.KEY_NAMES.ERROR_IN + "deleteDynamicHeroEditDataAsync:",
      error
    );
  }
};
const searchArticles = () => {
  if (searchQuery.value) {
    isTableDataLoading.value = true;
    if (searchQuery.value.length) {
      const filteredArticles = [];

      if (!formattedArticles.value.length) {
        getArticleDataAsync();
        formattedArticles.value.forEach((group) => {
          const filteredContent = group.content.filter((item) =>
            item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
          );
          if (filteredContent.length > 0) {
            filteredArticles.push({ ...group, content: filteredContent });
          }
        });
      }

      formattedArticles.value.forEach((group) => {
        const filteredContent = group.content.filter((item) =>
          item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
        if (filteredContent.length > 0) {
          filteredArticles.push({ ...group, content: filteredContent });
        }
      });

      formattedArticles.value = filteredArticles;
      isSearchPopupExitEnable.value = true;
    } else {
      searchQuery.value = "";
      getArticleDataAsync();
      isSearchPopupExitEnable.value = false;
    }
    isTableDataLoading.value = false;
  }
};
const clearSearchList = () => {
  searchQuery.value = "";
  getArticleDataAsync();
  isSearchPopupExitEnable.value = false;
};
const deleteEvent = () => {
  isContentDeleted.value = true;
};
const backToDynamicHeroList = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToDynamicHeroListConfirm();
  }
};
const backToDynamicHeroListConfirm = () => {
  isCampaignModified.value = false;
  triggerLoading($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  if (isPageOverviewVisible.value) {
    routeToPage("overview");
  } else {
    routeToPage("dynamic-hero");
  }
};
const replaceArticle = () => {
  isAddArticlesMatches.value = true;
  isArticlesPopupVisible.value = true;
  getArticleDataAsync();
  formattedArticles.value.forEach((data) => {
    data.content.forEach((item) => {
      item.isDisable = false;
      item.isAdded = false;
    });
  });
};

const selectArticle = () => {
  getArticleDataAsync();
  isAddArticlesMatches.value = true;
  isArticlesPopupVisible.value = true;
};

const deleteArticlePopUp = () => {
  isDeleteArticlePopupVisible.value = true;
};
const deleteArticle = () => {
  selectedArticle.value = [];
  isCampaignModified.value = true;
  isDeleteArticlePopupVisible.value = false;
};
const deleteRecipePopUp = () => {
  isDeleteRecipePopupVisible.value = true;
};
const deleteRecipe = () => {
  selectedRecipe.value = [];
  isCampaignModified.value = true;
  isDeleteRecipePopupVisible.value = false;
};
const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};
const getArticleDataAsync = async () => {
  formattedArticles.value = [];
  isTableDataLoading.value = true;

  try {
    const response = await store.dispatch("articles/getArticleDataAsync", {
      lang: lang.value,
    });

    const dummyUUID = dummySelectedArticle.value?.[0]?.uuid;
    const selectedUUID = selectedArticle.value?.[0]?.uuid;

    formattedArticles.value = response.map((article) => {
      const updatedContent = article.content.map((item) => {
        const isDummyMatched = dummyUUID && item.uuid === dummyUUID;
        const isSelectedMatched = selectedUUID && item.uuid === selectedUUID;

        let isDisable;
        if (dummyUUID) {
          isDisable = !isDummyMatched;
        } else {
          isDisable = Boolean(selectedUUID);
        }

        return {
          ...item,
          isAdded: isDummyMatched || isSelectedMatched,
          isDisable,
        };
      });
      return { ...article, content: updatedContent };
    });

    if (selectedUUID) {
      const articleIndex = formattedArticles.value.findIndex((article) =>
        article.content.some((item) => item.uuid === selectedUUID)
      );

      if (articleIndex > 0) {
        const [selectedArticleData] = formattedArticles.value.splice(articleIndex, 1);
        formattedArticles.value.unshift(selectedArticleData);
      }

      const selectedArticleContent = formattedArticles.value[0].content;
      const contentIndex = selectedArticleContent.findIndex((item) => item.uuid === selectedUUID);

      if (contentIndex > 0) {
        const [selectedItem] = selectedArticleContent.splice(contentIndex, 1);
        selectedArticleContent.unshift(selectedItem);
      }
    }

  } catch (error) {
    console.error(error);
  } finally {
    isTableDataLoading.value = false;
  }
};
const addHeroArticle = (uuid) => {
  isCampaignModified.value = true;
  contentCTALink.value = `rocheapp://learn?id=${uuid}`;
  selectedArticle.value = [];
  dummySelectedArticle.value = [];

  formattedArticles.value.forEach((data) => {
    data.content.forEach((item) => {
      if (item?.uuid === uuid) {
        dummySelectedArticle.value.push(item);
        item.isAdded = true;
        selectedRecipe.value = [];
      } else {
        item.isDisable = true;
      }
    });
  });
};

const searchPopUp = () => {
  if (queryPopUp.value !== "") {
    isSearchPopupExitEnable.value = queryPopUp.value !== "";
    isTableDataLoading.value = true;
    fromPopUp.value = 0;
    getRecipeDataForSearcAsync();
    const scroll = getRef("addTable");
    scroll?.scrollTo(0, 0);
  }
};

const resetPopupQuery = () => {
  queryPopUp.value = "";
  fromPopUp.value = 0;
  getRecipeDataForHeroAsync();
  searchPopUp();
  isSearchPopupExitEnable.value = false;
};
const selectRecipe = () => {
  fromPopUp.value = 0;
  getRecipeDataForHeroAsync();
  isRecipeContentFormVisible.value = true;
  isTableDataLoading.value = true;
};
const getRecipeDataForHeroAsync = async () => {
  let isin = "";
  try {
    const response = await RecipeService.getRecipeForCategoriesPopUp(
      project.value,
      queryPopUp.value,
      isin,
      fromPopUp.value,
      sizePopUp.value,
      lang.value,
      store,
      $auth
    );

    response.results.forEach((data) => {
      data.isAlreadyinCategoryRecipe = false;
      data.isAdded = false;
      data.dropDown = false;
      data.isSelected = true;
      data.isSearched = false;

      dummySelectedRecipe.value.forEach((item) => {
        if (data.isin === item.isin) {
          data.isAdded = true;
        }
      });
    });

    addRecipeListTotal.value = response.total;

    if (fromPopUp.value >= 10) {
      response.results.forEach((data) => {
        recipeDataForhero.value.forEach((item) => {
          if (data.isin === item.isin) {
            data.isAlreadyinCategoryRecipe = true;
          }
        });
      });
    } else {
      addRecipeList.value = response.results;
      addRecipeList.value.forEach((data) => {
        recipeDataForhero.value.forEach((item) => {
          if (data.isin === item.isin) {
            data.isAlreadyinCategoryRecipe = true;
          }
        });
      });
    }

    if (selectedRecipe.value.length) {
      let check = false;
      addRecipeList.value.forEach((data, index) => {
        if (data.isin === selectedRecipe.value[0].isin) {
          check = true;
          data.isAdded = true;

          if (index !== 0) {
            const temp = addRecipeList.value[0];
            addRecipeList.value[0] = addRecipeList.value[index];
            addRecipeList.value[index] = temp;
          }
        } else {
          isItemSelected.value = true;
        }
      });

      if (!check) {
        isTableDataLoading.value = true;
        await loadMoreRecipesAsync();
      }
    }

    isTableDataLoading.value = false;
    return response;
  } catch (error) {
    console.error("Error fetching recipe data:", error);
    isTableDataLoading.value = false;
  }
};
const getRecipeDataForSearcAsync = async () => {
  try {
    const response = await RecipeService.getRecipeForCategoriesPopUp(
      project.value,
      queryPopUp.value,
      route.query[QUERY_PARAM_KEY.ISIN],
      fromPopUp.value,
      sizePopUp.value,
      lang.value,
      store,
      $auth
    );

    response.results.forEach((data) => {
      data.isAlreadyinCategoryRecipe = false;
      data.isAdded = false;
      data.dropDown = false;
      data.isSelected = true;
      data.isSearched = false;

      dummySelectedRecipe.value.forEach((item) => {
        if (data.isin === item.isin) {
          data.isAdded = true;
        }
      });
    });

    addRecipeListTotal.value = response.total;

    if (fromPopUp.value >= 10) {
      response.results.forEach((data) => {
        recipeDataForhero.value.forEach((item) => {
          if (data.isin === item.isin) {
            data.isAlreadyinCategoryRecipe = true;
          }
        });
      });
    } else {
      addRecipeList.value = response.results;
      addRecipeList.value.forEach((data) => {
        recipeDataForhero.value.forEach((item) => {
          if (data.isin === item.isin) {
            data.isAlreadyinCategoryRecipe = true;
          }
        });
      });
    }

    if (selectedRecipe.value.length) {
      let check = false;
      addRecipeList.value.forEach((data, index) => {
        if (data.isin === selectedRecipe.value[0].isin) {
          check = true;
          data.isAdded = true;

          if (index !== 0) {
            const temp = addRecipeList.value[0];
            addRecipeList.value[0] = addRecipeList.value[index];
            addRecipeList.value[index] = temp;
          }
        } else {
          isItemSelected.value = true;
        }
      });

      if (!check) {
        isTableDataLoading.value = true;
        await loadMoreRecipesAsync();
      }
    }

    isTableDataLoading.value = false;
    return response;
  } catch (error) {
    console.error(error);
    isTableDataLoading.value = false;
  }
};
const loadMoreRecipesAsync = async () => {
  const from = fromPopUp.value + sizePopUp.value;
  if (from < addRecipeListTotal.value) {
    fromPopUp.value = from;
    const loadMoreRecipeData = await getRecipeDataForHeroAsync();
    if (loadMoreRecipeData?.results) {
      addRecipeList.value = [
        ...addRecipeList.value,
        ...loadMoreRecipeData.results,
      ];
    }
  }
};
const closeConfirmModal = () => {
  dummySelectedRecipe.value = [];
  queryPopUp.value = "";
  isSearchPopupExitEnable.value = false;
  isItemSelected.value = false;
  closeModal();
  fromPopUp.value = 0;
};
const addRecipeToContent = () => {
  if (dummySelectedRecipe.value.length) {
    selectedRecipe.value.push(dummySelectedRecipe.value[0]);
  }
  closeConfirmModal();
};
const selectHeroRecipe = (recipe) => {
  contentCTALink.value = `rocheapp://learn?isin=${recipe.isin}`;
  if (selectedRecipe.value) {
    selectedRecipe.value.isAdded = false;
  }
  recipe.isAdded = true;

  if (selectedRecipe.value.length) {
    selectedRecipe.value = [];
    dummySelectedRecipe.value = [];
  }
  dummySelectedRecipe.value.push(recipe);
  selectedArticle.value = [];
  isItemSelected.value = true;
  isCampaignModified.value = true;
};
const selectedRecipeForContent = (recipe) => {
  contentCTALink.value = "";
  if (selectedRecipe.value) {
    selectedRecipe.value.isAdded = false;
  }
  recipe.isAdded = false;
  selectedArticle.value = [];
  selectedRecipe.value = [];
  dummySelectedRecipe.value = [];
  isItemSelected.value = false;
  isCampaignModified.value = true;
};
const SelectedData = (uuid) => {
  isCampaignModified.value = true;
  contentCTALink.value = "";
  selectedRecipe.value = [];
  selectedArticle.value = [];
  dummySelectedArticle.value = [];
  formattedArticles.value.forEach((data) => {
    data.content.map((item) => {
      if (item.isAdded) {
        item.isAdded = false;
        item.isDisable = false;
      }
    });
  });
  formattedArticles.value.forEach((data) => {
    data.content.map((item) => {
      if (item?.uuid === uuid) {
        selectedArticle.value.push(item);
        item.isAdded = false;
        selectedRecipe.value = [];
      } else {
        item.isDisable = false;
      }
    });
  });
  selectedRecipe.value = [];
};
const closeModal = () => {
  queryPopUp.value = "";
  dummySelectedRecipe.value = [];
  isItemSelected.value = false;
  fromPopUp.value = 0;
  queryCategory.value = "";
  selectedData.value = "";
  isDeleteCategoryPopupVisible.value = false;
  isAddCategoryModal.value = false;
  scheduleDateConfirm.value = scheduleDate.value;
  searchQuery.value = "";
  isSearchPopupExitEnable.value = false;
  isReplacementConfirmPopupVisible.value = false;
  isScheduleEventPopupVisible.value = false;
  isSaveDynamicContent.value = false;
  isRecipeContentFormVisible.value = false;
  isContentPopupVisible.value = false;
  isArticlesPopupVisible.value = false;
  isConfirmModalVisible.value = false;
  isContentDeleted.value = false;
  isLiveHeroSaveDynamicHero.value = false;
  isDeletingModalVisible.value = false;
  if (!isLiveHeroReplaced.value) {
    isLiveHeroReplaced.value = false;
  }
  isDeleteArticlePopupVisible.value = false;
  isDeleteRecipePopupVisible.value = false;
};
const selectContent = (data, name) => {
  contentList.value.forEach((element) => {
    element.isChecked = false;
  });
  data.isChecked = true;
  if (name === "Article") {
    isAddArticlesMatches.value = true;
    isArticlesButtonVisible.value = true;
    isRecipesButtonVisible.value = false;
  } else if (name === "Recipe") {
    isRecipesButtonVisible.value = true;
    isArticlesButtonVisible.value = false;
    isDataChecked.value = false;
  }
};

const handleTypeInput = (event) => {
  if (getRef("contentNameRef")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("descriptionNotes")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
  if (getRef("contentCTALink")?.contains(event.target)) {
    isCampaignModified.value = true;
  }
};
watchReactiveValue(isCampaignModified, $keys.KEY_NAMES.CAMPAIGN_MODIFIED);
onUnmounted(() => {
  document.removeEventListener($keys.KEY_NAMES.INPUT, handleTypeInput);
  document.removeEventListener($keys.KEY_NAMES.KEYUP, handleESCClickOutside);
});
</script>
