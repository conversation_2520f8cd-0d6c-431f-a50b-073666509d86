<template>
  <client-only>
    <content-wrapper :is-body-loading="tm_isLoading">
      <template v-slot:title> <span>{{ $t('CATEGORY_GROUP.CATEGORY_GROUPS') }}</span> </template>

      <template v-slot:head>
        <button
          v-if="!isSearchEnabled"
          type="button"
          class="btn-green"
          @click="openCategoryGroupPopup()"
        >
          {{$t('CATEGORY_GROUP.NEW_GROUP') }}
        </button>
      </template>

      <block-wrapper is-transparent>
        <template v-if="isSearchEnabled" v-slot:title>{{ $t('COMMON.SEARCH_RESULT') }}</template>

        <simple-table
          v-if="tm_rows.length"
          :column-names="columnNames"
          :column-keys="columnKeys"
          :data-source="tm_rows"
          table-class="cat-group-table"
        >
          <template v-slot:image="props">
            <img
              :src="props.data?.image || defaultImage"
              :src-placeholder="defaultImage"
              :alt="props.data?.name"
              class="simple-table-img"
            />
          </template>

          <template v-slot:isin="props">
            <span>{{ props.data?.isin }}</span>
          </template>

          <template v-slot:title="props">
            <span class="text-14 font-bold">{{ props.data?.name }}</span>
            <languages-alert
              :languages="props.data?.langs"
              :has-alert="props.data?.hasAlert"
              :lang="tm_lang"
              :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
            ></languages-alert>
          </template>

          <template v-slot:count="props">
            <p class="text-14 font-bold color-gray">
              {{ props.data?.totalCategories }}
              <span v-if="props.data?.totalCategories > 1">Categories</span>
              <span v-else>Category</span>
              <span>|</span>
              {{ props.data?.totalRecipes }}
              <span v-if="props.data?.totalRecipes > 1">Recipes</span>
              <span v-else>Recipe</span>
            </p>
            <p class="cat-group-table-name-categories text-14 color-gray">{{ props.data?.nameCategories }}</p>
          </template>

          <template v-slot:status="props">
            <badge
              :label="$t(STATE_MAPPING[props.data?.state].tKey)"
              :badge-type="STATE_MAPPING[props.data?.state].badgeType"
              :img-src="STATE_MAPPING[props.data?.state].icon"
            ></badge>
          </template>

          <template v-slot:actions="props">
            <simple-actions
              :is-edit-btn-disabled="isActionDisabled(props.data?.state)"
              :is-edit-info-tooltip-showing="isActionDisabled(props.data?.state)"
              @editOnClick="editCategoryGroup(props.data)"
              :is-delete-btn-disabled="isActionDisabled(props.data?.state) || props.data?.totalRecipes !== 0"
              :is-delete-info-tooltip-showing="isActionDisabled(props.data?.state)"
              :delete-btn-warn-tooltip-text="$t('CATEGORY_GROUP.SIMPLE_ACTIONS_DELETE_TOOLTIP')"
              @deleteOnClick="openDeleteModal(props.data)"
            ></simple-actions>
          </template>
        </simple-table>

        <simple-paginate
          :is-pagination-enabled="isPaginationShowing"
          :pagination-total="tm_pagination.total"
          :pagination-size="tm_pagination.size"
        />

        <noResultFound
          v-if="!tm_rows.length && tm_isFirstLoadCompleted"
          :isReloadRequired="false"
          :isContentSearched="isSearchEnabled"
          :noResultText="$t('NO_RESULT.CATEGORIES_GROUP')">
        </noResultFound>
      </block-wrapper>

    </content-wrapper>
  </client-only>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import noResultFound from "@/components/no-result-found";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import Badge from "@/components/badge/badge.vue";
import { STATE_MAPPING } from "@/сonstants/state-mapping";
import BlockWrapper from "@/components/block-wrapper/block-wrapper.vue";
import SimpleActions from "@/components/simple-actions/simple-actions.vue";
import { useNuxtApp } from '#app';
import defaultImage from "~/assets/images/default_recipe_image.png";
import { useI18n } from 'vue-i18n';
import { useProjectLang } from "@/composables/useProjectLang";
import { useRouter } from 'vue-router';
import SimplePaginate from "../components/simple-paginate.vue";
import { useTableManager } from "../composables/useTableManager.js";
import { LOCAL_TRACKER_CONFIG } from "../сonstants/trackerConfig.js";
import { useCommonUtils } from "../composables/useCommonUtils.js";
import { useQueryUtils } from "../composables/useQueryUtils.js";
import { useSearchStore } from "../stores/search.js";
import { categoryGroupModel } from "../models/category-group.model.js";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import ConfirmModal from "../components/modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../models/confirm-modal.model.js";
import ProcessModal from "../components/modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";

const { $keys } = useNuxtApp();
const router = useRouter();
const { $tracker } = useNuxtApp();
const store = useStore();
const { t } = useI18n();
const { readyProject } = useProjectLang();
const { triggerLoading } = useCommonUtils();
const { getPageQuery, getSearchQuery } = useQueryUtils();
const { isSearchEnabled } = useSearchStore();
const {
  tm_rows,
  tm_pagination,
  tm_isLoading,
  tm_isFirstLoadCompleted,
  tm_lang,
  tm_fetch,
} = useTableManager({
  storeId: "cat-group",
  clientKey: "flite",
  endpointKey: "getCategoryGroupMasterData",
  defaultParams: {
    includeCategories: true,
  },
  smoothUpdate: true,
  resultModel: categoryGroupModel,
});

const { openModal, closeModal } = useBaseModal({
  "deleteModal": {
    component: ConfirmModal,
    skipClickOutside: true,
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: t('DESCRIPTION_POPUP.DELETE_CATEGORY_GROUP'),
      description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} category group?`,
    },
  },
  "deletingModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
});

const columnNames = ref([
  "",
  t('CATEGORY_GROUP.GROUP_ISIN'),
  t('CATEGORY_GROUP.GROUP_TITLE'),
  t('CATEGORY_GROUP.CATEGORY_AND_RECIPE_COUNT'),
  t('COMMON.STATUS'),
  "",
]);
const columnKeys = ref(['image', 'isin', 'title', 'count', 'status', 'actions']);

const isPaginationShowing = computed(() => !!((tm_pagination.value.total > tm_pagination.value.size) && tm_rows.value.length));

const editCategoryGroup = (data) => {
  if (data?.state === $keys.STATE.PUBLISHING) {
    return;
  }

  router.push({
    path: "/edit-cat-group",
    query: {
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
      [QUERY_PARAM_KEY.ISIN]: data?.isin,
      [QUERY_PARAM_KEY.SEARCH]: getSearchQuery(),
    },
  });
};
const openDeleteModal = (category) => {
  if (category.state === $keys.STATE.PUBLISHING || category.totalRecipes !== 0) {
    return;
  }

  openModal({
    name: "deleteModal",
    onClose: (result) => result && deleteCategoryGroupListAsync(category.isin),
  });
};

const deleteCategoryGroupListAsync = async (isin) => {
  const wentWrong = () => {
    triggerLoading("somethingWentWrong");
    tm_fetch({});
  };

  if (!isin) {
    wentWrong();
    return;
  }

  openModal({ name: "deletingModal" });
  try {
    await store.dispatch("categoriesGroup/deleteCategoryGroupListAsync", { isin });
    triggerLoading("newDeletedSuccess");
  } catch {
    wentWrong();
  } finally {
    tm_fetch({});
    closeModal("deletingModal");
  }
};
const openCategoryGroupPopup = () => {
  router.push({
    path: "/add-cat-group",
    query: {
      [QUERY_PARAM_KEY.BACK_FROM]: getPageQuery(),
    },
  });
};

const resetStore = () => store.dispatch("categoriesGroup/resetCategoryGroupList");
const isActionDisabled = (state) => state === $keys.STATE.PUBLISHING || state === $keys.STATE.UNPUBLISHING;

onMounted(() => {
  resetStore();
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      $tracker.sendEvent($keys.EVENT_KEY_NAMES.VIEW_CATEGORY_GROUP, {}, { ...LOCAL_TRACKER_CONFIG });
    }
  });
});
onBeforeUnmount(() => {
  resetStore();
});
</script>
