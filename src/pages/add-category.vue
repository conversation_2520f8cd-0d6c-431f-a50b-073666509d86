<template>
  <client-only>
    <content-wrapper class="padding-zero">
      <div class="add-category-main-container">
        <div class="background-image-add-categories">
          <img alt="background" class="background-image" :src="`${image}`" />
          <button
            type="button"
            class="back-btn"
            @click="
              backToCategories();
              dropDownClose();
            "
          >
            <img
              alt="back arrow"
              class="back-arrow-image"
              src="../assets/images/back-arrow.png"
            />
            <span class="back-to-categories text-title-2">{{
              $t('CATEGORY.BACK_MESSAGE')
            }}</span>
          </button>
          <div class="head-btn">
            <button type="button"
              :class="
                isCampaignModified &&
                categoriesName.trim() !== '' &&
                productImage &&
                !isRecipeVariantNameEmpty &&
                !isSlugCheckConfirm &&
                (uploadImagePercentage == 0 || uploadImagePercentage == 100)
                  ? 'btn-green'
                  : 'disabled-button btn-green'
              "
              @click="
                displayPopupAsync();
                dropDownClose();
              "
              @keydown="preventEnterAndSpaceKeyPress($event)"
            >
              {{ categoriesStatus === "active" ? "Publish" : "Save" }}
            </button>
            <button type="button"
              @click="
                backToCategories();
                dropDownClose();
              "
              @keydown="preventEnterAndSpaceKeyPress($event)"
              class="btn-green-outline"
            >
              {{ $t('BUTTONS.CANCEL_BUTTON') }}
            </button>
          </div>
        </div>
        <div class="input-add-categories-section">
          <div class="input-section">
            <div class="input-sub-section">
              <div class="left-section">
                <div
                  :style="{ backgroundImage: image ? `url('${image}')` : '' }"
                  class="image-section"
                  id="categoryImage"
                >
                  <div class="image-main-div" id="recipeVideo">
                    <div class="image-inner-container">
                      <div
                        class="progress-image"
                        v-show="
                          uploadImagePercentage >= 1 &&
                          uploadImagePercentage <= 99
                        "
                      >
                        <div class="progress-image-content">
                          <div
                            v-show="
                              uploadImagePercentage >= 1 &&
                              uploadImagePercentage <= 5
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 6 &&
                              uploadImagePercentage <= 11
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 12 &&
                              uploadImagePercentage <= 17
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 18 &&
                              uploadImagePercentage <= 24
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 25 &&
                              uploadImagePercentage <= 30
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 31 &&
                              uploadImagePercentage <= 36
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 37 &&
                              uploadImagePercentage <= 41
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 42 &&
                              uploadImagePercentage <= 49
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 50 &&
                              uploadImagePercentage <= 55
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 56 &&
                              uploadImagePercentage <= 61
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 62 &&
                              uploadImagePercentage <= 67
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 68 &&
                              uploadImagePercentage <= 74
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 75 &&
                              uploadImagePercentage <= 80
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 81 &&
                              uploadImagePercentage <= 86
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 87 &&
                              uploadImagePercentage <= 92
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-show="
                              uploadImagePercentage >= 93 &&
                              uploadImagePercentage <= 98
                            "
                          >
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                            />
                          </div>
                          <div v-show="uploadImagePercentage == 99">
                            <img
                              alt=""
                              class="progress-icon"
                              src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                            />
                          </div>
                          <div class="upload-text">
                            <div
                              class="upload-heading text-light-h4"
                              v-if="
                                uploadImagePercentage >= 1 &&
                                uploadImagePercentage <= 98
                              "
                            >
                              Upload is in progress
                            </div>
                            <div class="upload-heading text-light-h4" v-else>Uploaded</div>
                            <span class="upload-media text-light-h6"
                              >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                              {{ (uploadImageSize / 1024000).toFixed(1) }}
                              MB</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                    <img
                      alt=""
                      v-if="
                        image &&
                        (uploadImagePercentage == 0 ||
                          uploadImagePercentage == 100)
                      "
                      class="display-image-section"
                      :src="`${image}`"
                    />
                    <div
                      class="replace-image-tag text-h3 font-normal"
                      v-if="
                        uploadImagePercentage == 0 ||
                        uploadImagePercentage == 100
                      "
                    >
                      <div class="hover-image">
                        <input
                          type="file"
                          class="upload-input"
                          title="Update Picture"
                          @click="uploadSameImageVideo($event)"
                          @change="checkUploadedFiles"
                          accept=".jpg,.png,.jpeg"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="text-section"
                  :class="{
                    'simple-data-tooltip': hasCategorieNameFocus,
                  }"
                  :data-tooltip-text="hasCategorieNameFocus && categoriesName"
                >
                  <img
                    alt="compulsory"
                    v-if="!categoriesName"
                    class="compulsory-field-category"
                    src="../assets/images/asterisk.svg?skipsvgo=true"
                  />
                  <input
                    @click="dropDownClose()"
                    type="text"
                    class="title text-title-1"
                    ref="categoryNameRef"
                    autocomplete="off"
                    placeholder="Name your category"
                    v-model.trim="categoriesName"
                    @mouseover="checkAddCategorieName()"
                    @mouseleave="hideAddcategoryNameTip()"
                    @keydown="hideAddcategoryNameTip()"
                    @input="hideAddcategoryNameTip()"
                  />
                </div>
                <div class="slug-category-details">
                  <span class="slug-category-text text-title-3">Slug:</span>
                  <input
                    autocomplete="off"
                    id="slug-category"
                    v-model="categoriesSlug"
                    class="slug-category-input"
                    @input="slugInput(), debounceInput()"
                    @click="dropDownClose()"
                  />
                  <div v-if="isSlugStatus" class="slug-exist-main">
                    <p class="slug-exist text-light-h4">This slug already exists</p>
                  </div>
                </div>
                <div class="image-details">
                  <span class="bold text-title-2">Category Image: </span>
                  <span class="normal text-title-2 font-normal"
                    >jpg,png format (recom. 1MB, max 15 MB)</span
                  >
                  <span class="compulsory-field-category-image">*</span>
                </div>
              </div>
              <div class="right-section">
                <div class="publish-btn">
                  <span class="text text-title-2"> {{ $t('COMMON.PUBLISH') }} </span>
                  <label class="switch">
                    <input
                      type="checkbox"
                      :checked="categoriesStatus == 'active'"
                      @click.prevent="
                        categoriesName.length > 0 &&
                        productImage &&
                        !isRecipeVariantNameEmpty &&
                        !isSlugCheckConfirm &&
                        (uploadImagePercentage == 0 ||
                          uploadImagePercentage == 100)
                          ? publishToggleBtn()
                          : publishToggleBtnPopup()
                      "
                    />
                    <span class="slider-round"></span>
                  </label>
                </div>
              </div>
            </div>
            <div
              v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
              class="category-variant-section"
            >
              <div class="category-variants-main">
                <div class="category-variants">Category Variants:</div>
                <div
                  class="add-variant-section"
                  :class="{
                    'simple-data-tooltip': recipeVariantLanguageList.length < 1,
                  }"
                  :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
                >
                  <div
                    class="add-variant-main"
                    @click="openRecipeVariantPopUp()"
                    :class="
                      recipeVariantLanguageList.length > 0
                        ? 'add-variant-main'
                        : 'disable-add-variant-main add-variant-main'
                    "
                  >
                    <div class="add-variant-btn">
                      <img alt="" src="@/assets/images/category-add.png" />
                    </div>
                    <div class="add-variant-text text-h3">Add variant</div>
                  </div>
                </div>
              </div>
              <div
                class="add-category-variant text-title-2 font-normal"
                v-if="recipeVariantList.length <= 0"
              >
                Add category variants to support multiple languages.
              </div>
              <div class="category-variant-card-main">
                <template v-for="(categoryVariant, index) in recipeVariantList">
                  <variant-card-field
                    v-if="categoryVariant?.lang !== lang"
                    v-model="categoryVariant.name"
                    :prefix-label="displayLanguageCode(categoryVariant.lang)"
                    input-placeholder="Enter name"
                    @input-change="inputContentChanged(index)"
                    @delete-action="deleteCategoryVariant(categoryVariant, index)"
                  ></variant-card-field>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div
          class="add-category-recipes-section"
          v-if="
            recipeDataForCategories.length == 0 &&
            categoryPromotedRecipes.length == 0
          "
        >
          <div class="content">
            <div class="left-section">
              <div class="head-text text-h2 font-normal">
                {{ $t('CATEGORY.ADD_RECIPE_TO_CATEGORY') }}
              </div>
              <div class="sub-text text-title-2 font-normal">
                {{ $t('CATEGORY.ADD_RECIPES_TO_NEW_CATEGORY') }}
              </div>
              <button type="button"
                class="btn-green"
                @click="addRecipe"
                @keydown="preventEnterAndSpaceKeyPress($event)"
              >
                {{ $t('COMMON.ADD_RECIPES') }}
              </button>
            </div>
            <div class="right-section">
              <div class="image-content">
                <img
                  alt=""
                  class="image"
                  src="../assets/images/pan-image.png"
                />
              </div>
            </div>
          </div>
        </div>
        <div
          class="category-recipes-table-content"
          v-if="
            recipeDataForCategories.length || categoryPromotedRecipes.length
          "
        >
          <div class="content">
            <div
              class="promoted-header text-h2 font-normal"
              v-if="categoryPromotedRecipes.length <= 1"
            >
              {{ categoryPromotedRecipes.length }} Promoted Recipe
            </div>
            <div
              class="promoted-header text-h2 font-normal"
              v-if="categoryPromotedRecipes.length > 1"
            >
              {{ categoryPromotedRecipes.length }} Promoted Recipes
            </div>
            <div class="promoted-subtitle text-title-2 font-normal">
              Promoted recipes are the first to be displayed in category.
            </div>
            <div
              class="add-zero-section"
              v-if="categoryPromotedRecipes.length == 0"
            >
              <div class="zero-promoted">
                <span class="bold text-title-2"> 0 PROMOTED RECIPES. </span>
                <span class="normal text-title-2 font-normal"> Recipes will be auto-selected. </span>
              </div>
            </div>
            <div
              class="promote-table-content"
              v-if="categoryPromotedRecipes.length > 0"
            >
              <div
                class="table-header"
                v-show="categoryPromotedRecipes.length > 0"
              >
                <div class="margin-div"></div>
                <div class="category-group-isin">
                  <span>{{ $t('COMMON.RECIPE_ISIN') }} </span>
                </div>
                <div class="category-group-title">
                  <span>{{ $t('COMMON.RECIPE_TITLE') }}</span>
                </div>
                <div class="category-group-total-time">
                  <span> {{ $t('COMMON.TOTAL_TIME') }}</span>
                </div>
                <div class="ing-count">
                  <span>{{ $t('COMMON.INGREDIENT_COUNT') }}</span>
                </div>
              </div>
              <table class="promote-table" id="promote-table">
                <caption></caption>
                <tbody>
                  <draggable
                    :list="categoryPromotedRecipes"
                    class="all-content-categories"
                    :scroll-sensitivity="200"
                    :force-fallback="true"
                    ghost-class="hidden-list"
                    @start="drag = true"
                    @end="drag = false"
                    handle=".draggable-icon"
                  >
                    <tr
                      class="body"
                      v-for="(promote, index) in categoryPromotedRecipes"
                      :key="index"
                    >
                     <th scope="col"></th>
                     <td>
                        <div class="draggable-icon">
                          <div class="instruction-drag-icon">
                            <img
                              alt=""
                              class="promoted-handle"
                              src="@/assets/images/drag-vertically.svg?skipsvgo=true"
                            />
                          </div>
                        </div>
                      </td>
                      <td class="table-image-promote">
                        <div class="image-promote" v-if="promote.image">
                          <img
                            alt=""
                            class="image"
                            :src="
                              promote.image.url
                                ? promote.image.url
                                  ? promote.image.url
                                  : defaultImage
                                : ''
                            "
                          />
                        </div>
                        <div
                          class="image-promote"
                          v-if="promote.media && promote.media[lang]"
                        >
                          <img
                            alt=""
                            v-if="promote.media[lang].image"
                            class="image"
                            :src="promote.media[lang].image"
                          />
                          <img
                            alt=""
                            v-else-if="promote.media[lang].externalImageUrl"
                            class="image"
                            :src="promote.media[lang].externalImageUrl"
                          />
                          <img alt="" v-else class="image" :src="defaultImage" />
                        </div>
                        <div
                          class="image-promote"
                          v-if="promote.externalImageUrl"
                        >
                          <img
                            alt=""
                            class="image"
                            :src="
                              promote.externalImageUrl
                                ? promote.externalImageUrl
                                : defaultImage
                            "
                          />
                        </div>
                        <div
                          class="image-promote"
                          v-if="
                            !promote.image &&
                            !promote.media &&
                            !promote.externalImageUrl
                          "
                        >
                          <img alt="" class="image" :src="defaultImage" />
                        </div>
                      </td>
                      <td class="table-promote-code">
                        <div class="promote-code">
                          {{
                            promote ? (promote.isin ? promote.isin : "") : ""
                          }}
                        </div>
                      </td>
                      <td
                        class="promote-recipe-name"
                        :class="{
                          'simple-data-tooltip': !isDraggableTooltipDisplay && isCategoryListTooltipVisible
                        }"
                        :data-tooltip-text="!isDraggableTooltipDisplay && isCategoryListTooltipVisible
                          ? (promote?.title?.[lang] || promote?.title)
                          : ''"
                      >
                        <div class="edit-category-recipe-name-tooltip">
                          <div
                            class="promote-name"
                            @mouseover="checkPromoteName(index)"
                            @mouseleave="hidePromoteNameTip(index)"
                            :id="`Promoterecipename${index}`"
                          >
                            {{
                              promote.title[lang]
                                ? promote.title[lang]
                                : promote.title
                                ? promote.title
                                : ""
                            }}
                          </div>
                          <div
                            v-if="promote.langs.length > 1"
                            class="simple-data-tooltip"
                            :data-tooltip-text="getAvailableLanguagesTooltip(categoryPromotedRecipes)"
                          >
                            <img
                              alt="globe"
                              src="@/assets/images/language-icon.png"
                            />
                          </div>
                        </div>
                        <div
                          class="promote-subtitle"
                          v-if="
                            promote &&
                            promote.subtitle &&
                            promote.subtitle[lang]
                          "
                        >
                          <span
                            v-if="
                              promote &&
                              promote.subtitle &&
                              promote.subtitle[lang] &&
                              promote.subtitle[lang].length >= 40
                            "
                          >
                            {{
                              promote.subtitle[lang].substring(0, 40) + "..."
                            }}
                          </span>
                          <span
                            v-if="
                              promote &&
                              promote.subtitle &&
                              promote.subtitle[lang] &&
                              promote.subtitle[lang].length < 40
                            "
                          >
                            {{ promote.subtitle[lang] }}
                          </span>
                        </div>
                        <div
                          class="promote-subtitle"
                          v-if="promote && promote.subtitle"
                        >
                          <span
                            v-if="
                              promote &&
                              promote.subtitle &&
                              promote.subtitle.length >= 40
                            "
                          >
                            {{ promote.subtitle.substring(0, 40) + "..." }}
                          </span>
                          <span
                            v-if="
                              promote &&
                              promote.subtitle &&
                              promote.subtitle.length < 40
                            "
                          >
                            {{ promote.subtitle }}
                          </span>
                        </div>
                      </td>
                      <td class="promote-ingredient-time">
                        <div class="promote-details">
                          <div class="details">
                            <span
                              v-if="
                                promote.time && parseDurationString(promote.time.total)
                              "
                              >{{
                                parseDurationString(
                                  promote.time.total ?? ""
                                ) ?? ""
                              }}</span
                            >
                            <span
                              v-if="
                                promote.totalTime && parseDurationString(promote.totalTime)
                              "
                              >{{
                                parseDurationString(
                                  promote.totalTime ?? ""
                                ) ?? ""
                              }}</span
                            >
                            <span
                              v-if="
                                !promote.totalTime &&
                                !(promote.time && promote.time.total)
                              "
                              >none</span
                            >
                          </div>
                        </div>
                      </td>
                      <td class="promote-ingredient-count">
                        <div class="promote-details">
                          <div class="details">
                            <span
                              v-if="
                                promote.ingredients &&
                                promote.ingredients[lang] &&
                                promote.ingredients[lang].length == 1
                              "
                              >{{
                                promote.ingredients && promote.ingredients[lang]
                                  ? promote.ingredients[lang].length
                                  : ""
                              }}
                              ingredient</span
                            >
                            <span
                              v-if="
                                promote.ingredients &&
                                promote.ingredients[lang] &&
                                promote.ingredients[lang].length > 1
                              "
                              >{{
                                promote.ingredients[lang].length
                              }}
                              ingredients</span
                            >
                            <span
                              v-if="
                                promote.ingredients &&
                                !promote.ingredients[lang] &&
                                promote.ingredients.length == 1
                              "
                              >{{ promote.ingredients.length }} ingredient</span
                            >
                            <span
                              v-if="
                                promote.ingredients &&
                                !promote.ingredients[lang] &&
                                promote.ingredients.length > 1
                              "
                              >{{
                                promote.ingredients.length
                              }}
                              ingredients</span
                            >
                          </div>
                        </div>
                      </td>
                      <td>
                        <div class="menu">
                          <div
                            :class="
                              promote.dropDown
                                ? 'menu-container menu-selected'
                                : 'menu-container'
                            "
                            @click="displayOption(promote)"
                          >
                            <img
                              alt=""
                              v-if="promote.dropDown"
                              class="table-edit-btn"
                              src="
                              @/assets/images/green-edit-btn.svg?skipsvgo=true"
                            />
                            <img
                              alt=""
                              v-if="!promote.dropDown"
                              class="table-edit-btn"
                              src="@/assets/images/edit-btn.svg?skipsvgo=true"
                            />
                          </div>
                          <div class="menu-box" v-if="promote.dropDown">
                            <ul class="menu-list text-title-2">
                              <li @click="editRecipe(promote.isin)">
                                {{ $t('BUTTONS.PREVIEW_BUTTON') }}
                              </li>
                              <li @click="unPromoteRecipe(promote, index)">
                                {{ $t('COMMON.UNPROMOTE') }}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </draggable>
                </tbody>
              </table>
            </div>
            <div class="recipe-category">
              <div class="recipe-header-section">
                <div v-if="!isSearchExitEnable">
                  <div
                    class="recipe-header text-h2 font-normal"
                    v-if="recipeDataForCategories.length <= 1"
                  >
                    {{ recipeDataForCategories.length }}
                    Recipe in Category
                  </div>
                  <div
                    class="recipe-header text-h2 font-normal"
                    v-if="recipeDataForCategories.length > 1"
                  >
                    {{ recipeDataForCategories.length }}
                    Recipes in Category
                  </div>
                </div>
                <div v-if="isSearchExitEnable">
                  <div class="recipe-header text-h2 font-normal">Search results</div>
                </div>
                <div v-if="!hasSelectionEnabled" class="search-section">
                  <div
                    v-if="
                      !hasSelectionEnabled &&
                      !isSearchExitEnable &&
                      recipeDataForCategories.length > 0
                    "
                    class="edit-category-select-button text-h3"
                  >
                    <span @click="selectProducts()">{{
                      $t('COMMON.SELECT')
                    }}</span>
                  </div>
                  <div class="search-box">
                    <input
                      type="text"
                      class="search-input-box"
                      @click="dropDownClose()"
                      placeholder="Search for recipe name"
                      v-model.trim="queryRecipe"
                      @keypress.enter="searchRecipeList()"
                      :class="{ 'align-search-input-box': queryRecipe }"
                    />
                    <img
                      alt=""
                      class="search-icon-green-image"
                      @click="
                        searchRecipeList();
                        dropDownClose();
                      "
                      src="@/assets/images/search-icon-green.png"
                    />
                    <img
                      alt=""
                      class="exit-search-icon"
                      v-if="isSearchExitEnable"
                      @click="resetQuery()"
                      src="@/assets/images/exit-gray.png"
                    />
                  </div>
                  <div
                    class="add-btn"
                    @click="
                      addRecipe();
                      dropDownClose();
                    "
                  >
                    <img
                      alt=""
                      class="add-image"
                      src="@/assets/images/category-add.png"
                    />
                    <span class="text text-h3"> {{ $t('COMMON.ADD_RECIPE') }} </span>
                  </div>
                </div>
              </div>
              <div
                v-show="hasSelectionEnabled"
                class="edit-category-selection-container"
              >
                <div class="edit-category-selection-panel">
                  <div
                    id="selectAllCheckboxId"
                    class="edit-category-select-all-checbox-section"
                  >
                    <label class="edit-category-checkbox-section">
                      <input
                        type="checkbox"
                        :checked="selectionOfRecipes[0].isSelected"
                        @click="selectAllMatches()"
                      />
                      <span class="checkmark"></span>
                    </label>
                  </div>
                  <div
                    @click="selectAllMatches()"
                    class="edit-category-select-all-text"
                  >
                    Select All
                  </div>
                  <div class="edit-category-selection">
                    <div class="edit-category-selected-text">
                      {{ checkSelectedRecipes }} selected<span
                        v-if="selectedProducts.length > 0"
                        class="edit-category-selected-cross-icon"
                      >
                        <img
                          src="@/assets/images/close.svg?skipsvgo=true"
                          @click="removeAllSelected()"
                          alt="edit-category-close-icon"
                        />
                      </span>
                    </div>
                  </div>
                  <div class="edit-category-btn-container">
                    <button type="button"
                      :class="
                        selectedProducts.length == 0
                          ? 'disabled-button btn-red'
                          : 'btn-red'
                      "
                      @click="deleteSelect()"
                    >
                      {{ $t('BUTTONS.REMOVE_BUTTON') }}
                    </button>
                  </div>
                  <button type="button"
                    class="edit-category-cancel-btn text-h3"
                    @click="cancelSelect()"
                  >
                    {{ $t('BUTTONS.CANCEL_BUTTON') }}
                  </button>
                </div>
              </div>
              <div class="recipe-table-content">
                <div
                  class="add-zero-section category-recipe-section"
                  v-if="
                    recipeDataForCategories.length == 0 &&
                    !isSearchExitEnable
                  "
                >
                  <div class="zero-promoted">
                    <span class="bold text-title-2"> 0 RECIPE IN CATEGORY. </span>
                    <span class="normal text-title-2 font-normal"> Add recipes in category. </span>
                  </div>
                </div>
                <div
                  class="no-result-for-category text-title-2"
                  v-if="searchRecipesCount == 0 && isSearchExitEnable"
                >
                  {{ $t('COMMON.NO_RESULT_FOUND') }}
                </div>
                <table
                  class="recipe-table"
                  id="recipe-table"
                  v-if="recipeDataForCategories"
                >
                <caption></caption>
                  <thead
                    v-if="
                      (recipeDataForCategories.length &&
                        !isSearchExitEnable) ||
                      searchRecipesCount != 0
                    "
                    class="table-head text-h3"
                    v-show="recipeDataForCategories.length > 0"
                  >
                    <tr class="title">
                      <th></th>
                      <th></th>
                      <th></th>
                      <th class="category-group-isin">
                        <span>{{ $t('COMMON.RECIPE_ISIN') }} </span>
                      </th>
                      <th class="category-group-title">
                        <span>{{ $t('COMMON.RECIPE_TITLE') }}</span>
                      </th>
                      <th class="category-group-total-time">
                        <span> {{ $t('COMMON.TOTAL_TIME') }}</span>
                      </th>
                      <th class="ing-count">
                        <span>{{ $t('COMMON.INGREDIENT_COUNT') }}</span>
                      </th>
                      <th class="ing-promote"></th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody
                    :style="{
                      cursor: hasSelectionEnabled ? 'pointer' : 'default',
                    }"
                  >
                    <tr
                      class="body"
                      @click="selectMatchToDelete(index, recipe)"
                      :id="
                        recipe.isdeleteSelected ? 'delete-selected' : ''
                      "
                      v-for="(recipe, index) in recipeDataForCategories"
                      :key="index"
                      v-show="!recipe.isSearched"
                      :class="{
                        'recipe-selected-color-category':
                          hasSelectionEnabled && recipe.isSelectedToDelete,
                      }"
                    >
                      <td>
                        <div
                          v-if="hasSelectionEnabled"
                          :class="
                            hasSelectionEnabled
                              ? 'edit-category-product-table-srno-checkbox'
                              : 'edit-category-product-table-srno'
                          "
                        >
                          <div
                            id="selectAllCheckboxId"
                            class="edit-category-select-all-checbox-section"
                          >
                            <label class="edit-category-checkbox-section">
                              <input
                                @click="selectMatchToDelete(index, recipe)"
                                :checked="recipe.isSelectedToDelete"
                                type="checkbox"
                              />
                              <span class="checkmark"></span>
                            </label>
                          </div>
                        </div>
                      </td>
                      <td></td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                        class="table-image-recipe"
                      >
                        <div class="image-recipe" v-if="recipe.media">
                          <img
                            alt=""
                            v-if="
                              recipe.media &&
                              recipe.media[lang] &&
                              recipe.media[lang].image &&
                              !recipe.media[lang].externalImageUrl
                            "
                            class="image"
                            :src="
                              recipe.media &&
                              recipe.media[lang] &&
                              recipe.media[lang].image
                                ? recipe.media[lang].image
                                : ''
                            "
                          />
                          <img
                            alt=""
                            v-if="
                              recipe.media &&
                              recipe.media[lang] &&
                              recipe.media[lang].externalImageUrl
                            "
                            class="image"
                            :src="
                              recipe.media &&
                              recipe.media[lang] &&
                              recipe.media[lang].externalImageUrl
                                ? recipe.media[lang].externalImageUrl
                                : ''
                            "
                            @error="$event.target.src = `${defaultImage}`"
                          />
                          <img
                            alt=""
                            v-if="
                              (!recipe.media ||
                                !recipe.media[lang] ||
                                !recipe.media[lang].image) &&
                              (!recipe.media ||
                                !recipe.media[lang] ||
                                !recipe.media[lang].externalImageUrl)
                            "
                            class="image"
                            :src="defaultImage"
                          />
                        </div>
                        <div class="image-recipe" v-if="recipe.image">
                          <img
                            alt=""
                            class="image"
                            :src="
                              recipe.image.url
                                ? recipe.image.url
                                  ? recipe.image.url
                                  : defaultImage
                                : ''
                            "
                          />
                        </div>
                        <div
                          class="image-recipe"
                          v-if="!recipe.image && !recipe.media"
                        >
                          <img alt="" class="image" :src="defaultImage" />
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                        class="table-recipe-code"
                      >
                        <div class="recipe-code text-light-h4">
                          {{ recipe.isin ? recipe.isin : "" }}
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                        class="table-recipe-name"
                      >
                        <div
                          class="table-recipe-section"
                          :class="{
                            'simple-data-tooltip': isRecipTagInFocus,
                          }"
                          :data-tooltip-text="isRecipTagInFocus && (recipe?.title?.[lang] || recipe?.title)"
                        >
                          <div
                            class="recipe-name text-h3"
                            :id="`simplerecipename${index}`"
                            @mouseover="showRecipNameTip(index)"
                            @mouseleave="hideRecipenameTip(index)"
                          >
                            {{ recipe.title?.[lang] || recipe.title || "" }}
                          </div>
                          <div
                            v-if="(recipe.langs?.length > 1) || (recipe.locales?.length > 1)"
                            class="tooltip-main-container-edit-category simple-data-tooltip text-light-h4"
                            :data-tooltip-text="getAvailableLanguagesTooltip(recipeDataForCategories)"
                          >
                            <img alt="globe" src="@/assets/images/language-icon.png" />
                          </div>
                        </div>
                        <div
                          class="recipe-subtitle"
                          v-if="
                            recipe && recipe.subtitle && recipe.subtitle[lang]
                          "
                        >
                          <span
                            v-if="
                              recipe &&
                              recipe.subtitle &&
                              recipe.subtitle[lang].length >= 40
                            "
                          >
                            {{
                              recipe && recipe.subtitle && recipe.subtitle[lang]
                                ? recipe.subtitle[lang].substring(0, 40) + "..."
                                : " "
                            }}
                          </span>
                          <span v-else>
                            {{
                              recipe && recipe.subtitle && recipe.subtitle[lang]
                                ? recipe.subtitle[lang]
                                : " "
                            }}
                          </span>
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                        class="table-recipe-time"
                      >
                        <div class="recipe-details">
                          <div class="details">
                            <span
                              v-if="recipe.time && parseDurationString(recipe.time.total)"
                              >{{
                                parseDurationString(
                                  recipe.time.total ? recipe.time.total : ""
                                )
                                  ? parseDurationString(
                                      recipe.time.total ? recipe.time.total : ""
                                    )
                                  : ""
                              }}</span
                            >
                            <span
                              v-if="
                                recipe.totalTime && parseDurationString(recipe.totalTime)
                              "
                              >{{
                                parseDurationString(
                                  recipe.totalTime ? recipe.totalTime : ""
                                )
                                  ? parseDurationString(
                                      recipe.totalTime ? recipe.totalTime : ""
                                    )
                                  : ""
                              }}</span
                            >
                            <span
                              v-if="
                                !recipe.totalTime &&
                                !(recipe.time && recipe.time.total)
                              "
                              >none</span
                            >
                          </div>
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                      >
                        <div class="recipe-details">
                          <div class="details">
                            <span
                              v-if="
                                recipe.ingredients &&
                                recipe.ingredients[lang] &&
                                recipe.ingredients[lang].length == 1
                              "
                              >{{
                                recipe.ingredients && recipe.ingredients[lang]
                                  ? recipe.ingredients[lang].length
                                  : ""
                              }}
                              ingredient</span
                            >
                            <span
                              v-if="
                                recipe.ingredients &&
                                recipe.ingredients[lang] &&
                                recipe.ingredients[lang].length > 1
                              "
                              >{{
                                recipe.ingredients && recipe.ingredients[lang]
                                  ? recipe.ingredients[lang].length
                                  : ""
                              }}
                              ingredients</span
                            >
                            <span v-if="recipe.numIngredients == 1"
                              >{{
                                (
                                  recipe.numIngredients
                                    ? recipe.numIngredients
                                    : ""
                                )
                                  ? recipe.numIngredients
                                    ? recipe.numIngredients
                                    : ""
                                  : ""
                              }}
                              ingredient</span
                            >
                            <span v-if="recipe.numIngredients > 1"
                              >{{
                                (
                                  recipe.numIngredients
                                    ? recipe.numIngredients
                                    : ""
                                )
                                  ? recipe.numIngredients
                                    ? recipe.numIngredients
                                    : ""
                                  : ""
                              }}
                              ingredients</span
                            >
                          </div>
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                        class="table-recipe-promote-btn"
                      >
                        <div
                          class="recipe-btn"
                          v-if="!hasSelectionEnabled"
                          :class="{
                            'simple-data-tooltip': recipe?.status !== 'active',
                          }"
                          :data-tooltip-text="recipe?.status !== 'active' && $t('COMMON.CANNOT_PROMOTE_UNPUBLISHED_RECIPES')"
                        >
                          <button type="button"
                            :class="
                              recipe &&
                              recipe.status &&
                              recipe.status == 'active'
                                ? 'btn-green-outline'
                                : 'disabled-button btn-green-outline'
                            "
                            @click="promoteRecipe(recipe, index)"
                            @keydown="preventEnterAndSpaceKeyPress($event)"
                          >
                            {{ $t('COMMON.PROMOTE') }}
                          </button>
                        </div>
                      </td>
                      <td
                        :class="recipe.isPromoted ? 'disable-table-data' : ''"
                      >
                        <div class="menu">
                          <div
                            v-if="!hasSelectionEnabled"
                            :class="
                              recipe.dropDown
                                ? 'menu-container menu-selected'
                                : 'menu-container'
                            "
                            @click="displayOption(recipe)"
                          >
                            <img
                              alt=""
                              v-if="recipe.dropDown"
                              class="table-edit-btn"
                              src="
                              @/assets/images/green-edit-btn.svg?skipsvgo=true"
                            />
                            <img
                              alt=""
                              v-if="!recipe.dropDown"
                              class="table-edit-btn"
                              src="@/assets/images/edit-btn.svg?skipsvgo=true"
                            />
                          </div>
                          <div
                            v-if="recipe.dropDown && !hasSelectionEnabled"
                            class="menu-box"
                          >
                            <ul class="menu-list text-title-2">
                              <li @click="editRecipe(recipe.isin)">
                                {{ $t('BUTTONS.PREVIEW_BUTTON') }}
                              </li>
                              <li @click="deleteRecipeListPopUp(recipe, index)">
                                {{ $t('BUTTONS.REMOVE_BUTTON') }}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div
                  class="load-button"
                  v-if="fromRecipe + sizeRecipe < recipeForCategoriesTotal"
                >
                  <button type="button"
                    class="btn-green"
                    @click="
                      loadCategoriesRecipes();
                      dropDownClose();
                    "
                    @keydown="preventEnterAndSpaceKeyPress($event)"
                  >
                    {{ $t('COMMON.LOAD_MORE') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <deleteModal
          v-if="isRemoveCategoryVariantVisible"
          :closeModal="closeModal"
          :productInfoTitle="'Remove Category Variant?'"
          :productDescriptionOne="'Are you sure you want to remove this variant from the'"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="removeCategoryVariant"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <unableToContentModal
          v-if="isUnableToPublishArticle"
          :closeModal="closeModal"
          :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
        />
        <deleteModal
          v-if="isDeleteRecipeModal"
          :closeModal="closeModal"
          :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="deleteRecipeBtn"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <addRecipeModal
          v-if="isAddRecipeModal"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @closeModal="closeModal"
          :recipeDataForCategories="recipeDataForCategories"
          :categoryPromotedRecipes="categoryPromotedRecipes"
          @campaignModifiedAddRecipe="campaignModifiedAddRecipe"
          :selectedCategoryRecipe="selectedCategoryRecipe"
          :isAddCategory="isAddCategory"
        />
        <saveModal
          v-if="isPublishModalVisible"
          :closeModal="closeModal"
          :saveAndPublishFunction="publishConfirm"
          :availableLang="[]"
          :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
          :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"

        />
        <unpublishModal
          v-if="isUnPublishModalVisible"
          :description="'Do you want to unpublish this category?'"
          :noteMessage="''"
          :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
          :unpublishFunction="unPublishConfirm"
          :closeModal="closeModal"
        />
        <saveModal
          v-if="isSaveModalVisible && categoriesStatus != 'active'"
          :closeModal="closeModal"
          :saveAndPublishFunction="saveButtonClickAsync"
          :availableLang="[]"
          :isSlugCheckConfirm="isSlugCheckConfirm"
          :hasSlugExist="hasSlugExist"
          :buttonName="$t('BUTTONS.SAVE_BUTTON')"
          :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        />
        <saveModal
          v-if="isSaveModalVisible && categoriesStatus == 'active'"
          :closeModal="closeModal"
          :saveAndPublishFunction="saveButtonClickAsync"
          :availableLang="[]"
          :isSlugCheckConfirm="isSlugCheckConfirm"
          :hasSlugExist="hasSlugExist"
          :buttonName="'Publish'"
          :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"

        />
        <cancelModal
          v-if="isConfirmModalVisible"
          :availableLang="[]"
          :isCampaignModifiedFromShoppableReview="false"
          :callConfirm="backToCategory"
          :closeModal="closeModal"
        />
        <Modal v-if="openPreviewRecipe" @close="closeModal">
          <template #noProductMatches>
            <div class="open-preview-recipe-modal">
              <div class="recipe-main-preview-header">
                <div class="recipe-preview-header text-h2">
                  {{ $t('COMMON.RECIPE_PREVIEW') }}
                </div>
              </div>
              <img
                alt=""
                class="close-preview-recipe-modal"
                @click="closeModal"
                src="../assets/images/exit-gray.png"
              />
              <div class="open-preview-recipe-main">
                <recipePreviewDetail
                  :rISIN="rISIN"
                  :checkRecipePreviewVideo="checkRecipePreviewVideo"
                ></recipePreviewDetail>
              </div>
            </div>
          </template>
        </Modal>
        <selectTheLanguageModal
          v-if="hasRecipeVariantLanguagePopup"
          :closeModal="closeModal"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
          @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
          @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
          :recipeVariantLanguageList="recipeVariantLanguageList"
          :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
        />
        <addVariant
          v-if="isAddVariantCategoryNamePopup"
          :closeModal="closeModal"
          :typeName="'Category'"
          :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
          :itemName="categoriesName"
          @addConfirmVariant="addRecipeVariant"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @backToRoute="backToSelectLanguageVariantPopUp"
        />
        <savingModal
          v-show="isCategorySaving"
          :status="categoriesStatus == 'active' ? 'publishing' : 'saving'"
        />
        <invalidImageVideoPopup
          v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
          :closeModal="closeModal"
          :acceptedFile="' jpg,png'"
          :video="false"
          :image="true"
          :zip="false"
        />
        <sizeLimit
          v-if="isUploadingImagePopup"
          :continueImage="continueImage"
          :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
          :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
          :closeModal="closeModal"
          :isUploadingImagePopup="isUploadingImagePopup"
        />
        <sizeLimit
          v-if="isMaxImagePopupVisible"
          :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
          :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
          :closeModal="closeModal"
          :isMaxImagePopupVisible="isMaxImagePopupVisible"
        />
        <deleteModal
          v-if="isSelectDeleteModalVisible"
          :closeModal="closeModal"
          :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPES')"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPES_POPUP')"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="deleteSelectProductMatches"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
      </div>
    </content-wrapper>
  </client-only>
</template>

<script setup>

import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import unpublishModal from "@/components/unpublish-modal";
import savingModal from "@/components/saving-modal";
import sizeLimit from "@/components/size-limit.vue";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import cancelModal from "@/components/cancel-modal";
import saveModal from "@/components/save-modal";
import Modal from "@/components/Modal";
import deleteModal from "@/components/delete-modal";
import unableToContentModal from "@/components/unable-to-content-modal";
import addVariant from "@/components/add-variant";
import selectTheLanguageModal from "@/components/select-the-language";
import RecipeService from "@/services/RecipeService";
import recipePreviewDetail from "@/components/recipe-preview-detail";
import addRecipeModal from "@/components/add-recipe-modal.vue";
import { debounce } from "lodash";
import axios from "axios";
import defaultImage from "~/assets/images/default_recipe_image.png";
import { computed, watchEffect, onMounted, onBeforeUnmount , ref, reactive } from 'vue';
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useTimeUtils } from "@/composables/useTimeUtils";
import { useEventUtils } from "@/composables/useEventUtils";
import { VueDraggableNext as draggable  } from 'vue-draggable-next';



const store = useStore();
const {parseDurationString} = useTimeUtils();
const { getRef } = useRefUtils();
const { preventEnterAndSpaceKeyPress, onEscapeKeyPress } = useEventUtils()
const router = useRouter();
const { t } = useI18n();
const { $auth, $eventBus } = useNuxtApp();
const { triggerLoading , formatAndTrimSlug , generateIncrementedSlug , formatSlug , extractNumericCount } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { readyProject, isAdmin, getAvailableLangs } = useProjectLang();
const isCheckVideoPreview = ref(false);
const hasSlugExist = ref(false);
const isSlugInputWarn = ref(false);
const isSlugStatus = ref(false);
const hasCategorieNameFocus = ref(false);
const isMaxImagePopupVisible = ref(false);
const isSlugCheckConfirm = ref(false);
const isUnableToPublishArticle = ref(false);
const uploadImageConfirm = ref("");
const isUploadingImagePopup = ref(false);
const isInvalidImageModalVisible = ref(false);
const isRemoveCategoryVariantVisible = ref(false);
const isCategorySaving = ref(false);
const imageResponseUrl= ref("");
const isAddRecipeModal = ref(false);
const categoriesName = ref("");
const categoriesImg = ref("");
const categoriesSlug = ref("");
const editCategoriesISIN = ref("");
const openPreviewRecipe = ref(false);
const rISIN = ref("");
const fromRecipe = ref(0);
const sizeRecipe = ref(10);
const recipeForCategoriesTotal = ref(0);
const queryRecipe = ref("");
const newIsin = ref("");
const productImage = ref("");
const categoriesStatus = ref("hidden");
const searchRecipesCount = ref(0);
const queryText = ref("");
const operationStatusDetails = ref("");
const isDeleteRecipeModal = ref(false);
const isSaveModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const deleteRecipeISIN = ref("");
const isSearchExitEnable = ref(false);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const hasRecipeVariantLanguagePopup = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const recipeVariantSelectedLanguage = ref("");
const isAddVariantCategoryNamePopup = ref(false);
const variantName = ref("");
const categoryVariantDataIndex = ref("");
const deletedvariant = ref("");
const hasDisabledSelectLanguageButton = ref(false);
const lang = ref("");
const isRecipeVariantNameEmpty = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const selectedProducts = ref([])
const uploadImageSize = ref(0);
const image = ref("");
const isDraggableTooltipDisplay = ref(false);
const isAddCategory = ref(true);
const hasSelectionEnabled = ref(false);
const isSelectDeleteModalVisible = ref(false);
const isAdminCheck = ref(false);
const promoteList = ref([]);
const promotedRecipeMatches = ref([]);
const recipeDataForCategories = ref([]);
const categoryPromotedRecipes = ref([]);
const filteredRecipeIsins = ref([]);
const selectedCategoryRecipe = ref([]);
const promotedRecipesIsins = ref([]);
const dropdownItem = ref([]);
const recipeVariantLanguageList = ref([]);
const recipeVariantList = ref([]);
const selectedDefaultLang = ref([]);
const finalSelectedLanguage = ref([]);
const finalAvailableLangs = ref([]);
const file = ref("");
const indexOfRecipe = ref("");
const isCategoryListTooltipVisible = ref(false);
const isRecipTagInFocus = ref(false);
const selectionOfRecipes = ref([
  {
    isSelected: false,
  },
]);
const filesName = ref("");
const deletedVariant = ref();

const cancelImage = reactive({});
const categoryNameRef = ref(null);
const showLoader = ref(false);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      await initializeDataAsync();
      addEventListeners();
      registerRootListeners();
    }
  });
});

const checkSelectedRecipes = computed(() => {
  return selectedProducts.value.filter(data => data.isSelectedToDelete).length;
});

const initializeDataAsync = async () => {
  isAdminCheck.value = isAdmin;
  lang.value = store.getters["userData/getDefaultLang"];
  finalAvailableLangs.value = await getAvailableLangs();
  selectedDefaultLang.value.push(lang.value);
  await handleAvailableLanguagesAsync();
};

const addEventListeners = () => {
  document.addEventListener("input", handleTypeInput);
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("click", handleClickOutsidePopup);
  document.addEventListener("scroll", checkScrollPosition);
};

const handleAvailableLanguagesAsync = async () => {
  if (finalAvailableLangs.value?.length) {
    finalAvailableLangs.value.forEach((lang) => {
      let langData = {};
      if (lang === "es-US") {
        langData = {
          language: lang,
          language_name: "Spanish",
          languageFlag: "/images/flags/spain-flag.png",
        };
      } else if (lang === "fr-FR") {
        langData = {
          language: lang,
          language_name: "French",
          languageFlag: "/images/flags/france-flag.png",
        };
      }
      if (!selectedDefaultLang.value.includes(lang)) {
        recipeVariantLanguageList.value.push(langData);
      }
    });
  }
};

const registerRootListeners = () => {
  $eventBus.on("backButtonConfirm", () => {
    backToCategoriesConfirm();
  });
  $eventBus.on("closeModal", () => {
    closeModal();
  });
};
const removeAllSelected = () => {
  recipeDataForCategories.value.forEach((item) => {
    item.isSelectedToDelete = false;
  });
  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];
};

const selectAllMatches = () => {
  selectionOfRecipes.value[0].isSelected =
    !selectionOfRecipes.value[0].isSelected;

  if (selectionOfRecipes.value[0].isSelected) {
    recipeDataForCategories.value.forEach((item) => {
      item.isSelectedToDelete = true;
      selectedProducts.value.push(item);
    });
  } else {
    recipeDataForCategories.value.forEach((item) => {
      item.isSelectedToDelete = false;
      selectedProducts.value = selectedProducts.value.filter((insideItem) => {
        if (item.isin === insideItem.isin) {
          return false;
        }
        return true;
      });
    });
  }

  selectedProducts.value = selectedProducts.value.filter(
    (value, index, self) =>
      index === self.findIndex((t) => t.isin === value.isin)
  );
};

const selectMatchToDelete = (data, product) => {
  if (hasSelectionEnabled.value) {
    recipeDataForCategories.value.forEach((item, index) => {
      if (index === data) {
        item.isSelectedToDelete = !item.isSelectedToDelete;
        if (item.isSelectedToDelete) {
          selectedProducts.value.push(item);
        } else {
          selectedProducts.value = selectedProducts.value.filter(
            (insideData) => insideData.isin !== product.isin
          );
        }
      }
    });
    checkSelected();
  }
};

const deleteSelectProductMatches = () => {
  const excludeIsin = selectedProducts.value
    .filter((data) => data.isSelectedToDelete)
    .map((data) => data.isin);
  recipeDataForCategories.value = recipeDataForCategories.value.filter(
    (data) => !excludeIsin.includes(data.isin)
  );
  selectedCategoryRecipe.value = selectedCategoryRecipe.value.filter(
    (data) => !excludeIsin.includes(data)
  );

  isCampaignModified.value = true;
  closeModal();
  triggerLoading($keys.KEY_NAMES.DELETED);
  selectedProducts.value = [];
  selectionOfRecipes.value[0].isSelected = false;
  hasSelectionEnabled.value = false;
};

const checkSelected = () => {
  const count = recipeDataForCategories.value.filter(
    (item) => item.isSelectedToDelete
  ).length;

  selectionOfRecipes.value[0].isSelected = count === recipeDataForCategories.value.length;
};

const cancelSelect = () => {
  hasSelectionEnabled.value = false;
  selectedProducts.value = [];

  if (recipeDataForCategories.value.length > 0) {
    selectionOfRecipes.value[0].isSelected = false;
    recipeDataForCategories.value.forEach((item) => {
      item.isSelectedToDelete = false;
    });
  }

  resetSelectbarPosition();
};

const selectProducts = () => {
  hasSelectionEnabled.value = true;
};
const checkScrollPosition = () => {
  if (hasSelectionEnabled.value) {
    const ele = document.querySelector("#recipe-table");
    if (
      ele.getBoundingClientRect().top < 0 &&
      hasSelectionEnabled.value &&
      recipeDataForCategories.value.length >= 4
    ) {
      changeSelectbarPosition();
    } else {
      resetSelectbarPosition();
    }
  }
};

const resetSelectbarPosition = () => {
  const ele = document.querySelector(".edit-category-selection-container");
  const deletebtn = document.querySelector(".edit-category-btn-container");
  const cancelbtn = document.querySelector(".edit-category-cancel-btn");
  const selectText = document.querySelector(".edit-category-selected-text");
  const selectAll = document.querySelector(".edit-category-select-all-text");
  const selectBox = getRef("selectAllCheckboxId");
  const selectionPanel = document.querySelector(".edit-category-selection-panel");

  ele.style.position = "relative";
  ele.style.paddingTop = "0px";
  ele.style.top = "0px";
  ele.style.width = "auto";
  ele.style.boxShadow = "";
  ele.style.marginLeft = "0px";
  ele.style.height = "auto";
  deletebtn.style.right = "247px";
  cancelbtn.style.right = "20px";
  selectText.style.left = "136px";
  selectAll.style.left = "49px";
  selectBox.style.marginLeft = "9px";
  selectionPanel.style.marginTop = "10px";
};

const changeSelectbarPosition = () => {
  const ele = document.querySelector(".edit-category-selection-container");
  const deletebtn = document.querySelector(".edit-category-btn-container");
  const cancelbtn = document.querySelector(".edit-category-cancel-btn");
  const selectText = document.querySelector(".edit-category-selected-text");
  const selectAll = document.querySelector(".edit-category-select-all-text");
  const selectBox = getRef("selectAllCheckboxId");
  const selectionPanel = document.querySelector(".edit-category-selection-panel");

  if (hasSelectionEnabled.value) {
    ele.style.backgroundColor = "#FFFFFF";
    ele.style.height = "64px";
    ele.style.alignItems = "center";
    ele.style.paddingTop = "inherit";
    ele.style.position = "fixed";
    ele.style.zIndex = "999";
    ele.style.top = "60px";
    ele.style.width = "-webkit-fill-available";
    ele.style.marginLeft = "-20px";
    ele.style.boxShadow = "1px 1px 4px 0px #888888";
    deletebtn.style.right = "300px";
    cancelbtn.style.right = "95px";
    selectText.style.left = "161px";
    selectAll.style.left = "74px";
    selectBox.style.marginLeft = "29px";
    selectionPanel.style.marginTop = "15px";
  }
};

const deleteSelect = () => {
  if (selectedProducts.value.length > 0) {
    isSelectDeleteModalVisible.value = true;
  }
};

const campaignModifiedAddRecipe = () => {
  isCampaignModified.value = true;
};

const backToCategory = () => {
  backToCategoriesConfirm();
  closeModal();
};
const checkRecipePreviewVideo = (data) => {
  isCheckVideoPreview.value = !!data;
};

const uploadSameImageVideo = (event) => {
  event.target.value = "";
};

const checkUploadedFiles = (event) => {
  isCampaignModified.value = true;
  const evnt = event;
  file.value = evnt.target.files || evnt.srcElement.files;
  const fileType = file.value[0].type.split("/")[0];

  if (fileType === "image") {
    uploadFiles();
  } else {
    isInvalidImageModalVisible.value = true;
  }
};

const uploadImageFile = (url, file) => {
  cancelImage.value = axios.CancelToken.source();

  axios
    .put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelImage.value.token,
      onUploadProgress: (progressEvent) => {
        uploadImagePercentage.value = Math.round(
          (progressEvent.loaded / progressEvent.total) * 100
        );
        uploadedImageFunctionAsync(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      },
    })
    .then(() => {
      // Handle success if needed
    })
    .catch((e) => {
      if (axios.isCancel(e)) {
        console.error("Image request canceled.");
      } else {
        console.error(e);
      }
    });
};

const uploadedImageFunctionAsync = async (data) => {
  if (data === 100) {
    uploadImagePercentage.value = 99;
    await delay(2000);
    uploadImagePercentage.value = 100;
  }
};



const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const processSlugAsync = async () => {
  const slugLength = 90;
  categoriesSlug.value = formatAndTrimSlug(categoriesSlug.value, slugLength);
  await checkSlugAsync(categoriesSlug.value);
};

const callSlugChangeAsync = async () => {
  isSlugInputWarn.value = false;
  isSlugCheckConfirm.value = true;
  if (categoriesSlug.value.trim() !== "") {
    await processSlugAsync();
    isSlugCheckConfirm.value = false;
  } else {
    resetSlugStatus();
  }
};

const debounceInput = debounce(() => {
  callSlugChangeAsync();
}, 2000);

const checkSlugAsync = async (slug) => {
  isSlugCheckConfirm.value = true;
  showLoader.value = true;
  await store.dispatch("categories/checkCategorySlugExistAsync", { slug, lang: lang.value });
  const response = store.getters["categories/getCategorySlug"];

  if (response !== $keys.KEY_NAMES.SLUG_ALREADY_EXIST) {
    isSlugCheckConfirm.value = false;
    const isSlugDuplicate = response.isin !== editCategoriesISIN;
    isSlugStatus.value = isSlugDuplicate;
    hasSlugExist.value = isSlugDuplicate;

    if (categoriesSlug.value !== "" && isSlugInputWarn.value) {
      isSlugInputWarn.value = false;

      let incrementSlug = categoriesSlug.value;
      const existedSlugCount = extractNumericCount(incrementSlug);
      const slugBase = formatSlug(incrementSlug);
      categoriesSlug.value = generateIncrementedSlug(slugBase, existedSlugCount);

      await checkSlugAsync(categoriesSlug.value);
      isSlugCheckConfirm.value = false;
    }
  } else {
    resetSlugStatus();
  }
};

const checkPromoteName = (index) => {
  const element = getRef(`Promoterecipename${index}`);
  if (element.scrollHeight > element.clientHeight) {
    isCategoryListTooltipVisible.value = true;
  }
};

// Reset slug status method
const resetSlugStatus = () => {
  isSlugCheckConfirm.value = false;
  isSlugStatus.value = false;
  hasSlugExist.value = false;
};
const hidePromoteNameTip = (index) => {
  const element = getRef(`Promoterecipename${index}`);
  if (element.scrollHeight > element.clientHeight) {
    isCategoryListTooltipVisible.value = false;
  }
};

// Method to show the recipe name tip
const showRecipNameTip = (index) => {
  const element = getRef(`simplerecipename${index}`);
  if (element.scrollHeight > element.clientHeight) {
    isRecipTagInFocus.value = true;
  }
};

const hideRecipenameTip = (index) => {
  const element = getRef(`simplerecipename${index}`);
  if (element.scrollHeight > element.clientHeight) {
    isRecipTagInFocus.value = false;
  }
};

const checkAddCategorieName = () => {
  const name = categoryNameRef.value;
  if (name.scrollWidth > name.clientWidth && name !== document.activeElement) {
    hasCategorieNameFocus.value = true;
  }
};

const hideAddcategoryNameTip = () => {
  hasCategorieNameFocus.value = false;
};

const checkVariantNameExists = () => {
  if (recipeVariantList.value.length === 0) {
    isRecipeVariantNameEmpty.value = false;
    return false;
  }
  isRecipeVariantNameEmpty.value = recipeVariantList.value.some(data => data.name.trim().length === 0);
  return !isRecipeVariantNameEmpty.value;
};

const displayTooltipLanguage = (item, index, langlength) => {
  const arr = item.split("-");
  if (item !== lang.value) {
    if (index < langlength) return `${arr[0].toUpperCase()},`;
    else return `${arr[0].toUpperCase()}.`;
  }
};

const getAvailableLanguagesTooltip = (list) => {
  if (!list?.length) return "";

  const uniqueLanguages = [
    ...new Set(
      list.flatMap(recipe =>
        recipe.langs.map((item) => displayTooltipLanguage(item))
      )
    ),
  ];

  return `Available in ${uniqueLanguages.join(" ")}`;
};
const dropDownClose = () => {
  if (recipeDataForCategories.value.length > 0) {
    recipeDataForCategories.value.forEach((item) => {
      item.dropDown = false;
    });
  }
  if (categoryPromotedRecipes.value.length > 0) {
    categoryPromotedRecipes.value.forEach((item) => {
      item.dropDown = false;
    });
  }
};
const deleteRecipeListPopUp = (deleteRecipe, index) => {
  indexOfRecipe.value = index;
  isDeleteRecipeModal.value = true;
  deleteRecipeISIN.value = deleteRecipe.isin;

  recipeDataForCategories.value.forEach((data) => {
    data.dropDown = false;
  });
};

const deleteRecipeBtn = () => {
  recipeDataForCategories.value.splice(indexOfRecipe.value, 1);
  const removeRecipeIndex = selectedCategoryRecipe.value.indexOf(deleteRecipeISIN.value);

  if (removeRecipeIndex !== -1) {
    selectedCategoryRecipe.value.splice(removeRecipeIndex, 1);
  }

  closeModal();
  deleteRecipeISIN.value = "";
  searchRecipesCount.value -= 1;
  triggerLoading($keys.KEY_NAMES.DELETED);

  if (recipeDataForCategories.value.length) {
    indexOfRecipe.value = Math.min(
      indexOfRecipe.value,
      recipeDataForCategories.value.length - 1
    );
  }
};

const publishToggleBtn = () => {
  dropDownClose();

  if (categoriesStatus.value === "active") {
    isUnPublishModalVisible.value = true;
  } else if (categoriesStatus.value === "hidden") {
    isPublishModalVisible.value = true;
  }

  isCampaignModified.value = true;
};

const publishToggleBtnPopup = () => {
  if (categoriesStatus.value !== "active") {
    isUnableToPublishArticle.value = true;
  } else {
    publishToggleBtn();
  }
};

const patchPublishCategoryAsync = async () => {
  if (newIsin.value && categoriesStatus.value) {
    const payload = {
      status: categoriesStatus.value,
    };

    try {
      await store.dispatch("categories/patchCategoryAsync", { payload, isin: newIsin.value });
      isSaveModalVisible.value = false;
      isCategorySaving.value = false;
      router.push("/categories");
      const loadingStatus = categoriesStatus.value !== "active" ? "savedSuccess" : "isPublishedData";
      triggerLoading(loadingStatus);
    } catch (error) {
      showLoader.value = false;
      isCategorySaving.value = false;
    }
  }
};
const promotedRecipesData = (response) => {
  recipeStatus.value = response.preview;
  categoryPromotedRecipes.value = response.promotedRecipes || [];
  filteredRecipeIsins.value = response.filteredRecipesIsins || [];
};

const uploadImageAsync = async () => {
  if (file.value) {

    const reader = new FileReader();
    reader.onload = async () => {
      const extension = file.value[0].type.split("/")[1];
      const params = {
        entity: "recipeCategory",
        content: "image",
        lang: "en-US",
        extension: extension,
        public: true,
      };
      if (!newIsin.value) {
        await getCategoryISINAsync();
      }
      if (newIsin.value) {
        await store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", {
          isin: newIsin.value,
          params,
        });
        const response = store.getters['preSignedUrl/getPreSignedUrl'];

        const uploadedImage = response?.data?.url ?? "";
        await uploadImageFile(uploadedImage, file.value[0]);
        await RecipeService.upload(uploadedImage, file.value[0]);
        imageResponseUrl.value = uploadedImage;
        productImage.value = uploadedImage;
      }
    };
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  }
};

const uploadSameImage = (event) => {
  event.target.value = "";
};

const continueImage = async () => {
  file.value = uploadImageConfirm.value;
  const reader = new FileReader();
  reader.onload = async () => {
    image.value = reader.result;
    if (image.value) {
      loadedImageSize.value = 0;
      uploadImagePercentage.value = 1;
      await uploadImageAsync();
    }
  };
  if (file.value[0]) {
    reader.readAsDataURL(file.value[0]);
  }
};

const uploadFiles = async () => {
  isCampaignModified.value = true;
  if (file.value.length > 0) {
    filesName.value = file.value[0].name.toLowerCase();
    const reg = /(.*?)\.(jpg|png|jpeg)$/;
    if (!filesName.value.match(reg)) {
      isInvalidImageModalVisible.value = true;
      file.value = null;
      filesName.value = "";
      return;
    }
    const fileSize = file.value[0].size;
    uploadImageSize.value = fileSize;
    const size = parseInt(fileSize.toFixed(0));
    if (size >= 1 * 1024 * 1024) {
      isUploadingImagePopup.value = true;
      uploadImageConfirm.value = file.value;
      file.value = null;
    }
    if (size >= 15 * 1024 * 1024) {
      file.value = null;
      isUploadingImagePopup.value = false;
      isMaxImagePopupVisible.value = true;
      return;
    } else {
      const reader = new FileReader();
      reader.onload = async () => {
        image.value = reader.result;
        if (image.value) {
          loadedImageSize.value = 0;
          uploadImagePercentage.value = 1;
          await uploadImageAsync();
        }
      };
      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  }
};

const displayOption = (item) => {
  dropdownItem.value = item;
  item.dropDown = !item.dropDown;

  if (categoryPromotedRecipes.value) {
    categoryPromotedRecipes.value.forEach((data) => {
      if (item.isin !== data.isin) {
        data.dropDown = false;
      }
    });
  }

  if (recipeDataForCategories.value) {
    recipeDataForCategories.value.forEach((data) => {
      if (item.isin !== data.isin) {
        data.dropDown = false;
      }
    });
  }
};

const addRecipe = () => {
  resetQuery();
  isAddRecipeModal.value = true;
};

const closeModal = () => {
  isSelectDeleteModalVisible.value = false;
  isUnableToPublishArticle.value = false;
  isRemoveCategoryVariantVisible.value = false;
  openPreviewRecipe.value = false;
  isConfirmModalVisible.value = false;
  isAddRecipeModal.value = false;
  isDeleteRecipeModal.value = false;
  isInvalidImageModalVisible.value = false;
  isMaxImagePopupVisible.value = false;
  isSaveModalVisible.value = false;
  isPublishModalVisible.value = false;
  isUnPublishModalVisible.value = false;
  hasRecipeVariantLanguagePopup.value = false;
  isAddVariantCategoryNamePopup.value = false;
  variantName.value = "";
  isUploadingImagePopup.value = false;
};

const backToCategories = () => {
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    backToCategoriesConfirm();
  }
};

const backToCategoriesConfirm = () => {
  isCampaignModified.value = false;
  $eventBus.emit('campaignModified', isCampaignModified.value);
  router.push({ path: "/categories" });
};
const resetQuery = () => {
  queryRecipe.value = "";
  searchRecipeList();
  isSearchExitEnable.value = false;
};

const toggleCheckbox = (event, recipe) => {
  recipe.isSelected = event.target.checked;
  if (!recipe.isSelected) {
    filteredRecipeIsins.value.push(recipe.isin);
  } else {
    const index = filteredRecipeIsins.value.indexOf(recipe.isin);
    if (index !== -1) {
      filteredRecipeIsins.value.splice(index, 1);
    }
  }
};

// Method to promote a recipe
const promoteRecipe = (recipe, index) => {
  recipe.dropDown = false;
  categoryPromotedRecipes.value.push(recipe);
  promotedRecipesIsins.value.push(recipe.isin);
  recipeDataForCategories.value.splice(index, 1);
  searchRecipesCount.value--;
  triggerLoading($keys.KEY_NAMES.RECIPE_PROMOTED);
};

// Method to unpromote a recipe
const unPromoteRecipe = (recipe, index) => {
  recipe.dropDown = false;
  recipeDataForCategories.value.unshift(recipe);
  categoryPromotedRecipes.value.splice(index, 1);
  const recipeIndex = promotedRecipesIsins.value.indexOf(recipe.isin);
  if (recipeIndex !== -1) {
    promotedRecipesIsins.value.splice(recipeIndex, 1);
  }
  if (isSearchExitEnable.value) {
    resetQuery();
  }
  triggerLoading($keys.KEY_NAMES.RECIPE_UNPROMOTED);
};

// Method to edit a recipe
const editRecipe = (isin) => {
  if (isin) {
    rISIN.value = isin;
    openPreviewRecipe.value = true;
  }
};

// Async method to post category recipe
const postCategoryRecipeAsync = async () => {
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: "add",
      isin: newIsin.value,
      targets: selectedCategoryRecipe.value,
    },
  };

  try {
    const response = await store.dispatch("categories/postCategoryRecipeAsync", { payload });
    const operationId = response.opId;
    await checkOperationStatusAsync(operationId);
    await savePromotedRecipe();
  } catch {
    showLoader.value = false;
  }
};

// Async method to check operation status
const checkOperationStatusAsync = async (operationId) => {
  while (true) {
    await getOperationStatusAsync(operationId);
    if (
      operationStatusDetails.value.state === $keys.KEY_NAMES.DONE ||
      operationStatusDetails.value.state === $keys.KEY_NAMES.FAILED
    ) {
      operationStatusDetails.value = "";
      break;
    }
  }
};

// Async method to get operation status
const getOperationStatusAsync = async (operationId) => {
  operationStatusDetails.value = "";
  await store.dispatch("categories/getOperationStatusAsync", { operationId });

  const response = store.getters["categories/getOperationStatus"];
  operationStatusDetails.value = response;
};
const savePromotedRecipe = async () => {
  const payload = {
    isin: newIsin.value,
    campaignType: "categoryRecipeSuggestion",
    promotedRecipeIsins: categoryPromotedRecipes.value
      ? categoryPromotedRecipes.value.map(recipe => recipe.isin)
      : [],
    filteredRecipeIsins: filteredRecipeIsins.value,
    preview: false,
  };

  try {
    await store.dispatch("categories/saveRecipeCampaignDataAsync", { payload, lang: lang.value });
    await patchPublishCategoryAsync();
  } catch {
    showLoader.value = false;
  }
};

// Async save button click handler
const saveButtonClickAsync = async () => {
  isSlugCheckConfirm.value = true;

  if (isSlugStatus.value) {
    isSlugInputWarn.value = true;

    if (categoriesSlug.value.trim() !== "") {
      await callSlugChangeAsync();

      if (hasSlugExist.value) {
        categoriesSlug.value = await generateUniqueSlugAsync(categoriesSlug.value);
      }
      processSlugAsync();
      isSlugCheckConfirm.value = false;
      await saveButtonClickAsync();
    } else {
      resetSlugStatus();
    }
  } else {
    resetSlugStatus();

    if (file.value && productImage.value && categoriesName.value) {
      isCategorySaving.value = true;

      if (newIsin.value !== "") {
        await postCategoryConfirmAsync();
      }
    }

    isCampaignModified.value = false;
    $eventBus.emit("campaignModified", isCampaignModified.value);
  }
};

// Async method to generate unique slug
const generateUniqueSlugAsync = async (slug) => {
  let suffix = 1;

  while (true) {
    const newSlug = `${slug}-${suffix}`;
    await checkSlugAsync(newSlug);

    if (!hasSlugExist.value) {
      return newSlug;
    }

    suffix++;
  }
};

// Async method to get new category ISIN
const getCategoryISINAsync = async () => {
  const payload = {
    user: $auth?.user?.value?.email,
    entity: "recipeGroup"
  };

  try {
    await store.dispatch("isin/getNewISINsAsync", { payload, lang: lang.value });
    const response = store.getters['isin/getISIN'];
    newIsin.value = response?.isin ?? "";
  } catch (e) {
    isCategorySaving.value = false;
    console.error(e);
  }
};

// Async method to set payload with variant
const setPayLoadWithVariantAsync = async (payload) => {
  if (payload?.image[lang.value]) {
    payload.image = await setLanguageVariant(payload.image);
  }
  return payload;
};

// Async method to set payload with variant slug
const setPayLoadWithVariantSlugAsync = async (payload) => {
  if (payload.slug && payload.slug[lang.value]) {
    payload.slug = await setLanguageVariant(payload.slug);
  }
  return payload;
};

// Async method to confirm posting a category
const postCategoryConfirmAsync = async () => {
  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: lang.value,
  };

  recipeVariantList.value.push(defaultVariantData);
  await postCategoryAsync();
};

// Async method to post category
const postCategoryAsync = async () => {
  let payload = {
    isin: newIsin.value,
    slug: {
      [lang.value]: categoriesSlug.value?.trim() || "",
    },
    type: "category",
    data: updatedRecipeVariantList(recipeVariantList.value),
    image: {
      [lang.value]: imageResponseUrl.value
        ? imageResponseUrl.value.replace(/\?.*/, "")
        : productImage.value,
    },
  };

  payload = await setPayLoadWithVariantAsync(payload);
  payload = await setPayLoadWithVariantSlugAsync(payload);

  if (!payload?.slug?.[lang.value]) {
    delete payload.slug;
  }

  try {
    await store.dispatch("categories/postCategoryOrCategoryGroupAsync", {
      payload,
      lang: lang.value,
    });

    if (selectedCategoryRecipe.value.length) {
      await postCategoryRecipeAsync();
    } else {
      await savePromotedRecipe();
    }
  } catch {
    showLoader.value = false;
    isCategorySaving.value = false;
  }
};

// Method to navigate to the categories page
const pageRoute = () => {
  router.push({ path: "/categories" });
};
const moveRecipeUp = (item, index) => {
  categoryPromotedRecipes.value.splice(index, 1);
  categoryPromotedRecipes.value.splice(index - 1, 0, item);
};

// Move recipe down in the list
const moveRecipeDown = (item, index) => {
  categoryPromotedRecipes.value.splice(index, 1);
  categoryPromotedRecipes.value.splice(index + 1, 0, item);
};

// Search recipe list
const searchRecipeList = () => {
  resetSearchState();

  if (queryRecipe.value) {
    filterRecipesByQuery();
    isSearchExitEnable.value = true;
  } else {
    resetRecipeSearchFlags();
    isSearchExitEnable.value = false;
  }
};

// Reset search state
const resetSearchState = () => {
  searchRecipesCount.value = 0;
  queryText.value = queryRecipe.value;
  resetRecipeSearchFlags();
};

// Reset recipe search flags
const resetRecipeSearchFlags = () => {
  recipeDataForCategories.value.forEach((data) => {
    data.isSearched = false;
  });
};

// Filter recipes based on the query
const filterRecipesByQuery = () => {
  const filter = queryRecipe.value.toUpperCase();
  let count = 0;

  recipeDataForCategories.value.forEach((recipe) => {
    const isinValue = getISINValue(recipe);
    const textValue = getTextValue(recipe);

    if (!isinValue.includes(filter) && !textValue.includes(filter)) {
      recipe.isSearched = true;
      count += 1;
    }
  });

  searchRecipesCount.value = recipeDataForCategories.value.length - count;
};

// Get ISIN value from the recipe
const getISINValue = (recipe) => {
  return recipe.isin || "";
};

// Get text value from the recipe
const getTextValue = (recipe) => {
  return (
    recipe.title?.[lang.value]?.toUpperCase() ||
    recipe.title?.[0]?.toUpperCase() ||
    ""
  );
};
const displayPopupAsync = async () => {
  isSlugCheckConfirm.value = true;

  if (categoriesSlug.value.trim() !== "") {
    await processSlugAsync();
    isSlugCheckConfirm.value = false;
  } else {
    resetSlugStatus();
  }

  // Reset dropdowns in recipe data
  recipeDataForCategories.value.forEach((data) => {
    data.dropDown = false;
  });

  // Reset dropdowns in promoted recipes
  categoryPromotedRecipes.value.forEach((data) => {
    data.dropDown = false;
  });

  isSaveModalVisible.value = true;
};

// Unpublish confirmation logic
const unPublishConfirm = () => {
  categoriesStatus.value = "hidden";
  closeModal();
};

// Publish confirmation logic
const publishConfirm = () => {
  categoriesStatus.value = "active";
  closeModal();
};

// Handle input events
const handleTypeInput = (event) => {
  if (categoryNameRef.value && categoryNameRef.value.contains(event.target)) {
    isCampaignModified.value = true;
  }
};

// Handle slug input
const slugInput = () => {
  isCampaignModified.value = true;
  isSlugCheckConfirm.value = true;
};

// Handle click outside to close dropdown
const handleClickOutside = (event) => {
  if (dropdownItem.value && dropdownItem.value.dropDown) {
    if (!document.querySelector(".menu-selected").contains(event.target)) {
      dropdownItem.value.dropDown = false;
    }
  }
};

// Handle click outside popup
const handleClickOutsidePopup = (event) => {
  if (hasRecipeVariantLanguageResult.value) {
    if (!document.querySelector(".category-group-dropdown").contains(event.target)) {
      hasRecipeVariantLanguageResult.value = false;
    }
  }
};
const openRecipeVariantPopUp = () => {
  hasRecipeVariantLanguagePopup.value = true;
  hasRecipeVariantLanguageResult.value = false;
  hasDisabledSelectLanguageButton.value = false;
  recipeVariantLanguage.value = "";
};

// Set recipe variant language matches
const setRecipeVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  recipeVariantLanguageIndex.value = index;
  hasRecipeVariantLanguageResult.value = false;
  hasDisabledSelectLanguageButton.value = true;

  recipeVariantLanguageList.value.forEach((data, idx) => {
    if (data.language === recipeVariantLanguage.value) {
      recipeVariantLanguageList.value.splice(idx, 1);
      recipeVariantLanguageList.value.unshift(data);
    }
  });
};

// Proceed to the next category variant name modal popup
const nextCategoryVariantNameModalPopUp = (item) => {
  if (item === "") {
    recipeVariantSelectedLanguage.value = recipeVariantLanguageList.value[0].language;
    recipeVariantLanguage.value = recipeVariantLanguageList.value[0].language;
  } else {
    recipeVariantSelectedLanguage.value = item;
  }
  hasRecipeVariantLanguagePopup.value = false;
  isAddVariantCategoryNamePopup.value = true;
  hasRecipeVariantLanguageResult.value = false;
};

// Show recipe variant language matches
const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};

// Go back to select language variant popup
const backToSelectLanguageVariantPopUp = () => {
  isAddVariantCategoryNamePopup.value = false;
  hasRecipeVariantLanguagePopup.value = true;
};
const addRecipeVariant = (item) => {
  variantName.value = item;
  if (variantName.value !== "") {
    let newVariantData = {
      name: item.trim(),
      lang: recipeVariantLanguage.value,
    };
    recipeVariantList.value.push(newVariantData);
    isCampaignModified.value = true;
    isAddVariantCategoryNamePopup.value = false;
    variantName.value = "";

    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(data => data.language !== recipeVariantLanguage.value);
  }
};

// Prepare to delete category variant
const deleteCategoryVariant = (data, index) => {
  isRemoveCategoryVariantVisible.value = true;
  categoryVariantDataIndex.value = index;
  deletedVariant.value = data.lang;
};

// Remove the selected category variant
const removeCategoryVariant = () => {
  isCampaignModified.value = true;
  recipeVariantList.value.splice(categoryVariantDataIndex.value, 1);
  let langData = {};

  if (deletedVariant.value === "es-US") {
    langData = {
      language: deletedVariant.value,
      language_name: "Spanish",
      language_flag: "/images/flags/spain-flag.png",
    };
  } else if (deletedVariant.value === "fr-FR") {
    langData = {
      language: deletedVariant.value,
      language_name: "French",
      language_flag: "/images/flags/france-flag.png",
    };
  }

  recipeVariantLanguageList.value.push(langData);
  closeModal();
};

// Display language code
const displayLanguageCode = (item) => {
  if (item) {
    const arr = item.split("-");
    return arr[0].toUpperCase();
  }
  return "";
};

// Handle input content change
const inputContentChanged = (index) => {
  isCampaignModified.value = true;
};

// Update recipe variant list
const updatedRecipeVariantList = (variantList) => {
  finalSelectedLanguage.value = [];

  if (variantList && variantList.length > 0) {
    variantList.forEach(item => {
      if (item && item.name !== "" && item.lang !== "") {
        item[item.lang] = {
          name: item.name ? item.name : "",
        };
      }
    });
  }

  const updatedVariantList = Object.assign({}, ...variantList);
  delete updatedVariantList.lang;
  delete updatedVariantList.name;

  finalSelectedLanguage.value = Object.keys(updatedVariantList);
  return updatedVariantList;
};

// Set language variant
const setLanguageVariant = (variantList) => {
  const copyObjectData = [];

  if (finalSelectedLanguage.value.length > 0 && variantList && variantList[lang.value]) {
    finalSelectedLanguage.value.forEach(item => {
      const object = {
        [item]: variantList[lang.value],
      };
      copyObjectData.push(object);
    });
  }

  return Object.assign({}, ...copyObjectData);
};
onBeforeUnmount(() => {
  document.removeEventListener("scroll", checkScrollPosition);
  document.removeEventListener("input", handleTypeInput);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("click", handleClickOutsidePopup);
});

onEscapeKeyPress(closeModal);

watchEffect(() => {
  if (isCampaignModified.value) {
    $eventBus.emit('campaignModified', isCampaignModified.value);
    checkVariantNameExists();
  }
});

</script>
