import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useTableManagerStore = (
  {
    storeId,
    clientKey,
    endpointKey,
    fetchFn,
    defaultPagination,
    resultModel,
  }
) => defineStore(`Table-Manager-${storeId}`, () => {
  const rowsRef = ref([]);
  const isLoadingRef = ref(true);
  const paginationRef = ref(defaultPagination);
  const isFirstLoadCompletedRef = ref(false);

  const rows = computed(() => rowsRef);
  const isLoading = computed(() => isLoadingRef);
  const pagination = computed(() => paginationRef);
  const isFirstLoadCompleted = computed(() => isFirstLoadCompletedRef);

  const setRows = (value) => {
    rowsRef.value = value || [];
  };

  const setLoading = (value) => {
    isLoadingRef.value = value;
  };

  const setFirstLoad = (value) => {
    isFirstLoadCompletedRef.value = value;
  };

  const setPaginationFrom = (value) => {
    if (value !== undefined && value !== null) {
      paginationRef.value.from = value;
    }
  };

  const setPaginationTotal = (value) => {
    if (value !== undefined && value !== null) {
      paginationRef.value.total = value;
    }
  };

  const fetch = async ({ params = {}, isSmoothUpdate = false }) => {
    if (!isSmoothUpdate) {
      setLoading(true);
    }

    setPaginationFrom(params.from);

    if (fetchFn && typeof fetchFn === "function") {
      try {
        const response = await fetchFn(params, clientKey, endpointKey);
        setLoading(false);
        setFirstLoad(true);
        return response;
      } catch (e) {
        console.error(`Table Manager, cannot fetch data by fetchFn!`, e);
        setLoading(false);
        setFirstLoad(true);
        return e;
      }
    }

    try {
      const response = await useSimpleCustomFetch("", { params }, clientKey, endpointKey);
      if (response) {
        const result = _applyResultModel(response.results, resultModel);
        setRows(result);
        setPaginationTotal(response.total);
      }
    } catch (e) {
      console.error(`Table Manager, cannot fetch data: clientKey: ${clientKey}, endpointKey: ${endpointKey}.`, e);
    } finally {
      setLoading(false);
      setFirstLoad(true);
    }
  };

  const reset = () => {
    setRows([]);
    setLoading(true);
    setPaginationTotal(0);
    setFirstLoad(false);
  };

  function _applyResultModel(data, model) {
    try {
      if (typeof model !== "function") {
        return data;
      }

      if (model.prototype?.constructor) {
        return data.map((item) => new model(item));
      }

      return data.map((item) => model(item));
    } catch (e) {
      console.error("Table Manager, cannot apply the Result Model!", e);
      return data;
    }
  }

  return {
    tms_rows: rows,
    tms_isLoading: isLoading,
    tms_pagination: pagination,
    tms_isFirstLoadCompleted: isFirstLoadCompleted,

    tms_fetch: fetch,
    tms_setRows: setRows,
    tms_setLoading: setLoading,
    tms_setPaginationTotal: setPaginationTotal,
    tms_setPaginationFrom: setPaginationFrom,
    tms_reset: reset,
  };
})();
