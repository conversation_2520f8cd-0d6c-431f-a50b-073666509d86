import { computed, ref } from "vue";
import URLFormatter from "@/services/URLFormatter";

export const useConfigStore = defineStore("Config", () => {
  const configRef = ref(null);
  const sidebarNavsRef = ref(null);
  const isLoadingRef = ref(true);

  const config = computed(() => configRef.value);
  const sidebarNavs = computed(() => sidebarNavsRef.value);

  const setConfigAsync = async (conf, navs) => {
    configRef.value = conf || null;
    sidebarNavsRef.value = navs || null;
    isLoadingRef.value = false;
  };

  const getConfigEndpointHost = (clientKey, endpointKey) => {
    const client = configRef.value?.clients[clientKey];
    return {
      endpoint: URLFormatter.urlFormat(client?.endpoints?.[endpointKey]),
      baseURL: client?.host,
    };
  };

  return {
    configRef,
    sidebarNavsRef,

    config,
    sidebarNavs,

    setConfigAsync,
    getConfigEndpointHost,
  };
})
