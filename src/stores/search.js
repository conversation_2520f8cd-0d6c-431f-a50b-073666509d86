export const useSearchStore = defineStore("Search", () => {
  const searchQueryRef = ref({
    str: "",
    emitQueryParam: true,
  });
  const isSearchEnabledRef = ref(false);

  const searchQuery = computed(() => searchQueryRef);
  const isSearchEnabled = computed(() => isSearchEnabledRef);

  const setSearchQuery = (value, { emitQueryParam = true } = {}) => {
    const trimmed = value?.trim() ?? '';
    searchQueryRef.value = {
      str: trimmed,
      emitQueryParam,
    };

    isSearchEnabledRef.value = !!trimmed;
  };

  return {
    searchQuery,
    isSearchEnabled,
    setSearchQuery,
  };
});
