export default defineNuxtPlugin(() => {
  if (process.client) {
    const loadScript = (src, id) => {
      return new Promise((resolve, reject) => {
        if (document.getElementById(id)) {
          resolve();
          return;
        }
        const script = document.createElement("script");
        script.src = src;
        script.id = id;
        script.onload = resolve;
        script.onerror = reject;
        document.body.appendChild(script);
      });
    };

    const ensureDependenciesLoaded = async () => {
      if (!window.jQuery) {
        await loadScript("https://code.jquery.com/jquery-3.6.0.min.js", "jquery_script");
      }

      if (!jQuery.fn.ZammadForm) {
        await loadScript("https://support2.innit.com/assets/form/form.js", "zammad_form_script");
        await new Promise((resolve, reject) => {
          const interval = setInterval(() => {
            if (jQuery.fn.ZammadForm) {
              clearInterval(interval);
              resolve();
            }
          }, 100);
          setTimeout(() => {
            clearInterval(interval);
            reject(new Error("ZammadForm failed to load"));
          }, 5000);
        });
      }
    };

    window.zE = () => {};

    window.InnitZammadForm = async function() {
      try {
        await ensureDependenciesLoaded();
        const buttonId = "zammad-feedback-form";
        let button = document.getElementById(buttonId);
        if (!button) {
          button = document.createElement("button");
          button.id = buttonId;
          button.style.display = "none";
          document.body.appendChild(button);
        }
        jQuery(`#${buttonId}`).ZammadForm({
          messageTitle: "Feedback Form",
          messageSubmit: "Submit",
          messageThankYou: "Thank you for your inquiry (#%s)! We'll contact you as soon as possible.",
          modal: true,
          attachmentSupport: true,
        });
        button.click();
      } catch (error) {
        console.error("Failed to load dependencies:", error);
      }
    };
  }
});
